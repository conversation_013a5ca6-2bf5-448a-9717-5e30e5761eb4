{
  "git.autofetch": true,
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "prettier.printWidth": 100,
  "editor.defaultFormatter": "esbenp.prettier-vscode", //自动格式化程序为prettier
  "javascript.updateImportsOnFileMove.enabled": "always", //js重命名文件自动更新路径
  // #让vue中的js按"prettier"格式进行格式化
  "vetur.format.defaultFormatter.html": "prettier",
  "vetur.format.defaultFormatter.js": "prettier-eslint", //主要是这一行解决冲突
  "vetur.format.defaultFormatterOptions": {
    // #vue组件中html代码格式化样式
    // "js-beautify-html": {},
    // "prettier-eslint": {},
    // "prettyhtml": {}
  },
  "editor.suggestSelection": "first", //tab优先选第一个建议
  "editor.formatOnPaste": true, //自动格式化粘贴内容
  "editor.formatOnType": true,
  "editor.formatOnSave": true,
  "window.zoomLevel": 0 //键入一行后自动格式
}
