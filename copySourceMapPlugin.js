const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class CopySourceMapPlugin {
  apply(compiler) {
    compiler.hooks.afterEmit.tapAsync('CopySourceMapPlugin', (compilation, callback) => {
      const outputPath = compilation.outputOptions.path;
      const sourceMapFolder = path.join(outputPath, 'sourceMap');

      // 创建目标文件夹
      if (!fs.existsSync(sourceMapFolder)) {
        fs.mkdirSync(sourceMapFolder);
      }

      // 查找并复制非 sourceMap 文件夹下的 .map 和 .map.gz 文件到目标文件夹
      const copyFiles = folder => {
        const files = fs.readdirSync(folder);
        files.forEach(file => {
          const filePath = path.join(folder, file);
          const isDirectory = fs.lstatSync(filePath).isDirectory();
          if (isDirectory) {
            copyFiles(filePath);
          } else if (
            (file.endsWith('.map') || file.endsWith('.map.gz')) &&
            !filePath.includes(`sourceMap${path.sep}`)
          ) {
            const targetFile = path.join(sourceMapFolder, file);
            file.endsWith('.map') && fs.copyFileSync(filePath, targetFile);
            fs.unlinkSync(filePath);
          }
        });
      };

      copyFiles(outputPath);

      // 压缩 sourceMap 文件夹
      const zipFileName = 'sourceMap.zip';
      const zipFilePath = path.join(outputPath, zipFileName);
      const output = fs.createWriteStream(zipFilePath);
      const archive = archiver('zip');

      output.on('close', () => {
        // 删除 sourceMap 文件夹
        fs.rmdirSync(sourceMapFolder, { recursive: true });
        callback();
      });

      output.on('error', err => {
        console.error(`压缩文件 ${zipFileName} 失败:`, err);
        callback();
      });

      archive.pipe(output);
      archive.directory(sourceMapFolder, 'sourceMap');
      archive.finalize();
    });
  }
}

module.exports = CopySourceMapPlugin;
