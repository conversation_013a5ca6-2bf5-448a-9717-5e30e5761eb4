import util from '../libs/util';

const formDesign = () =>
  import('@oa/sw-workflow/src/workflow/src/Conformance/cloudForm/form/form_design');
const bpmnModel = () =>
  import('@oa/sw-workflow/src/workflow/src/Conformance/workflow/process/bpmnModel');
const processForm = () =>
  import('@oa/sw-workflow/src/workflow/src/Conformance/workflow/form/isScreenFrom');
const appH5 = () => import('@oa/sw-workflow/src/workflow/app/appH5');
// const xybH5 = () => import("@oa/sw-workflow/src/workflow/app/xybH5");
// const Sigin = () =>
//   import(
//     /* webpackChunkName:"sigin"*/ ""
//   );
import Sigin from '@oa/sw-workflow/src/workflow/app/sigin';
const docEditOnline = () =>
  import(/* webpackChunkName:"docEditOnline"*/ '../views/wps/docEditOnline');
const docPreviewOnline = () => import('../views/wps/docPreviewOnline');
const newsDetail = () => import('../views/hnjcoa/newsManage/newsDetail');
const noticePhoneDetail = () => import('../views/hnjcoa/notice/noticePhoneDetail');
const SplitScreen = () => import('../views/hnjcoa/components/splitScreen');

const login = () => import('../views/biyi-admin/src/system-page/login/login.vue');
const updatePaswd = () => import('../views/biyi-admin/src/components/update-pswd.vue');
const OtherLogin = () => import('../views/biyi-admin/src/system-page/login/OtherLogin.vue');
const NationalLogin = () => import('../views/biyi-admin/src/system-page/login/NationalLogin.vue');

/**
 * 配置静态路由
 * 分为两种路由：
 * 1.路由出口在App.vue
 * 2.路由出口在main.vue
 * 注意：为避免不必要的错误，每个路由需设置name属性！！！
 * 注意：为避免不必要的错误，每个路由需设置name属性！！！
 * 注意：为避免不必要的错误，每个路由需设置name属性！！！
 */

// 1.路由出口在App.vue
const singleRoutes = [
  {
    // 设置初始页面
    path: '',
    redirect: '/home'
  },
  // 金山wps在线预览
  {
    path: '/docPreviewOnline',
    name: 'docPreviewOnline',
    component: docPreviewOnline,
    meta: {
      title: '文档在线预览'
    }
  },

  // 金山wps在线编辑
  {
    path: '/docEditOnline',
    name: 'docEditOnline',
    component: docEditOnline,
    meta: {
      title: '文档在线编辑'
    }
  },
  {
    path: '/splitScreen',
    name: 'splitScreen',
    component: SplitScreen,
    meta: {
      title: '分屏预览'
    }
  },
  // 新闻详情
  {
    path: '/news/newsDetail',
    name: 'newsDetail',
    component: newsDetail,
    meta: {
      title: '新闻详情'
    }
  },
  // 新闻详情
  {
    path: '/notice/noticePhoneDetail',
    name: 'noticePhoneDetail',
    component: noticePhoneDetail,
    meta: {
      title: '通知公告详情'
    }
  },
  {
    path: '/other-login',
    name: 'otherLogin',
    component: OtherLogin,
    meta: {}
  },
  {
    path: '/national-login',
    name: 'nationalLogin',
    component: NationalLogin,
    meta: {}
  },
  {
    path: '/login',
    name: 'login',
    component: login,
    meta: {
      // 密码规则校验开关
      validatePaswd: true
    }
  },
  {
    path: '/tokenInvalid',
    name: 'tokenInvalid',
    component: () => import('../views/biyi-admin/src/system-page/login/tokenInvalid.vue')
  },
  {
    path: '/update-password',
    name: 'update-password',
    component: updatePaswd
  },
  // {
  //   path: '/404',
  //   name: '404',
  //   component: () => import('../views/error-pages/404.vue')
  // },
  {
    path: '/flow/bpmn',
    name: 'defined-flow-button',
    title: '流程设计器',
    component: bpmnModel
  },
  {
    path: '/form-design',
    name: 'form-design',
    title: '表单设计器',
    component: formDesign
  },
  {
    path: '/formrender',
    title: '渲染表单',
    name: 'form-render',
    component: processForm
  },
  {
    path: '/appH5',
    name: 'appH5',
    component: appH5
  },
  {
    path: '/sigin',
    name: 'sigin',
    component: Sigin
  },
  {
    path: '/pdf-sign',
    name: 'pdfSign',
    component: () => import('../views/wps/pdfSign.vue')
  },
  {
    path: '/ofd-page',
    name: 'ofdPage',
    component: () => import('../views/ofd/index.vue')
  }
];

/**
 * 路由出口在main.vue，包括了菜单项和非菜单页面
 * 若该页面属于菜单页面，需要添加type: 'menu'属性
 */
const staticTree = [
  {
    path: '/main',
    name: 'main-home',
    title: '首页',
    icon: 'md-home',
    type: 'menu',
    component: () => import('../views/main/main.vue'),
    children: [
      {
        path: '/home',
        name: 'home',
        title: '首页',
        icon: 'md-home',
        type: 'menu',
        component: () => import('../views/huaihua/home/<USER>') // 屏蔽首页
      },
      {
        path: '/leaderInstruction',
        name: 'leaderInstruction',
        title: '领导批示',
        component: () => import('@/views/yueyang/leaderInstruction/index.vue')
      },
      {
        path: '/self-edit',
        name: 'self-edit',
        title: '个人中心',
        component: () => import('@/views/biyi-admin/src/system-page/own-space/self-edit.vue')
      },
      {
        path: '/document-reception',
        name: 'document-reception',
        title: '公文接收',
        component: () => import('@/views/hnjcoa/document-reception/document-reception.vue')
      },
      {
        path: '/document-reception/document-reception-detail',
        name: 'document-reception-detail',
        title: '公文详情',
        component: () => import('@/views/hnjcoa/document-reception/document-reception-detail.vue')
      },
      {
        path: '/404',
        name: '404',
        title: '404',
        component: () => import('../views/error-pages/404.vue')
      }
    ]
  }
];
const staticMenuList = util.getMenuList(staticTree);
const staticMainRoutes = util.getMenuRoutes(staticTree);

// 汇总所有的路由
const staticRoutes = [...staticMainRoutes, ...singleRoutes];

export { staticRoutes, staticMenuList, staticTree };
