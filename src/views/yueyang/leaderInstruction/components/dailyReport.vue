<template>
  <div class="daily-report-manage">
    <Form class="header" :model="searchForm" inline :label-width="70">
      <FormItem label="标题" :label-width="40">
        <Input clearable v-model="searchForm.title" placeholder="请输入标题"></Input>
      </FormItem>
      <FormItem label="领导名称">
        <Input clearable v-model="searchForm.readUsername" placeholder="请输入领导名称"></Input>
      </FormItem>
      <FormItem label="拟稿人" :label-width="60">
        <Input clearable v-model="searchForm.createName" placeholder="请输入拟稿人"></Input>
      </FormItem>
      <FormItem label="拟稿部门">
        <Input clearable v-model="searchForm.departmentName" placeholder="请输入拟稿部门"></Input>
      </FormItem>
      <FormItem label="拟稿时间">
        <DatePicker
          transfer
          placeholder="请选择"
          v-model="searchDate"
          @on-change="dateChange"
          type="daterange"
          style="width: 200px"
        ></DatePicker>
      </FormItem>
      <FormItem :label-width="0">
        <Button @click="currentPageChange(1)" oaType="oaBlue" class="mr10">搜索</Button>
        <Button @click="resetSearchForm" oaType="white">重置</Button>
      </FormItem>
    </Form>
    <div class="main-table">
      <Table border stripe :columns="cols" :data="dataList">
        <template #action="{ row }">
          <Button @click="checkDailyReport(row)" oaType="oaEdit" style="margin-right: 5px"
            >详情</Button
          >
          <Button @click="checkReadStatusDetail(row)" oaType="oaEdit" style="margin-right: 5px"
            >查看分发</Button
          >
        </template>
      </Table>
    </div>
    <div v-show="total > 10" class="footer">
      <Page
        @on-change="currentPageChange"
        :total="total"
        :current="searchForm.currentPage"
        :page-size="searchForm.pageSize"
        transfer
        show-total
        show-elevator
      />
    </div>
    <Modal
      :title="'查看详情'"
      footer-hide
      :width="800"
      class-name="daily-report-detail-modal"
      v-model="dailyReportDetailModal"
    >
      <table class="main-table">
        <tr>
          <td class="red-text" colspan="1">标题：</td>
          <td colspan="3">
            {{ dailyReportDetail.title }}
          </td>
        </tr>
        <tr>
          <td class="red-text" colspan="1">类型：</td>
          <td colspan="3">
            {{ dailyReportDetail.reportTypeName }}
          </td>
        </tr>
        <tr>
          <td class="red-text" colspan="1">拟稿单位：</td>
          <td colspan="1">{{ dailyReportDetail.departmentName }}</td>
          <td class="red-text" colspan="1">拟稿人：</td>
          <td colspan="1">{{ dailyReportDetail.createName }}</td>
        </tr>
        <tr>
          <td class="red-text" colspan="1">公文正文：</td>
          <td colspan="3">
            <Button @click="checkFile(dailyReportDetail.fileMsg)" oaType="oaBlue" size="small">
              查看正文
            </Button>
          </td>
        </tr>
        <tr class="select-user">
          <td class="red-text" colspan="1">分阅用户：</td>
          <td colspan="3">
            <div class="user-main">
              {{ dailyReportDetail.readUsernames
              }}{{
                dailyReportDetail.readCompanynames ? `,${dailyReportDetail.readCompanynames}` : ''
              }}
            </div>
          </td>
        </tr>
      </table>
    </Modal>
    <Modal
      :title="'分发详情'"
      :width="1200"
      class-name="read-status-detail-modal"
      v-model="readStatusDetailModal"
    >
      <Tabs v-model="readStatusTabsValue" @on-click="clickTab" name="tab1-1">
        <TabPane label="查阅人" name="personal" tab="tab1-1">
          <div class="table-main">
            <Form :model="personalDetailForm" inline label-colon :label-width="80">
              <FormItem label="查阅状态">
                <Select v-model="personalDetailForm.readStatus" style="width: 150px">
                  <Option value="">全部</Option>
                  <Option value="0">未查阅</Option>
                  <Option value="1">已查阅</Option>
                </Select>
              </FormItem>
              <FormItem :label-width="0">
                <Button @click="personalPageChange(1)" oaType="oaBlue">查询</Button>
              </FormItem>
            </Form>
            <Table
              @on-selection-change="personalSelectionChange"
              :data="personalDetailData"
              :columns="personalDetailCols"
              :max-height="390"
              border
              stripe
            >
              <template #action="{ row, index }">
                <div v-if="row.isSign == 1">
                  <Button @click="checkSign(row)" oaType="oaEdit" style="margin-right: 5px"
                    >查看</Button
                  >
                  <!--                  <Button-->
                  <!--                    @click="downFIle(row)"-->
                  <!--                    oaType="oaEdit"-->
                  <!--                    style="margin-right: 5px"-->
                  <!--                    >下载</Button-->
                  <!--                  >-->
                </div>
                <p v-else>--</p>
              </template>
            </Table>
            <div class="footer">
              <Page
                @on-change="personalPageChange"
                :total="personalDetailTotal"
                :current="personalDetailForm.currentPage"
                :page-size="personalDetailForm.pageSize"
                transfer
                show-total
                show-elevator
              />
            </div>
          </div>
        </TabPane>
        <TabPane label="查阅单位" name="unit" tab="tab1-1">
          <div class="table-main">
            <Form :model="unitDetailForm" inline label-colon :label-width="80">
              <FormItem label="查阅状态">
                <Select v-model="unitDetailForm.readStatus" style="width: 150px">
                  <Option value="">全部</Option>
                  <Option value="0">未查阅</Option>
                  <Option value="1">已查阅</Option>
                </Select>
              </FormItem>
              <FormItem :label-width="0">
                <Button @click="unitPageChange(1)" oaType="oaBlue">查询</Button>
              </FormItem>
            </Form>
            <Table
              @on-selection-change="unitSelectionChange"
              :data="unitDetailData"
              :columns="unitDetailCols"
              :max-height="390"
              border
              stripe
            />
            <div class="footer">
              <Page
                @on-change="unitPageChange"
                :total="unitDetailTotal"
                :current="unitDetailForm.currentPage"
                :page-size="unitDetailForm.pageSize"
                transfer
                show-total
                show-elevator
              />
            </div>
          </div>
        </TabPane>
      </Tabs>
      <!-- <div class="footer-btn-box" slot="footer">
        <Button @click="sendSms" oaType="oaBlue">短信提醒</Button>
        <Button @click="stopReport" oaType="oaBlue">终止</Button>
        <Button @click="cancelReport" oaType="oaBlue">撤回</Button>
      </div> -->
    </Modal>
    <!--群组成员选择-->
    <selectGroup ref="selectGroup" @getCheckList="getCheckList" />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { previewWpsOnline } from '@/libs/wps/wpsInvoke';
import { deepCopy } from 'view-design/src/utils/assist';
import selectGroup from '@/views/yueyang/dailyReport/components/selectGroup.vue';
import { downPDFSign, loadPDFJs } from '@/libs/goodSigin/pdfSign';
import { exportFile } from '@/utils/demoWeb';
import log from '@/api/log';
export default {
  name: 'dailyReport',
  components: { selectGroup },
  data() {
    return {
      dailyReportDetailModal: false,
      readStatusTabsValue: 'personal',
      readStatusDetailModal: false,
      reissueModal: false,
      reissueForm: {
        id: 0,
        readUserList: [],
        readCompanyids: '',
        readCompanynames: '',
        readUserids: '',
        readUsernames: '',
        smsReminder: ''
      },
      searchDate: [],
      searchForm: {
        title: '',
        readUsername: '',
        createName: '',
        departmentName: '',
        startTime: '',
        endTime: '',
        pageSize: 10,
        currentPage: 1
      },
      total: 0,
      cols: [
        {
          title: '序号',
          // type: 'index',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h(
              'span',
              params.index + (this.searchForm.currentPage - 1) * this.searchForm.pageSize + 1
            );
          }
        },
        {
          title: '标题',
          key: 'title',
          minWidth: 200,
          align: 'center',
          tooltip: true,
          render: (h, params) => {
            return h('span', {}, this.removeHTMLTag(params.row.title));
          }
        },
        {
          title: '类型',
          key: 'reportTypeName',
          minWidth: 200,
          align: 'center'
          // render: (h, params) => {
          //   return h('span', {}, this.getReportName(params.row.reportType));
          // }
        },
        {
          title: '领导批示',
          key: 'readUsernameList',
          align: 'center',
          minWidth: 300,
          resizable: true,
          render: (h, params) => {
            let leaderList =
              params.row.readUsernameList !== null ? params.row.readUsernameList : [];
            const list = leaderList.map(item => {
              return <Tag color="blue">{item}</Tag>;
            });
            const renderContent = list.length > 0 ? list : '暂无批示';
            return h('div', {}, renderContent);
          }
        },
        {
          title: '拟稿人',
          key: 'createName',
          minWidth: 100,
          align: 'center'
        },
        {
          title: '拟稿部门',
          key: 'departmentName',
          minWidth: 150,
          align: 'center'
        },
        {
          title: '拟稿时间',
          key: 'createTime',
          minWidth: 170,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          minWidth: 140,
          align: 'center'
        }
      ],
      dataList: [],
      dailyReportDetail: {},
      personalDetailForm: {
        staffOrCompany: 0,
        dailyReportId: '',
        readStatus: '',
        currentPage: 1,
        pageSize: 10
      },
      personalDetailCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            let index =
              this.readStatusTabsValue === 'unit'
                ? params.index +
                  (this.unitDetailForm.currentPage - 1) * this.unitDetailForm.pageSize +
                  1
                : params.index +
                  (this.personalDetailForm.currentPage - 1) * this.personalDetailForm.pageSize +
                  1;
            return h('span', index);
          }
        },

        {
          title: '查阅状态',
          minWidth: 100,
          key: 'readStatus',
          align: 'center',
          render: (h, params) => {
            return h('span', [h('span', params.row.readStatus === '1' ? '已查阅' : '未查阅')]);
          }
        },
        {
          title: '类型',
          key: 'reportTypeName',
          minWidth: 200,
          align: 'center'
          // render: (h, params) => {
          //   // const reportType = params.row.reportType === '1' ? '涉岳舆情' : '每日汇报';
          //   return h('span', {}, this.getReportName(params.row.reportType));
          // }
        },
        {
          title: '查阅人',
          key: 'readUsername',
          minWidth: 100,
          align: 'center'
        },
        {
          title: '单位名称',
          key: 'readCompany',
          minWidth: 100,
          align: 'center'
        },
        {
          title: '部门名称',
          key: 'departmentName',
          minWidth: 150,
          align: 'center'
        },
        {
          title: '查阅时间',
          key: 'readTime',
          minWidth: 170,
          align: 'center',
          render: (h, params) => {
            return h('span', [h('span', params.row.readTime || '--')]);
          }
        },
        {
          title: '分发时间',
          key: 'createTime',
          minWidth: 170,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          minWidth: 100,
          align: 'center'
        }
      ],
      personalDetailData: [],
      personalDetailTotal: 0,
      personalSelection: [],
      unitDetailForm: {
        staffOrCompany: 1,
        dailyReportId: '',
        readStatus: '',
        currentPage: 1,
        pageSize: 10
      },
      unitDetailCols: [],
      unitDetailData: [],
      unitDetailTotal: 0,
      unitSelection: [],
      userObj: [],
      unitObj: [],
      userAndUnitList: {},
      typeList: []
    };
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    })
  },
  created() {
    loadPDFJs();
    this.unitDetailCols = deepCopy(this.personalDetailCols);
    this.unitDetailCols.splice(-1, 1);
    this.getGroupList();
    this.getDataList();
    this.getReportType();
  },
  methods: {
    getReportType() {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'reportType' }).then(res => {
        if (res.data.resultCode == 20000) {
          this.typeList = res.data.resultData || [];
        }
      });
    },
    // 点击查阅详情
    async checkReadStatusDetail(item) {
      this.dailyReportDetail = deepCopy(item);
      // 初始化数据再去请求，在展示弹框
      this.readStatusTabsValue = 'personal';
      this.personalDetailForm = {
        staffOrCompany: 0,
        dailyReportId: item.dailyReportId,
        readStatus: '',
        currentPage: 1,
        pageSize: 10
      };
      this.unitDetailForm = {
        staffOrCompany: 1,
        dailyReportId: item.dailyReportId,
        readStatus: '',
        currentPage: 1,
        pageSize: 10
      };
      this.personalDetailTotal = 0;
      this.unitDetailTotal = 0;
      this.personalSelection = [];
      this.unitSelection = [];
      this.personalDetailData = [];
      this.unitDetailData = [];
      this.getPersonalOrUnitData();
      this.$nextTick(() => {
        this.readStatusDetailModal = true;
      });
    },
    // todo催发短信
    sendSms() {
      let selection =
        this.readStatusTabsValue === 'unit' ? this.unitSelection : this.personalSelection;
      if (selection.length === 0) {
        this.$Message.warning('当前没有选择需要催办的每日汇报！');
        return;
      }
      const reqForm = {
        id: this.dailyReportDetail.id,
        phones: selection.map(item => {
          return item.phone;
        })
      };
      const msg = this.$Message.loading({
        content: '正在进行短信催办',
        duration: 0
      });
      this.$api.dailyReport.messageUrge(reqForm).then(res => {
        msg();
        if (res && res.data.resultCode === 20000) {
          this.$Message.success('催办成功！');
          selection = [];
          this.getPersonalOrUnitData();
        } else {
          this.$nextTick(() => {
            this.$Message.error('操作失败，请重试');
          });
        }
      });
      console.log(reqForm);
    },
    // todo中止
    stopReport() {
      this.$Modal.confirm({
        title: '操作提示',
        content: `确定中止 ${this.dailyReportDetail.title} 吗 ？`,
        onOk: () => {
          const msg = this.$Message.loading({
            content: '正在终止',
            duration: 0
          });
          this.$api.dailyReport.stopReport(this.dailyReportDetail.id).then(res => {
            msg();
            if (res && res.data.resultCode === 20000) {
              this.getDataList();
              this.$Message.success('终止成功！');
              this.personalDetailForm = {
                staffOrCompany: 0,
                dailyReportId: this.dailyReportDetail.id,
                readStatus: '',
                currentPage: 1,
                pageSize: 10
              };
              this.unitDetailForm = {
                staffOrCompany: 1,
                dailyReportId: this.dailyReportDetail.id,
                readStatus: '',
                currentPage: 1,
                pageSize: 10
              };
              this.personalDetailTotal = 0;
              this.unitDetailTotal = 0;
              this.personalSelection = [];
              this.unitSelection = [];
              this.personalDetailData = [];
              this.unitDetailData = [];
              this.getDataList();
            }
          });
        }
      });
    },
    // todo撤回
    cancelReport() {
      let selection =
        this.readStatusTabsValue === 'unit' ? this.unitSelection : this.personalSelection;
      if (selection.length === 0) {
        this.$Message.warning('当前没有需要撤回的每日汇报！');
        return;
      }
      const ids = [];
      for (let i in selection) {
        ids.push(selection[i].id);
      }
      this.$Modal.confirm({
        title: '操作提示',
        content: `确定撤回 ${this.dailyReportDetail.title} 吗 ？`,
        onOk: () => {
          const msg = this.$Message.loading({
            content: '正在撤回',
            duration: 0
          });
          this.$api.dailyReport.cancelReportSub(ids).then(res => {
            msg();
            if (res && res.data.resultCode === 20000) {
              this.getDataList();
              this.$Message.success('撤回成功！');
              selection = [];
              this.getPersonalOrUnitData();
              this.getDataList();
            } else {
              this.$nextTick(() => {
                this.$Message.error('撤回失败，请重试');
              });
            }
          });
        }
      });
    },
    // 查阅详情--点击tab
    clickTab(name) {
      this.readStatusTabsValue = name;
      if (
        (name === 'personal' && this.personalDetailTotal === 0) ||
        (name === 'unit' && this.unitDetailTotal === 0)
      ) {
        this.getPersonalOrUnitData();
      }
    },
    // 查阅详情--查阅人选择
    personalSelectionChange(selection) {
      this.personalSelection = selection;
    },
    // 查阅详情--查阅人分页
    personalPageChange(pageNo) {
      this.personalDetailForm.currentPage = pageNo;
      this.getPersonalOrUnitData();
    },
    // 查阅详情--查阅单位选择
    unitSelectionChange(selection) {
      this.unitSelection = selection;
    },
    // 查阅详情--查阅单位分页
    unitPageChange(pageNo) {
      this.unitDetailForm.currentPage = pageNo;
      this.getPersonalOrUnitData();
    },
    // 获取查阅人或查阅单位数据
    getPersonalOrUnitData() {
      let reqForm = {};
      if (this.readStatusTabsValue === 'unit') {
        reqForm = deepCopy(this.unitDetailForm);
        this.unitSelection = [];
      } else {
        reqForm = deepCopy(this.personalDetailForm);
        this.personalSelection = [];
      }
      this.$api.dailyReport.queryBizDailyReportSubNoUserPage(reqForm).then(res => {
        let dataList = [];
        let total = 0;
        if (res.data.resultCode === 20000) {
          dataList = res.data.resultData.data;
          total = res.data.resultData.recordsTotal;
        }
        if (this.readStatusTabsValue === 'unit') {
          this.unitDetailData = dataList;
          this.unitDetailTotal = total;
        } else {
          this.personalDetailData = dataList;
          this.personalDetailTotal = total;
        }
      });
    },
    // 查看正文
    checkFile(fileMsg) {
      previewWpsOnline(fileMsg);
    },
    // 查看psf签批
    checkSign(row) {
      const { href } = this.$router.resolve({
        name: 'pdfSign',
        query: { signId: row.signId }
      });
      window.open(href, '_blank');
    },
    // 下载正文
    async downFIle(row) {
      const data = await downPDFSign(row.signId);
      if (!data.fileUrl) return;
      exportFile(data.fileUrl, data.fileName + '.pdf', false);
    },
    //查询详情
    async checkDailyReport(item) {
      const msg = this.$Message.loading({
        content: '正在查询详情',
        duration: 0
      });
      this.dailyReportDetail = deepCopy(item);
      // let name = this.getReportName(this.dailyReportDetail.reportType);
      // this.$set(this.dailyReportDetail, 'reportName', name); //自定义类型名称
      await this.$api.upload
        .getFormFiles('documentFile', { formDataId: item.dailyReportId })
        .then(res => {
          msg();
          if (res && res.data.resultCode === 20000 && res.data.resultData.length > 0) {
            this.dailyReportDetail.fileMsg = res.data.resultData[0];
            this.dailyReportDetailModal = true;
          } else {
            this.$nextTick(() => {
              this.$Message.error('查询失败，请重试');
            });
          }
        });
    },
    //获取类型名称
    getReportName(reportType) {
      let reportList = [];
      reportList = this.typeList.filter(item => {
        return item.code === reportType;
      });
      let name = reportList[0].name;
      return name;
    },
    // 管理列表-点击补发
    clickReplenish(item) {
      this.dailyReportDetail = deepCopy(item);
      this.reissueForm = {
        id: this.dailyReportDetail.id,
        readUserList: [],
        readCompanyids: '',
        readCompanynames: '',
        readUserids: '',
        readUsernames: '',
        smsReminder: this.userInfo.cscpOrgDTO.hasSms.toString()
      };
      this.reissueModal = true;
    },
    // 选择分阅用户
    chooseReadUser() {
      if (
        this.$refs.selectGroup.list.populationGroupList.length == 0 &&
        this.$refs.selectGroup.list.unitGroupList.length == 0
      ) {
        this.$refs.selectGroup.list = this.userAndUnitList;
      }
      this.$refs.selectGroup.selectGroupModal = true;
    },
    // 清空分阅用户
    emptyReadUser() {
      this.userObj = [];
      this.unitObj = [];
      this.reissueForm.readUserList = [];
      this.reissueForm.readUserids = '';
      this.reissueForm.readUsernames = '';
      this.reissueForm.readCompanyids = '';
      this.reissueForm.readCompanynames = '';
    },
    // 分阅用户确认--获取人员
    getCheckList(params) {
      this.userObj = params.userObj;
      this.unitObj = params.unitObj;
      let readUserid = '';
      let readUsername = '';
      let readCompanyids = '';
      let readCompanynames = '';
      let readUserList = [];
      const unitSortMap = {};
      for (let i in this.unitObj) {
        if (readCompanyids.indexOf(this.unitObj[i].templateBusinessId) === -1) {
          readCompanyids += this.unitObj[i].templateBusinessId + ',';
          readCompanynames += this.unitObj[i].templateBusinessName + ',';
          if (!unitSortMap[this.unitObj[i].templateBusinessId]) {
            unitSortMap[this.unitObj[i].templateBusinessId] = this.unitObj[i].groupSort;
          }
        }
      }
      for (let i in this.userObj) {
        if (readUserid.indexOf(this.userObj[i].templateBusinessId) === -1) {
          readUserid += this.userObj[i].templateBusinessId + ',';
          readUsername += this.userObj[i].templateBusinessName + ',';
          readUserList.push({
            readUserid: this.userObj[i].templateBusinessId,
            readUsername: this.userObj[i].templateBusinessName,
            readCompanyId: this.userObj[i].unitId,
            readCompany: this.userObj[i].unitName,
            readCompanySort: unitSortMap[this.userObj[i].unitId],
            readDepartment: this.userObj[i].branchName,
            readDepartmentId: this.userObj[i].branchId,
            phone: this.userObj[i].mobile
          });
        }
      }
      readUserid = readUserid.slice(0, -1);
      readUsername = readUsername.slice(0, -1);
      readCompanyids = readCompanyids.slice(0, -1);
      readCompanynames = readCompanynames.slice(0, -1);
      this.reissueForm.readUserList = readUserList;
      this.reissueForm.readUserids = readUserid;
      this.reissueForm.readUsernames = readUsername;
      this.reissueForm.readCompanyids = readCompanyids;
      this.reissueForm.readCompanynames = readCompanynames;
      console.log(this.reissueForm);
    },
    // 管理列表--时间变化
    dateChange(e) {
      this.searchForm.startTime = e[0] ? e[0] + ' 00:00:00' : '';
      this.searchForm.endTime = e[1] ? e[1] + ' 23:59:59' : '';
    },
    // 管理列表--重置并搜索
    resetSearchForm() {
      this.searchForm.title = '';
      this.searchForm.readUsername = '';
      this.searchForm.createName = '';
      this.searchForm.departmentName = '';
      this.searchForm.startTime = '';
      this.searchForm.endTime = '';
      this.searchDate = [];
      this.currentPageChange(1);
    },
    // 管理列表--分页
    currentPageChange(pageNo) {
      this.searchForm.currentPage = pageNo;
      this.getDataList();
    },
    // 管理列表--获取数据
    getDataList() {
      this.$api.leaderInstruction.queryDailyReportLeaderSignListPage(this.searchForm).then(res => {
        if (res.data.resultCode === 20000 && res.data.resultData) {
          this.dataList = res.data.resultData.data;
          this.total = res.data.resultData.recordsTotal;
        } else {
          this.dataList = [];
          this.total = 0;
        }
      });
    },
    // 获取机构和人员
    getGroupList() {
      this.$api.distributionGroup.queryTemplateGroupList({ modelForm: 'dailyReport' }).then(res => {
        this.userAndUnitList = res.data.resultData;
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ivu-modal-header p {
  width: 700px;
}
.daily-report-manage {
  .footer {
    padding: 15px 0 10px 0;
    width: 100%;
    text-align: right;
  }
}
.daily-report-detail-modal {
  .ivu-modal-body {
    table {
      width: 100%;
      text-align: center;
      &,
      & > tr > td {
        border: 1px solid #f00;
        border-collapse: collapse;
      }
      tr {
        height: 50px;
        td {
          width: 25%;
          &.red-text {
            color: #f00;
          }
        }
        &.select-user {
          height: 200px;
          .user-main {
            height: 199px;
            padding: 5px;
            overflow-y: auto;
            text-align: left;
          }
        }
      }
    }
  }
}
.read-status-detail-modal {
  .table-main {
    height: 500px;
    .ivu-modal-body {
      height: 600px;
    }
    .footer {
      padding-top: 15px;
      width: 100%;
      text-align: right;
    }
  }
  .footer-btn-box {
    text-align: center;
  }
}
.reissue-modal {
  .reissue-main {
    width: 100%;
    height: 200px;
    display: flex;
    border: 1px solid #f00;
    & > div:nth-child(1) {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #f00;
      border-right: 1px solid #f00;
    }
    & > div:nth-child(2) {
      flex: 3;
      .user-main {
        height: 170px;
        padding: 5px;
        overflow-y: auto;
        text-align: left;
      }
      .operation-box {
        height: 30px;
        display: flex;
        justify-content: space-between;
        .btn-box {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
