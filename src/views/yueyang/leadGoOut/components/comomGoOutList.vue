<!--
 * @Author: srd15999579174 <EMAIL>
 * @Date: 2023-06-20 17:48:42
 * @LastEditors: srd17680162958 <EMAIL>
 * @LastEditTime: 2024-08-06 15:30:33
 * @FilePath: \biyi-oa-web\src\views\yueyang\leadGoOut\components\comomGoOutList.vue
 * @Description: comStatus 1：外出请示 2：外出报备
-->
<template>
  <div>
    <Tabs v-model="tableStatus" @on-click="clickTab">
      <TabPane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name">
        <self-go-out-list
          :tableStatus="item.name"
          :tableData="item.tableData"
          :tableTotal.sync="item.tableTotal"
          :comStatus="comStatus"
          :ref="'tab' + item.name"
          v-bind="$attrs"
          @handleAction="handleAction"
          @handleGoOutToReport="handleGoOutToReport"
          @onRefresh=""
        />
      </TabPane>
    </Tabs>
    <Modal v-model="addModal" :title="title" width="60%" class-name="instructions-modal">
      <template v-if="addModal">
        <h1 class="title">领导外出请假{{ currentComStatus === '1' ? '报告' : '报备' }}单</h1>
        <table class="main-table">
          <!-- <tr>
            <td class="red-text" colspan="1">*标题：</td>
            <td colspan="3">
              <Input
                placeholder="请输入标题"
                :disabled="disabled"
                v-model="goOutConfigForm.title"
              />
            </td>
          </tr> -->
          <tr>
            <td class="red-text" colspan="1">*姓名：</td>
            <td colspan="1">
              <div class="user-main">
                <!-- {{ goOutConfigForm.leaderName }} -->
                <Input
                  placeholder="请选择外出领导姓名"
                  :disabled="disabled"
                  readonly
                  v-model="goOutConfigForm.leaderName"
                  @on-focus="showSelectGroupModel"
                />
              </div>
            </td>
            <td class="red-text" colspan="1">职务：</td>
            <td colspan="1">
              <Input disabled type="textarea" autosize v-model="goOutConfigForm.leaderDuties" />
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">呈报单位：</td>
            <td colspan="3">
              <Input
                v-if="currentComStatus === '1'"
                v-model="goOutConfigForm.requestCompanyName"
                disabled
              />
              <Input v-else disabled v-model="goOutConfigForm.reportCompanyName" />
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">*外出地点：</td>
            <td colspan="3">
              <div class="select-input-box">
                <Select
                  v-model="goOutConfigForm.gooutPlaceTypeId"
                  label-in-value
                  style="width: 200px"
                  @on-change="e => handleSelect(e, 'gooutPlaceType')"
                >
                  <Option
                    v-for="item in gooutPlaceList"
                    :value="item.code"
                    :label="item.name"
                    :key="item.code"
                  >
                  </Option>
                </Select>
                <Input
                  :disabled="disabled"
                  v-model="goOutConfigForm.gooutPlace"
                  placeholder="请输入外出地点"
                  :autosize="true"
                />
              </div>
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">*外出事由：</td>
            <td colspan="3">
              <div class="select-input-box">
                <Select
                  v-model="goOutConfigForm.gooutReasonTypeId"
                  label-in-value
                  style="width: 200px"
                  @on-change="e => handleSelect(e, 'gooutReasonType')"
                >
                  <Option
                    v-for="item in gooutReasonList"
                    :value="item.code"
                    :label="item.name"
                    :key="item.code"
                  >
                  </Option>
                </Select>
                <Input
                  type="textarea"
                  v-model="goOutConfigForm.gooutReason"
                  :autosize="true"
                  placeholder="请输入外出事由"
                  :disabled="disabled"
                />
              </div>
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">*出发时间：</td>
            <td colspan="3">
              <DatePicker
                :options="options3"
                type="datetime"
                placeholder="请选择出发时间"
                format="yyyy-MM-dd HH:mm"
                v-model="goOutConfigForm.gooutTime"
                class="form-input"
                :editable="false"
                placement="right"
                :disabled="disabled"
                @on-change="changeStartTime"
                @on-ok="changeDateDiff"
                @on-clear="clearStartTime"
              ></DatePicker>
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">*返回时间：</td>
            <td colspan="3">
              <DatePicker
                :options="options3"
                type="datetime"
                placeholder="请选择返回时间"
                format="yyyy-MM-dd HH:mm"
                v-model="goOutConfigForm.returnTime"
                class="form-input"
                placement="left"
                :editable="false"
                @on-change="changeEndTime"
                @on-ok="changeDateDiff"
                @on-clear="clearEndTime"
                :disabled="disabled"
              ></DatePicker>
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">外出期间主持工作人员及职务</td>
            <td colspan="3">
              <Input
                type="textarea"
                placeholder="请输入外出期间主持工作人员及职务"
                v-model="goOutConfigForm.presideWorkPersonPost"
                :autosize="{ minRows: 4, maxRows: 8 }"
                :disabled="disabled"
              />
            </td>
          </tr>
          <tr>
            <td class="red-text" colspan="1">*密级</td>
            <td colspan="3">
              <div class="select-input-box active">
                <Select
                  v-model="goOutConfigForm.secretLevel"
                  :label-in-value="true"
                  not-found-text="当前用户暂无可发布类型"
                  style="width: 30%"
                  :disabled="disabled"
                  @on-change="e => handleSelect(e, 'secretLevel')"
                >
                  <Option
                    v-for="(item, index) in secretLevelList"
                    :key="index"
                    :value="item.code"
                    :label="item.name"
                  >
                  </Option>
                </Select>
              </div>
            </td>
          </tr>
          <!-- 正文注释 -->
          <tr class="select-user" :class="{ disabled: fileDisabled }">
            <td class="red-text" colspan="1">附件：</td>
            <td colspan="3" style="padding: 10px">
              <div v-if="addModal" style="display: flex">
                <ArticleUpload
                  :action="action2"
                  :headers="headers"
                  :data="{
                    formDataId:
                      (goOutConfigForm.isProcess && goOutConfigForm.formDataId) ||
                      goOutConfigForm.id
                  }"
                  :security="goOutConfigForm.secretLevel"
                  :maxFileNum="10"
                  :multiple="false"
                  :isLooking="!fileDisabled"
                  :format="format"
                  :accept="accept"
                  isPreview
                  isDownload
                  btnText="上传附件"
                  tipText="（最多10个）"
                  ref="contentUpload"
                  @on-change="handlerFileChange"
                >
                  <!-- <template>
                    <Button
                      style="margin: 2px 10px 0 0"
                      type="primary"
                      @click.stop.native="newDoc"
                      class="mb10"
                      v-show="isUpload"
                    >
                      新建正文
                    </Button>
                    <Button type="primary">导入正文</Button>
                 </template> -->
                </ArticleUpload>
              </div>
            </td>
          </tr>
        </table>
        <div class="user-info">
          <div>填报人：{{ userName }}</div>
          <div>联系电话：{{ mobile }}</div>
          <div>填报时间：{{ nowDate }}</div>
        </div>
      </template>
      <div slot="footer" class="footer-btn">
        <Button
          oaType="oaBlue"
          @click="handleSaveBefore(2)"
          :loading="submitLoading"
          v-if="showStatus !== '2'"
          >上报{{
            currentComStatus === '1'
              ? goOutConfigForm.requestCompanyName
              : goOutConfigForm.reportCompanyName
          }}
        </Button>
        <Button
          oaType="oaBlue"
          @click="handleSaveBefore(1)"
          :loading="submitLoading"
          v-if="showStatus === '0' || showStatus === '1' || showStatus === '4'"
        >
          暂存
        </Button>
        <Button @click="handleBack" :loading="submitLoading">返回</Button>
      </div>
    </Modal>
    <!-- 领导姓名的选择和搜索对话框 -->
    <Modal
      title="领导姓名"
      v-model="userSelectModal"
      :mask-closable="false"
      footer-hide
      width="60%"
      :transfer="false"
    >
      <div>
        <Row class="margin-bottom-10">
          <Col span="8">
            <Input
              search
              clearable
              v-model="queryLeaderName"
              suffix="ios-search"
              placeholder="请输入姓名搜索"
              enter-button="搜索"
              @on-search="queryLeaderList"
              @on-clear="queryLeaderList"
            >
              <!-- <Button slot="append" icon="ios-search" @click.stop="getLeaderName"></Button> -->
            </Input>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <Table
              border
              stripe
              ref="leaderNameTable"
              :columns="leaderNameColumns"
              :data="leaderNameList"
              @on-select="selectLeaderName1"
              @on-row-click="handleRowClick"
            ></Table>
          </Col>
          <Col span="24">
            <div v-show="total > 10" class="footer">
              <Page
                @on-change="changeCurrentPage"
                :total="total"
                :current="currentPage"
                :page-size="pageSize"
                transfer
                show-total
                show-elevator
              />
            </div>
          </Col>
        </Row>
      </div>
    </Modal>
  </div>
</template>

<script>
import SelfGoOutList from '@/views/yueyang/leadGoOut/components/selfGoOutList.vue';
import ArticleUpload from '@/views/hnjcoa/components/upload.vue';
import uploadMixins from '@/mixins/upload.js';
import { mapState } from 'vuex';
import mixins from '../mixins/index';
const { VUE_APP_CHANNEL } = process.env;
import { openWps, openWpsOnline } from '@/libs/wps/wpsInvoke';
import { getDictionary } from '@/utils/demoWeb';
const TITLE_MAP = {
  0: '新增',
  1: '编辑',
  2: '详情'
};
// 定义一个共用的函数来生成 column 对象，以减少代码冗余和提高代码复用
function createColumn(name, labelText, total) {
  return {
    name,
    cols: [],
    label: (h, params) => {
      return h('div', [
        h(
          'span',
          {
            style: {
              marginRight: '3px'
            }
          },
          labelText
        ),
        h('Badge', {
          props: {
            count: total
          }
        })
      ]);
    },
    count: 0,
    tableData: [],
    tableTotal: 0
  };
}

// 使用映射表代替硬编码的索引访问，确保数组顺序变化时逻辑的稳定性
const columnMapping = {
  0: '全部',
  1: '待上报',
  2: '已上报',
  5: '驳回'
};

// 通过循环和映射表生成 columns 数组，减少数据冗余
const REPORT_COLUMNS = [
  createColumn('0', '全部', 0),
  createColumn('1', '待上报', 0),
  createColumn('2', '已上报', 0),
  createColumn('5', '驳回', 0)
];
// 同样的逻辑应用于 REQUSET_COLUMNS
const REQUSET_COLUMNS = [...REPORT_COLUMNS];

const REQUIRED_FIELDS = [
  // {
  //   field: 'title',
  //   errMsg: '标题'
  // },
  {
    field: 'leaderName',
    errMsg: '领导姓名'
  },
  // {
  //   field: 'gooutPlace',
  //   errMsg: '外出地点'
  // },
  // {
  //   field: 'gooutReason',
  //   errMsg: '外出事由'
  // },
  {
    field: 'gooutTime',
    errMsg: '出发时间'
  },
  {
    field: 'returnTime',
    errMsg: '返回时间'
  },
  {
    field: 'secretLevel',
    errMsg: '密级'
  }
];
export default {
  name: 'askForInstructions',
  mixins: [uploadMixins, mixins],
  components: { ArticleUpload, SelfGoOutList },
  props: {
    // 1：外出请示 2：外出报备
    comStatus: {
      type: String
    }
  },
  data() {
    return {
      addModal: false,
      disabled: false,
      fileDisabled: false,
      searchForm: {
        leaderName: '',
        gooutStartTime: '',
        gooutEndTime: '',
        state: '',
        currentPage: 1,
        pageSize: 10
      },
      goOutConfigForm: {
        id: null,
        title: '',
        leaderName: '', //领导姓名
        leaderCompanyId: 0, //所属单位id
        leaderCompanyName: '', //单位名称
        requestCompanyId: '',
        requestCompanyName: '',
        reportCompanyId: '',
        reportCompanyName: '',
        leaderDuties: '', //领导职务
        gooutPlace: '',
        gooutReason: '',
        gooutTime: '',
        returnTime: '',
        createPhone: '',
        state: '',
        sortNumber: '',
        gooutReasonTypeId: '1',
        gooutReasonTypeName: '',
        presideWorkPersonPost: '',
        gooutPlaceTypeId: '1',
        gooutPlaceTypeName: '',
        isProcess: '',
        formDataId: '',
        secretLevel: '',
        secretLevelName: ''
      },
      formDataId: '',
      action2: '',
      headers: { Authorization: sessionStorage.token },
      newNowDate: '',
      submitLoading: false,
      tableStatus: '0',
      tabs: [],
      showStatus: '0',
      //选择领导姓名的选择对话框
      userSelectModal: false,
      queryLeaderName: '', //领导姓名的搜索框
      leaderNameColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          key: 'leaderDuties',
          title: '职务',
          align: 'center'
        },
        {
          key: 'leaderName',
          title: '领导姓名',
          align: 'center'
        },
        {
          key: 'requestCompanyName',
          title: '上报单位',
          align: 'center'
        },
        {
          key: 'reportCompanyName',
          title: '报备单位',
          align: 'center'
        }
      ],
      selectLeaderName: '', //选择领导表格中已经勾选的领导姓名列表
      leaderNameList: [],
      isLimit: '1',
      options3: {
        disabledDate: date => {
          return date && date.valueOf() < Date.now() - 86400000;
        }
      },
      format: [
        'doc',
        'dot',
        'wps',
        'wpt',
        'docm',
        'dotm',
        'rtf',
        'uot',
        'mht',
        'mhtml',
        'html',
        'xml',
        'xls',
        'xlt',
        'et',
        'xlsx',
        'xltx',
        'xlsm',
        'pptx',
        'ppt',
        'pot',
        'pps',
        'ppsx',
        'dps',
        'dpt',
        'pptm',
        'potm',
        'ppsm',
        'pdf',
        'zip',
        'ofd',
        'jpg',
        'txt',
        'png',
        'gif',
        'jpeg',
        'bmp',
        'svg',
        'c',
        'java',
        'asp',
        'bat',
        'zip',
        'rar',
        'doc',
        'docx',
        'ofd',
        'pdf',
        'jpg',
        'png',
        'jpeg',
        'bmp'
      ], //上传文件格式限制
      /* 是否上传 */
      isUpload: false,
      /* 当前新的请示状态 */
      currentNewComStatus: undefined,
      /* 领导总人数 */
      total: 0,
      /* 当前页数 */
      currentPage: 1,
      /* 当前页数查询条数 */
      pageSize: 10,
      /* 领导外出事由类型下拉列表 */
      gooutReasonList: [],
      /* 领导外出地点类型下拉列表 */
      gooutPlaceList: []
    };
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo || {}
      // realName: state => state.user.userInfo.realName,
      // mobile: state => state.user.userInfo.mobile
    }),
    userName() {
      let createName = this.goOutConfigForm.declarePeople;
      if (!createName && this.userInfo.realName) {
        return `${this.userInfo.realName}`;
      }
      return createName;
    },
    mobile() {
      let createPhone = this.goOutConfigForm.createPhone;
      if (!createPhone && this.userInfo.mobile) {
        return `${this.userInfo.mobile}`;
      }
      return createPhone;
    },
    nowDate() {
      let declareTime = this.goOutConfigForm.declareTime;
      if (!declareTime) {
        declareTime = this.parseTime(new Date(), '{y}年{m}月{d}日');
      }
      return declareTime;
    },
    accept() {
      return this.format
        .map(item => {
          return `.${item}`;
        })
        .join();
    },
    title() {
      const title = TITLE_MAP[this.showStatus];
      return title;
    },
    // 当前表单状态
    currentComStatus() {
      let comStatus = this.comStatus;
      if (this.currentNewComStatus) {
        comStatus = this.currentNewComStatus;
      }
      return comStatus;
    },
    // 当前请求点接口类型
    currentReportApi() {
      // this.showStatus:0-新增,1-编辑,2-详情,3-追加材料,4-外出报备
      // this.currentComStatus "1" 外出请示 "2" 外出报备
      let api = this.$api.leaderGoOut.createBizLeaderOutgoRequest;
      if (this.currentComStatus === '2') {
        api =
          this.showStatus === '0'
            ? this.$api.leaderGoOut.createBizLeaderOutgoReport
            : this.$api.leaderGoOut.updateBizLeaderOutgoReport;
      } else if (this.currentComStatus === '1' && this.showStatus !== '0') {
        api = this.$api.leaderGoOut.updateBizLeaderOutgoRequest;
      }
      return api;
    },
    currentPageApi() {
      // this.showStatus:0-新增,1-编辑,2-详情,3-追加材料,4-外出报备
      // this.currentComStatus "1" 外出请示 "2" 外出报备
      let api = this.$api.leaderGoOut.queryBizLeaderOutgoRequestPage;
      if (this.comStatus == '2') {
        api = this.$api.leaderGoOut.queryBizLeaderOutgoReportPage;
      }
      return api;
    },
    /* 是否外出报备 */
    isReport() {
      return this.comStatus === '2';
    },
    currentDetailApi() {
      let currentApi = this.$api.leaderGoOut.getLeaderOutgoRequestById;
      if (this.isReport) {
        currentApi = this.$api.leaderGoOut.getLeaderOutgoReportById;
      }
      return currentApi;
    }
  },
  async created() {
    if (this.comStatus === '1') {
      this.tabs = REQUSET_COLUMNS;
    } else {
      this.tabs = REPORT_COLUMNS;
    }
    this.action2 = this.$util.getAjaxUrl('workflow') + '/api/enclosureFile/uploadFormFiles';
    let statusList = this.tabs.map(item => item.name);
    const pageList = await Promise.all(
      statusList.map(async (item, index) => {
        const resultData = await this.queryPage(item);
        return resultData;
      })
    );
    pageList.forEach((element, index) => {
      let item = this.tabs[index];
      const data = element?.data ?? [];
      const tableTotal = element?.recordsTotal ?? 0;
      item = Object.assign(item, createColumn(item.name, columnMapping[item.name], tableTotal));
      this.$set(item, 'tableData', data);
      this.$set(item, 'tableTotal', tableTotal);
      // this.$set(item, 'count', tableTotal);
    });
    this._initSelectList();
    this._initSecretLevelList(); // 初始化保密级别
  },
  mounted() {
    this.$on('hook:beforeDestroy', () => {
      clearInterval(this.timer);
    });
  },
  methods: {
    handleSelect(e, type) {
      const item = e || {};
      if (type === 'gooutReasonType') {
        this.goOutConfigForm.gooutReasonTypeName = item.label;
      } else if (type === 'gooutPlaceType') {
        this.goOutConfigForm.gooutPlaceTypeName = item.label;
      } else if (type === 'secretLevel') {
        this.goOutConfigForm.secretLevelName = item.label;
      }
    },
    async initSelectList() {
      if (this.gooutReasonList.length === 0) {
        this.gooutReasonList = await getDictionary('gooutReasonType');
      }
      if (this.gooutPlaceList.length === 0) {
        this.gooutPlaceList = await getDictionary('gooutPlaceType');
      }
      const reasonTypeItem = this.gooutReasonList[0] || {};
      const gooutPlaceItem = this.gooutPlaceList[0] || {};

      if (reasonTypeItem.id) {
        this.goOutConfigForm.gooutReasonTypeId = reasonTypeItem.code;
        this.goOutConfigForm.gooutReasonTypeName = reasonTypeItem.name;
      }
      if (gooutPlaceItem.id) {
        this.goOutConfigForm.gooutPlaceTypeId = gooutPlaceItem.code;
        this.goOutConfigForm.gooutPlaceTypeName = gooutPlaceItem.name;
      }
    },
    handlerFileChange(fileList) {
      this.isUpload = fileList.length > 0 ? false : true;
    },

    // 新建正文
    async newDoc() {
      let user = this.$store.state.user.userInfo;
      let msg = this.$Message.loading({
        content: '正在打开WPS',
        duration: 0
      });
      let res = await this.$api.upload.createNeFile(this.goOutConfigForm.id);
      this.$refs.contentUpload.uploadList.push(res.data.resultData);

      if (VUE_APP_CHANNEL === 'yueyang') {
        openWpsOnline(res.data.resultData, user, 'redSeal');
      } else {
        openWps(res.data.resultData, user, 'redSeal');
      }
      msg();
    },
    clickTab(tabName) {
      this.tableStatus = tabName;
      this.$refs['tab' + tabName][0].getDataList(this.tableStatus);
    },
    async handleAction(params) {
      this.goOutConfigForm = this.$options.data().goOutConfigForm;
      let {
        actionStatus = '0',
        declareTime // 填报时间
      } = params;
      // 如果是流程新增数据查看详情则进行特殊处理
      if (params.formDataId) {
        this._queryLeaderOutgoConfigProcessInfo(params.formDataId);
        return;
      }
      this.showStatus = actionStatus;
      //actionStatus:0-新增,1-编辑,2-详情,3-追加材料,4-外出报备
      this.newNowDate = '';
      if (actionStatus === '0') {
        //获取雪花id
        this._getCirculateId().then(res => {
          this.formDataId = res;
          this.goOutConfigForm.id = this.formDataId;
        });
        this.isUpload = true;
        this.initSelectList();
      }
      if (actionStatus === '0' || actionStatus === '1') {
        this.disabled = false;
        this.fileDisabled = false;
        // if (actionStatus === '1') {
        //   // this.handleIsLimit(leaderId);
        // }
      } else {
        if (actionStatus === '2') {
          this.disabled = true;
          this.fileDisabled = true;
          this.newNowDate = declareTime;
        }
        if (actionStatus === '3') {
          this.disabled = true;
          this.fileDisabled = false;
        }
        if (actionStatus === '4') {
          this.disabled = true;
          this.fileDisabled = true;
        }
      }
      if (actionStatus !== '0') {
        // let newCpdAnnexId = cpdAnnexId ? cpdAnnexId : 0;
        const formDataId = params.isProcess ? params.formDataId : params.id;
        this.formDataId = params.id;
        this._getFormFiles(formDataId, true).then(res => {
          const e = res;
          this.$nextTick(() => {
            this.$refs.contentUpload.uploadList = e;
          });
        });
      }
      if (params.id) {
        const { data } = await this.currentDetailApi(params.id);
        const { resultCode, resultData } = data;
        if (resultCode === 20000) {
          this.goOutConfigForm = resultData;
        }
      }
      if (actionStatus === '0') {
        this.initSelectList();
      }
      this.addModal = true;
    },
    // // 查询领导干部外出请示流程信息
    // async queryLeaderOutgoConfigProcessInfo(id) {
    //   const { data } = await this.$api.leaderGoOut.leaderOutgoConfigProcessInfo(id);
    //   if (data.resultCode === 20000) {
    //     this.queryLeaderOutgoConfigProcessDetails(data.resultData);
    //   }
    // },
    // // 领导干部外出请示详情
    // queryLeaderOutgoConfigProcessDetails(params) {
    //   const design = this.$router.resolve({
    //     name: 'form-render',
    //     query: {
    //       processDefinitionKey: params.processDefinitionKey,
    //       processInstanceId: params.processInstanceId,
    //       rootProcessInstanceId: params.rootProcessInstanceId,
    //       taskId: params.taskId,
    //       nodekey: params.taskDefinitionKey,
    //       formId: params.formId,
    //       status: 'finished',
    //       procTypeName: params.procTypeName,
    //       formDataId: params.formDataId
    //     }
    //   });
    //   let winOpen = window.open(design.href, '_blank');
    //   winOpen = winOpen.focus();
    // },
    // 外出请示转报备
    async handleGoOutToReport(params) {
      this.fileDisabled = false;
      this.showStatus = params.actionStatus;
      this.currentNewComStatus = params.comStatus;
      this.goOutConfigForm = params;
      const id = this.goOutConfigForm.isProcess
        ? this.goOutConfigForm.formDataId
        : this.goOutConfigForm.id;
      this._getFormFiles(id, true).then(res => {
        const e = res;
        this.isUpload = true;
        this.$nextTick(() => {
          this.$refs.contentUpload.uploadList = e;
        });
      });
      this.addModal = true;
    },

    async handleIsLimit(id) {
      let res = await this.$api.leaderGoOut.getOneByLeaderId(id);
      if (res.data.resultCode === 20000) {
        this.goOutConfigForm.sortNumber = res.data.resultData.sortNumber;
        this.isLimit = res.data.resultData.isLimit;
        // 设置disabledDate
        this.options3 = {
          disabledDate: date => {
            let myDate = new Date();
            myDate =
              this.isLimit === '1' ? myDate.setDate(myDate.getDate() + 2) : Date.now() - 86400000;
            return date && date.valueOf() < myDate;
          }
        };
      }
    },

    showSelectGroupModel() {
      this.queryLeaderName = null;
      this.$nextTick(() => {
        this.userSelectModal = true;
        this.queryLeaderList();
      });
    },
    queryLeaderList() {
      this.currentPage = 1;
      this.getLeaderName();
    },
    changeCurrentPage(pageNo) {
      this.currentPage = pageNo;
      this.getLeaderName();
    },
    //选择领导对话框中的列表展示
    async getLeaderName() {
      const params = {
        leaderName: this.queryLeaderName,
        currentPage: this.currentPage,
        pageSize: this.pageSize
      };
      let res = null;
      res = await this.$api.leaderGoOut.queryOutgoConfigByLeaderCompanyIdPage(params);
      if (res.data.resultCode === 20000) {
        this.leaderNameList = res?.data?.resultData?.data ?? [];
        this.total = res?.data?.resultData?.recordsTotal || 1;
      }
      for (let j = 0; j < this.leaderNameList.length; j++) {
        if (this.goOutConfigForm.leaderId == this.leaderNameList[j].id) {
          this.$set(this.leaderNameList[j], '_checked', true);
        } else {
          this.$set(this.leaderNameList[j], '_checked', false);
        }
      }
    },
    //选择领导的选择对话框中表格多选事件
    selectLeaderName1(selection, row) {
      this.goOutConfigForm.leaderId = row.id;
      this.goOutConfigForm.leaderName = row.leaderName;
      this.goOutConfigForm.leaderCompanyId = row.leaderCompanyId;
      this.goOutConfigForm.leaderCompanyName = row.leaderCompanyName;
      this.goOutConfigForm.reportCompanyId = row.reportCompanyId;
      this.goOutConfigForm.reportCompanyName = row.reportCompanyName;
      this.goOutConfigForm.requestCompanyId = row.requestCompanyId;
      this.goOutConfigForm.requestCompanyName = row.requestCompanyName;
      this.goOutConfigForm.leaderDuties = row.leaderDuties;
      this.goOutConfigForm.sortNumber = row.sortNumber;
      this.goOutConfigForm.dutiesType = row.dutiesType;
      this.userSelectModal = false;
      // this.handleIsLimit(row.leaderId);
    },
    //新增行点击事件
    /**
     *
     * @param {*} row 行数据
     * @param {*} index 下标
     */
    handleRowClick(row, index) {
      this.selectLeaderName1([], row);
    },

    // 正文删除
    sureDelete(item) {
      this.$api.notice.deleteFormFiles(item.id).then(res => {
        if (res.data.resultCode == 20000) {
          this.$refs.contentUpload.deleteSuccess();
        }
      });
    },
    //请示新建保存
    async handleSave() {
      // // 进行附件合并
      // const success = await this.handerMergeFile('合并上报中请稍后...');
      // if (!success) {
      //   return this.$Message.error('合并失败，请重试！');
      // }
      this.submitLoading = true;
      const msg = this.$Message.loading({
        content: '正在保存',
        duration: 0
      });
      const params = {
        ...this.goOutConfigForm,
        createPhone: this.mobile,
        declarePeople: this.userName,
        declareTime: this.nowDate
      };
      let res = await this.currentReportApi(params);
      this.submitLoading = false;
      msg();
      if (res.data.resultCode === 20000) {
        this.$Message.success('保存成功');
        this.addModal = false;
        // 如果是外出请示转外出报备则跳转到外出报备列表
        // if (this.currentComStatus === '2') {
        //   this.currentNewComStatus = undefined;
        //   this.$router.replace({
        //     name: 'putOnRecords'
        //   });
        // }
        this.$refs.contentUpload.uploadList = []; //清除附件
        // const tableStatus = this.isReport ? '1' : '0';
        this.clickTab('0');
      } else {
        this.$Message.error('保存失败，请重试');
      }
    },
    //请示保存前操作
    async handleSaveBefore(status) {
      this.goOutConfigForm.createPhone = this.userInfo.mobile;
      this.goOutConfigForm.state = status;
      this.goOutConfigForm.gooutTime = this.parseTime(
        this.goOutConfigForm.gooutTime,
        '{y}-{m}-{d} {h}:{i}:{s}'
      );
      this.goOutConfigForm.returnTime = this.parseTime(
        this.goOutConfigForm.returnTime,
        '{y}-{m}-{d} {h}:{i}:{s}'
      );

      let errMsg = '';
      for (let index = 0; index < REQUIRED_FIELDS.length; index++) {
        const element = REQUIRED_FIELDS[index];
        const field = element.field;
        if (!this.goOutConfigForm[field]) {
          errMsg = `请填写${element.errMsg}`;
          break;
        }
      }
      if (errMsg) {
        this.$Message.error(errMsg);
        return;
      }
      // 判断出发/返回时间大小
      const { returnTime, gooutTime } = this.goOutConfigForm;
      const num = new Date(returnTime).getTime() - new Date(gooutTime).getTime();
      if (num < 0) {
        this.$Message.error('返回时间必须大于出发时间');
        return;
      }
      const { reportCompanyId, requestCompanyId } = this.goOutConfigForm;
      const companyId = this.isReport ? reportCompanyId : requestCompanyId;
      // 判断是否存在报备单位或请示单位没有提示添加且是上报操作时进行校验是否存在请示/报备单位
      if (!companyId && status === 2) {
        this.handleTipNoReportCompanyName();
        return;
      }
      this.handleSave();
    },
    // 提示没有报备单位或请示单位
    handleTipNoReportCompanyName() {
      const text = this.isReport ? '报备' : '请示';
      const content = `该用户还没有配置${text}单位，需要先配置${text}单位才能进行上报操作哦,是否进行暂存呢`;
      this.$Modal.confirm({
        title: '温馨提醒',
        content: content,
        loading: true,
        onOk: () => {
          // 将状态改成暂存不进行上报操作
          this.goOutConfigForm.state = 1;
          this.handleSave();
          this.$Modal.remove();
        }
      });
    },
    // async handerMergeFile() {
    //   this.handlerMergeDocumentTip();
    //   const formDataId = this.goOutConfigForm.id || this.formDataId;
    //   let success = false;
    //   try {
    //     const { data } = await this.$api.leaderGoOut.remedySingleFjMerge(formDataId);
    //     if (data.resultCode === 20000) {
    //       success = true;
    //     }
    //   } catch (error) {
    //     throw new Error(error);
    //   }
    //   this.$Spin.hide();
    //   return success;
    // },
    handleBack() {
      this.currentNewComStatus = undefined;
      this.addModal = false;
    },
    // 表单时间选择
    changeStartTime(e) {
      //开始时间选择
      this.goOutConfigForm.gooutTime = e;
    },
    changeEndTime(e) {
      //结束时间选择
      this.goOutConfigForm.returnTime = e;
    },

    changeDateDiff() {
      if (!this.goOutConfigForm.gooutTime || !this.goOutConfigForm.returnTime) {
        return;
      }
      if (this.goOutConfigForm.gooutTime > this.goOutConfigForm.returnTime) {
        this.goOutConfigForm.gooutTime = '';
        this.$Message.error('结束时间不能小于开始时间');
        return;
      }
    },
    clearStartTime() {},
    clearEndTime() {},
    async queryPage(state) {
      this.searchForm.state = state == 0 ? '' : state;
      let res = await this.currentPageApi(this.searchForm);
      let data = {};
      if (res.data.resultCode === 20000) {
        data = res?.data?.resultData ?? {};
      }
      return data;
    },
    async getDataList(state, index) {
      const resultData = await this.queryPage(state);
      const data = resultData?.data ?? [];
      this.tabs[index].tableData = data;
      this.tabs[index].tableTotal = resultData?.recordsTotal ?? 0;
      this.tabs[index].count = resultData?.recordsTotal ?? 0;
    }
  },
  watch: {
    // addModal(newValue, oldValue) {
    //   // if (!newValue) {
    //   //   clearInterval(this.timer);
    //   // }
    // }
    // comStatus: {
    //   handler(val) {
    //     this.tableStatus = val === '2' ? '1' : '0';
    //   },
    //   immediate: true
    // }
  }
};
</script>

<style lang="less" scoped>
// .disabled {
//   pointer-events: none;
// }
/deep/ .instructions-modal {
  .title {
    text-align: center;
    line-height: 50px;
    color: #f00;
    // font-size: 36px;
    padding: 10px;
    font-weight: normal;
    // font-weight: 800;
    // font-family: Consolas, arial, SimSun;
    text-align: center;
  }
  .main-table {
    margin: 0 auto;
    width: 100%;
    &,
    & > tr > td {
      border: 1px solid #f00;
      border-collapse: collapse;
    }
    .ivu-input:focus {
      /*去除选中后样式*/
      outline: none;
      border: none;
    }
    //其他样式设置
    .ivu-input {
      /*除默认样式*/
      border: none !important;
      border-radius: 0;
      background: none;
      // text-align: center;
      color: #444;
      font-size: 14px;
    }
    .ivu-select-disabled .ivu-select-selection {
      background: #fff;
      color: #444;
    }
    tr {
      height: 50px;
      td {
        width: 25%;
        min-width: 200px;
        &.red-text {
          font-size: 18px;
          text-align: center;
          color: #f00;
        }
      }
    }
  }
  .user-info {
    margin: 0 auto;
    width: 1000px;
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
  }
  .select-input-box {
    display: flex;
    align-items: center;
    min-height: 100px;
    padding: 0 5px;
    &.active {
      min-height: auto;
    }
  }
  .footer-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // button {
    //   // width: 150px;
    // }
  }
}
</style>
