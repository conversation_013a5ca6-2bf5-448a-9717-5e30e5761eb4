<!--
 * @Author: srd17680162958 <EMAIL>
 * @Date: 2024-04-16 11:48:29
 * @LastEditors: srd17680162958 <EMAIL>
 * @LastEditTime: 2024-08-22 15:33:54
 * @FilePath: /oaweb/src/views/yueyang/leadGoOut/components/browsingList.vue
 * @Description: 收件箱列表组件
-->

<template>
  <div class="browsing-list">
    <div class="header">
      <Form :model="searchForm" inline label-colon :label-width="80">
        <FormItem label="单位" :label-width="50">
          <Input v-model="searchForm.companyName" clearable style="width: 200px" />
        </FormItem>
        <FormItem label="外出时间">
          <DatePicker
            v-model="searchDate"
            @on-change="date => dateChange(date, 'gooutTime')"
            transfer
            clearable
            type="daterange"
            style="width: 200px"
          ></DatePicker>
        </FormItem>
        <FormItem label="返回时间">
          <DatePicker
            v-model="returnTime"
            @on-change="date => dateChange(date, 'returnTime')"
            transfer
            clearable
            type="daterange"
            style="width: 200px"
          ></DatePicker>
        </FormItem>
        <FormItem label="外出事项">
          <Select
            v-model="searchForm.gooutReasonTypeId"
            label-in-value
            clearable
            style="width: 200px"
          >
            <Option
              v-for="item in gooutReasonList"
              :value="item.code"
              :label="item.name"
              :key="item.code"
            >
            </Option>
          </Select>
        </FormItem>
        <FormItem label="外出地点">
          <Select
            v-model="searchForm.gooutPlaceTypeId"
            clearable
            label-in-value
            style="width: 200px"
          >
            <Option
              v-for="item in gooutPlaceList"
              :value="item.code"
              :label="item.name"
              :key="item.code"
            >
            </Option>
          </Select>
        </FormItem>
        <FormItem label="领导类型">
          <Select v-model="searchForm.dutiesType" label-in-value clearable style="width: 200px">
            <Option
              v-for="item in dutiesTypeList"
              :value="item.name"
              :label="item.name"
              :key="item.name"
            >
            </Option>
          </Select>
        </FormItem>
        <FormItem label="上报类型">
          <Select v-model="searchForm.type" clearable label-in-value style="width: 200px">
            <Option
              v-for="item in businessTypeList"
              :value="item.code"
              :label="item.name"
              :key="item.code"
            >
            </Option>
          </Select>
        </FormItem>

        <FormItem :label-width="0">
          <Button @click="changeCurrentPage(1)" oaType="oaBlue">搜索</Button>
        </FormItem>
        <FormItem :label-width="0">
          <Button @click="resetSearchForm" oaType="white">重置</Button>
        </FormItem>
      </Form>
      <div>
        <Button
          v-if="state == 2"
          type="primary"
          class="outStyle"
          style="width: 100px"
          @click="beforeSubmitApproval"
          :disabled="selectedRequestIds.length == 0"
        >
          呈领导批示
        </Button>
        &nbsp; &nbsp;
        <Button
          v-if="state == 2"
          type="primary"
          class="outStyle"
          style="width: 100px"
          @click="beforeBatchSign"
          :disabled="selectedReportIds.length == 0"
        >
          {{ '批量签收' }}
        </Button>
      </div>
    </div>
    <div class="main-table">
      <Table
        :columns="cols"
        :data="dataList"
        border
        stripe
        max-height="500"
        @on-selection-change="selecteChange"
        @on-select-cancel="selecteCancel"
        @on-select-all-cancel="selecteAllCancel"
      >
        <template #action="{ row }">
          <Button @click="checkDetail(row)" oaType="oaEdit">详情</Button>
          <Button @click="showRejectModal(row)" v-if="row.state == 2" oaType="oaDelete">
            驳回
          </Button>
          <Poptip confirm transfer title="确定要签收吗?" @on-ok="handleOnlyUpdateAction(row)">
            <Button v-if="row.state == 2 && row.businessTypeCode === '2'" oaType="oaEdit">
              签收
            </Button>
          </Poptip>
          <!-- <Button @click="checkComments(row)" v-if="[5, 6].includes(row.state)" oaType="oaEdit">
            查看批示
          </Button> -->
        </template>
      </Table>
    </div>
    <div v-show="total > 10" class="footer">
      <Page
        @on-change="changeCurrentPage"
        :total="total"
        :current="searchForm.currentPage"
        :page-size="searchForm.pageSize"
        transfer
        show-total
        show-elevator
        show-sizer
        :page-size-opts="[10, 20, 50, 100, 200]"
        @on-page-size-change="changePageSize"
      />
    </div>
    <Modal v-model="rejectModal" title="填写驳回原因">
      <Form ref="formValidate" :model="rejectForm" :rules="rejectFormRules" :label-width="80">
        <FormItem label="驳回原因" prop="rejectReason">
          <Input
            v-model="rejectForm.rejectReason"
            type="textarea"
            autosize
            placeholder="请填写驳回原因"
          ></Input>
        </FormItem>
      </Form>
      <!-- <div>
        <p>*驳回原因:</p>
        <Input v-model="rejectForm.rejectReason" style="margin-top: 10px" />
      </div> -->
      <div slot="footer">
        <Button @click="rejectModal = false">取消</Button>
        <Button type="primary" @click="rejectInstructions">确认</Button>
      </div>
    </Modal>
    <Modal title="选择流程" v-model="processListModel" width="60%" footer-hide>
      <Card :dis-hover="true">
        <div style="overflow: auto">
          <!-- <div class="typeTitle">
            <Icon type="ios-pricetag" class="titleIcon" />
          </div> -->
          <div class="colspan" v-for="(item, key) in processList" :key="key">
            <div class="process" @click="goFrom(item)">
              <div class="processName">
                <Tooltip :content="item.name" theme="light" placement="top-start">
                  <div>
                    <Icon :type="item.icon" :style="{ color: item.color }" class="processIcon" />
                  </div>
                  <div>{{ item.name }}</div>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </Modal>
  </div>
</template>

<script>
import {
  processIcon,
  processIconColor
} from '@oa/sw-workflow/src/workflow/src/Conformance/tool/color.js';
import { removeRepeat } from '@/utils/demoWeb';
import moment from 'moment';
import mixins from '../mixins/index';
const STATE_MAP = {
  1: '待上报',
  2: '未签收',
  3: '呈报中',
  4: '已批准',
  5: '驳回',
  6: '未批准',
  9: '已完成'
};
const COLOR_MAP = {
  1: '#333333',
  2: '#333333',
  3: '#333333',
  4: '#19be6b',
  5: '#ff9900',
  6: '#ed4014',
  9: '#19be6b'
};
const COLUMNS_SELECTED_ITEM = {
  type: 'selection',
  width: 60,
  align: 'center'
};
export default {
  name: 'browsingList',
  props: ['state'],
  mixins: [mixins],
  data() {
    return {
      businessTypeList: [
        {
          name: '外出请示',
          code: '1'
        },
        {
          name: '外出报备',
          code: '2'
        }
      ],
      searchDate: [],
      /* 返回时间段 */
      returnTime: [],
      /* 查询条件 */
      searchForm: {
        /* 外出事由 */
        gooutReasonTypeId: '',
        /* 外出地点 */
        gooutPlaceTypeId: '',
        /* 单位 */
        companyName: '',
        /* 外出开始时间 */
        gooutStartTime: '',
        /* 外出结束时间 */
        gooutEndTime: '',
        /* 返回开始时间 */
        returnStartTime: '',
        /* 返回结束时间 */
        returnEndTime: '',
        /* 领导类型 */
        dutiesType: '',
        /* 上报类型 */
        type: '',
        currentPage: 1,
        pageSize: 10
      },
      cols: [
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h(
              'span',
              params.index + (this.searchForm.currentPage - 1) * this.searchForm.pageSize + 1
            );
          }
        },
        // {
        //   title: '标题',
        //   key: 'title',
        //   minWidth: 250,
        //   align: 'center'
        // },
        {
          title: '姓名',
          key: 'leaderName',
          minWidth: 150,
          align: 'center'
        },
        {
          title: '职务',
          key: 'leaderDuties',
          minWidth: 200,
          align: 'center',
          tooltip: true
        },
        {
          title: '外出地点',
          key: 'gooutPlace',
          minWidth: 150,
          align: 'center',
          tooltip: true,
          render(h, params) {
            const { row } = params;
            const gooutPlace = row.gooutPlaceTypeName + (row.gooutPlace || '');
            return h('span', {}, gooutPlace);
          }
        },
        {
          title: '出发时间',
          key: 'gooutTime',
          minWidth: 170,
          align: 'center',
          sortable: true,
          render: (h, params) => {
            const { row } = params;
            const gooutTime = row.gooutTime;
            const str = (gooutTime && moment(gooutTime).format('YYYY-MM-DD HH:mm')) || '';
            return h('span', {}, str);
          }
        },
        {
          title: '返回时间',
          key: 'returnTime',
          minWidth: 190,
          align: 'center',
          render: (h, params) => {
            const { row } = params;
            const returnTime = row.returnTime;
            const str = (returnTime && moment(returnTime).format('YYYY-MM-DD HH:mm')) || '';
            return h('span', {}, str);
          }
        },
        {
          title: '上报类型',
          key: 'businessType',
          minWidth: 120,
          align: 'center'
        },
        {
          title: '报送单位',
          key: 'companyName',
          minWidth: 120,
          align: 'center'
        },
        {
          title: '填报人',
          key: 'declarePeople',
          minWidth: 100,
          align: 'center'
        },
        {
          title: '填报时间',
          key: 'declareTime',
          minWidth: 190,
          align: 'center'
        },
        {
          title: '状态',
          key: 'state',
          minWidth: 100,
          align: 'center',
          render: (h, params) => {
            return h(
              'span',

              {
                style: { color: COLOR_MAP[params.row.state] }
              },
              STATE_MAP[params.row.state]
            );
          }
        },
        {
          title: '操作',
          slot: 'action',
          minWidth: 250,
          align: 'center'
        }
      ],
      dataList: [],
      total: 0,
      /* 流程选择弹窗 */
      processListModel: false,
      /* 流程列表 */
      processList: [],
      /* 选择批示的集合 */
      selectedList: [],
      rejectModal: false,
      rejectForm: {
        id: '',
        state: 5,
        rejectReason: '',
        businessTypeCode: ''
      },
      rejectFormRules: {
        rejectReason: [{ required: true, message: '请填写驳回原因', trigger: 'blur' }]
      },
      /* 外出请示流程ID */
      formDataId: ''
    };
  },
  created() {
    this._initSelectList(); // 此方法来自mixins，用于获取外出地点和外出事项选择列表
    this._initDutiesTypeList(); // 此方法来自mixins，用于获取领导干部类型选择列表
    this.searchForm.state = this.state;
    this.state == 2 && this.getProcessList();
  },
  methods: {
    checkDetail(row) {
      this.$emit('check-detail', row);
    },
    checkComments(row) {
      this.$Modal.info({
        title: '查看批示',
        content: row.type === 5 ? row.rejectReason : row.unratifiedReason
      });
    },
    showRejectModal(row) {
      this.rejectForm.id = row.id;
      this.rejectForm.businessTypeCode = row.businessTypeCode;
      this.rejectForm.rejectReason = '';
      this.rejectModal = true;
    },
    /**
     * @description: 选择范围变化
     * @param {*} arr 已选数据
     * @param {*} type 0 人员 1 单位
     * @return {*}
     */
    selecteChange(arr) {
      this.selectedList = removeRepeat([...this.selectedList, ...arr], 'id');
    },
    // 单个取消元素
    selecteCancel(arr, row) {
      // 拿到取消选择的项数据 从arr2中去除 findIndex找返回传入一个符合条件的数组第一个元素位置,没有返回-1
      let result = this.selectedList.findIndex(ele => {
        return ele.id === row.id;
      });
      this.selectedList.splice(result, 1);
    },
    // 取消全部 对选中的数组进行过滤
    selecteAllCancel() {
      this.selectedList = this.selectedList.filter(item => {
        return this.dataList.every(item2 => {
          return item.id !== item2.id;
        });
      });
    },
    //
    /**
     * @description: 生成请示、报备模板
     * @param {*} item 当前请示、报备对象
     * @param {*} index 当前项索引
     * @return {*}
     */
    initTemplate(item, index = 0) {
      // 是否报备
      const isReporting = item.businessTypeCode === '2';
      const time = this.initDateTime(item.gooutTime, item.returnTime);
      const title = `<p><strong>${isReporting ? '外出报备' : '外出请示'}：</strong></p>`;
      const content = `${index === 0 ? title : ''}<p style="text-indent: 2em;">${index + 1}、${
        item.leaderCompanyName
      }${item.leaderDuties}<strong>${item.leaderName}</strong>拟于${time}赴${item.gooutPlace}${
        item.gooutReason
      }</p>`;
      return content;
    },

    /**
     * @description: 初始化时间
     * @param {*} startDate 开始时间
     * @param {*} endDate 结束时间
     * @return {*}
     */
    initDateTime(startDate, endDate) {
      if (!startDate || !endDate) return '';

      startDate = startDate.replace(/\//g, '-');
      endDate = endDate.replace(/\//g, '-');

      // 封装日期格式化逻辑
      const formatDate = (date, currentYear, currentMonth) => {
        const year = moment(date).year();
        const month = moment(date).month();
        const day = moment(date).get('date');
        const format = year === currentYear ? 'M月D日' : 'YYYY年M月D日';
        return moment(date).format(format);
      };

      const currentYear = moment().year();
      const currentMonth = moment().month();

      let startDateTime = formatDate(startDate, currentYear, currentMonth);
      let endDateTime = formatDate(endDate, currentYear, currentMonth);

      let dateStr = '';
      if (moment(endDate).isAfter(moment(startDate))) {
        dateStr = `${startDateTime}-${endDateTime}`;
      } else if (
        endDate.split('-')[1] === startDate.split('-')[1] &&
        endDate.split('-')[2] > startDate.split('-')[2]
      ) {
        if (moment(endDate).year() === currentYear && currentMonth === moment(endDate).month()) {
          endDateTime = moment(endDate).get('date') + '日';
        } else if (
          moment(endDate).year() === currentYear &&
          moment(startDate).month() !== currentMonth
        ) {
          endDateTime = moment(endDate).format('M月D日');
        }
        dateStr = `${startDateTime}-${endDateTime}`;
      } else if (endDate.split('-')[2] === startDate.split('-')[2]) {
        dateStr = `${startDateTime}当天`;
      }
      return dateStr;
    },
    beforeSubmitApproval() {
      if (this.processList.length === 0) {
        this.$Message.warning('还没有配置相关批示流程哦，请先配置流程');
        return;
      }
      if (this.selectedList.length > 100) {
        this.$Modal.confirm({
          title: '温馨提醒',
          content: '您选择的呈报数据超过10条，可能会造成数据合并过慢，等待时间过长哦，是否继续？',
          loading: true,
          okText: '继续呈报',
          onOk: () => {
            this.submitApproval();
            this.$Modal.remove();
          }
        });
      } else {
        this.submitApproval();
      }
    },
    beforeBatchSign() {
      this.$Modal.confirm({
        title: '确认操作',
        content: `您确认要进行【批量签收】操作吗？批量签收只针对外出报备的进行签收哦！`,
        onOk: () => {
          this.handleUpdateAction(this.selectedReportIds);
        }
      });
    },
    // 呈领导批示
    async submitApproval() {
      let requestIds = [],
        reportIds = [];
      const requestArray = this.selectedList.filter(item => item.businessTypeCode === '1');
      const dynamicData = requestArray.map((item, index) => {
        const sort = index + 1;
        item.businessTypeCode === '1' ? requestIds.push(item.id) : reportIds.push(item.id);
        return {
          sort: sort,
          name: item.leaderName,
          post: item.leaderDuties,
          gooutPlace: item.gooutPlaceTypeName + (item.gooutPlace || ''),
          gooutReason: item.gooutReasonTypeName + (item.gooutReason || ''),
          gooutTime: this.initDateTime(item.gooutTime, item.returnTime)
        };
      });
      // 判断是否报备类型 是的话执行批量签收操作
      const obj = {
        requestIds,
        reportIds,
        templateHtml:
          '<p style="text-indent: 2em;">我室已收集整理《近日外出请假情况汇总》，呈报省委领导阅示。</p>'
      };

      sessionStorage.setItem('leaderRequestInfo', JSON.stringify(obj));
      sessionStorage.setItem('dynamicData', JSON.stringify(dynamicData));

      if (this.processList.length > 1) {
        this.processListModel = true;
      } else {
        this.goFrom(this.processList[0]);
      }
    },
    //
    /**
     * @description: 合并呈领导批示文档
     * @param {string} formDataIds // 选择上报id集合
     * @param {string} mergeIds // 合并id集合
     * @param {Array.<Object>} requestMergeList - 包含配置对象的数组
     * @param {String} requestMergeList[].xh - 序号
     * @param {String} requestMergeList[].xm - 领导姓名
     * @param {String} requestMergeList[].zw - 领导职务
     * @param {String} requestMergeList[].wcdd - 外出地点
     * @param {String} requestMergeList[].wcsy - 外出理由
     * @param {String} requestMergeList[].wcsj - 外出时间
     * @return {*}
     */
    async handlerMergeDocument({ formDataIds, requestMergeList, mergeIds }) {
      this.handlerMergeDocumentTip('正在合并呈领导批示数据，请稍后...');
      let res = null;
      const params = {
        formDataIds,
        mergeIds: mergeIds,
        requestMergeList: requestMergeList,
        docType: 'docx' // docType=pdf 合并pdf docType=docx 合并word
      };
      try {
        const { data } = await this.$api.leaderGoOut.remedyWcqsZwMerge(params);
        if (data.resultCode === 20000) {
          res = data.resultData;
        }
      } catch (error) {
        this.$Spin.hide();
        throw new Error(error);
      }
      this.$Spin.hide();
      return res;
    },

    // 获取模块模型列表
    async getProcessList() {
      let data = { typeId: this.typeId, businessType: 'leaderOutgoSign' };
      let res = await this.$api.workflow.getBpmnDefinitionListByProcTypeId(data);
      if (res.data.resultData) {
        this.processList = res.data.resultData;
        this.processList.length > 1 && this.getIcon();
      }
    },
    getIcon() {
      this.processList.forEach((item, key) => {
        item.icon = processIcon[key % 10];
        item.color = processIconColor[key % 10];
      });
    },
    // 跳转起草单
    goFrom(item) {
      this.processListModel = false;
      const design = this.$router.resolve({
        name: 'form-render',
        query: {
          status: 'draft',
          processDefinitionKey: item.processDefinitionKey,
          processDefinitionId: item.processDefinitionId,
          formId: item.formId,
          modelName: 'leaderOutgoSign', // 外出请示类型
          initLeaderData: 1 // 初始化领导外出数据 区分草稿箱中的数据防止被自定义函数覆盖
        }
      });
      window.open(design.href, '_blank');
    },
    async rejectInstructions() {
      let valid = await this.$refs.formValidate.validate(valid => {
        return valid;
      });
      if (valid) {
        let resData = null;
        if (this.rejectForm.businessTypeCode == 1) {
          resData = await this.$api.leaderGoOut.updateBizLeaderOutgoRequest(this.rejectForm);
        } else {
          resData = await this.$api.leaderGoOut.updateBizLeaderOutgoReport(this.rejectForm);
        }
        if (resData.data.resultCode === 20000) {
          this.$Message.success('驳回成功');
          this.rejectModal = false;
          this.getDataList();
        }
      }
    },
    dateChange(e, type) {
      if (type == 'gooutTime') {
        this.searchForm.gooutStartTime = e[0] ? e[0] + ' 00:00:00' : '';
        this.searchForm.gooutEndTime = e[1] ? e[1] + ' 23:59:59' : '';
      } else if (type === 'returnTime') {
        this.searchForm.returnStartTime = e[0] ? e[0] + ' 00:00:00' : '';
        this.searchForm.returnEndTime = e[1] ? e[1] + ' 23:59:59' : '';
      }
    },
    resetSearchForm() {
      this.searchForm = this.$options.data().searchForm;
      this.searchDate = [];
      this.returnTime = [];
      this.selectedList = [];
      this.changeCurrentPage(1);
    },
    changeCurrentPage(pageNo) {
      this.searchForm.currentPage = pageNo;
      this.getDataList();
    },
    changePageSize(size) {
      this.searchForm.pageSize = size;
      this.getDataList();
    },
    getDataList() {
      const params = {
        ...this.searchForm,
        state: this.state
      };
      this.$api.leaderGoOut.queryBizLeaderOutgoInboxPage(params).then(res => {
        if (res.data.resultCode === 20000) {
          const data = res?.data?.resultData?.data ?? [];
          data.forEach(item => {
            const isSelect = this.selectedList.some(ele => ele.id === item.id);
            item['_checked'] = isSelect;
            // 报备的禁用选择
            // item['_disabled'] = !this.isReport && item.businessTypeCode === '2';
          });
          this.dataList = data;
          this.total = res?.data?.resultData?.recordsTotal;
          this.$emit('onChangeBadge', { count: this.total, state: this.state });
        } else {
          this.dataList = [];
          this.total = 0;
        }
      });
    },
    handleOnlyUpdateAction(row) {
      this.handleUpdateAction([row.id]);
    },
    // 报备签收
    async handleUpdateAction(ids) {
      const { data } = await this.$api.leaderGoOut.leaderOutgoReportUpdateState(ids);
      if (data.resultCode === 20000) {
        this.$Message.success('签收成功');
        this.selectedList = this.selectedList.filter(item => !ids.includes(item.id));
        this.getDataList();
      }
    }
  },
  computed: {
    // 是否报备类型
    isReport() {
      return this.searchForm.type == '2' && this.state === '2';
    },
    selectedReportIds() {
      return this.selectedList.filter(item => item.businessTypeCode === '2').map(item => item.id);
    },
    selectedRequestIds() {
      return this.selectedList.filter(item => item.businessTypeCode === '1').map(item => item.id);
    }
  },

  watch: {
    state: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue == '2') {
          this.cols.unshift(COLUMNS_SELECTED_ITEM);
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.browsing-list {
  .outStyle:disabled {
    background: #f7f7f7 !important;
  }
  .header {
    display: flex;
    flex-direction: column;
    padding-bottom: 8px;
    // justify-content: space-between;
  }
  .footer {
    text-align: right;
    padding-top: 10px;
  }
}
</style>
