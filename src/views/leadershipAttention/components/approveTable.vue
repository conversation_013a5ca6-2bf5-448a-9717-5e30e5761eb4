<template>
  <div class="approve-table">
    <Table stripe border :columns="tableCol" :data="tableData" :loading="tableLoading">
      <template #status="{ row }">
        <span :class="['status', `status-${row.status}`]">{{ statusDict[row.status] }}</span>
        <Tooltip
          v-if="row.status === 2 && row.auditReson"
          :content="row.auditReson"
          max-width="320"
          transfer
          placement="top"
        >
          <Icon type="md-alert" color="rgba(254, 66, 40, 1)" size="18" />
        </Tooltip>
      </template>
      <template slot-scope="{ row }" slot="action">
        <Button
          oaType="oaEdit"
          v-if="tabs == 'unread'"
          class="received-action"
          @click="handleCheck(row)"
          >审批</Button
        >
        <Button oaType="oaEdit" class="received-action" @click="handleLook(row)">查看</Button>
      </template>
    </Table>
    <!-- 审核Modal -->
    <Modal v-model="checkHistoryModal" width="50" :mask-closable="false">
      <p slot="header">
        <span>审批</span>
      </p>
      <div>
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="120">
          <FormItem label="是否通过" prop="status">
            <RadioGroup v-model="formValidate.status">
              <Radio :label="3"> 通过 </Radio>
              <Radio :label="2"> 驳回 </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="驳回原因" prop="auditReson" v-if="formValidate.status === 2">
            <Input
              v-model="formValidate.auditReson"
              type="textarea"
              :autosize="{ minRows: 4 }"
              placeholder="驳回原因"
            ></Input>
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <!-- <Button @click="checkHistoryModal = false">关闭</Button> -->
        <Button type="primary" @click="handleSubmit('formValidate')">确认</Button>
        <Button @click="handleReset('formValidate')" style="margin-left: 8px">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
export default {
  props: {
    tabs: {
      type: String,
      default: 'sended'
    },
    tableCol: {
      type: Array,
      default: () => {
        [];
      }
    },
    tableData: {
      type: Array,
      default: () => {
        [];
      }
    },
    name: {
      type: String,
      default: ''
    },
    tableLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const SatusDict = {
      0: '未提交',
      1: '待审批',
      2: '驳回',
      3: '通过',
      4: '已归档'
    };
    return {
      statusDict: SatusDict,
      checkHistoryModal: false,
      loading: true,
      // 审核
      formValidate: {
        status: 3,
        auditReson: ''
      },
      ruleValidate: {
        status: [{ required: true, message: '请选择是否通过' }],
        auditReson: [{ required: true, message: '请输入原因', trigger: 'blur' }]
      }
    };
  },
  computed: {},
  mounted() {},
  methods: {
    handleSubmit(name) {
      const that = this;
      this.$refs[name].validate(valid => {
        if (valid) {
          console.log(that.formValidate);
          this.$api.leadershipAttention.auditIssue(this.formValidate).then(res => {
            if (res.data.resultCode === 20000) {
              this.$Message.success('审核成功');
              this.checkHistoryModal = false;
              this.$emit('getSelect');
              this.$emit('setTabs', 'readed');
            }
          });
        }
      });
    },
    handleReset(name) {
      this.$refs[name].resetFields();
      this.checkHistoryModal = false;
    },
    // 审核
    handleCheck(row) {
      this.checkHistoryModal = true;
      this.formValidate.id = row.id;
    },
    // 点击查看键
    handleLook(row) {
      this.$byStoreSet('manage-edit', { id: row.id, edit: false });
      this.$router.push({
        name: 'manage-edit'
      });
    }
  }
};
</script>

<style lang="less">
.approve-table {
  .sended-action {
    display: flex;
    justify-content: center;
  }
  .received-action {
    cursor: pointer;
  }
}
.status {
  position: relative;
  margin-right: 4px;
  margin-left: 8px;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-12px, -50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #fe4228;
  }
  &.status-0::before {
    background: #ff9900;
  }
  &.status-1::before {
    background: #2185f7;
  }
  &.status-2::before {
    background: #fe4228;
  }
  &.status-3::before {
    background: #19be6b;
  }

  &.status-4::before {
    background: #999999;
  }
}
</style>
