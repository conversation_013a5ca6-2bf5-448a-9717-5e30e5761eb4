<template>
  <div class="our-unit-select" v-loading="loading">
    <div class="our-unit-select__search">
      <Form @submit.native.prevent :model="ourUnitSearchForm" ref="ourUnitSearchForm" label-position="left"
        :label-width="55" inline>
        <FormItem label="部门" prop="strUnitName">
          <Input v-model="ourUnitSearchForm.strUnitName" style="width: 200px" />
        </FormItem>
        <FormItem label="姓名" prop="realName">
          <Input v-model="ourUnitSearchForm.strUserId" style="width: 200px" />
        </FormItem>
        <FormItem label="手机号" prop="strMobile">
          <Input v-model="ourUnitSearchForm.strMobile" style="width: 200px" />
        </FormItem>
        <FormItem label="身份证" prop="idCardNo">
          <Input v-model="ourUnitSearchForm.idCardNo" style="width: 200px" />
        </FormItem>
        <FormItem :label-width="0">
          <Button type="primary" @click="syncBizHrsUserInfo">同步单位人员信息</Button>
        </FormItem>

        <FormItem :label-width="0">
          <Button @click="clickSearch" type="primary">搜索</Button>
        </FormItem>
        <FormItem :label-width="0">
          <Button @click="resetForm" type="primary">重置</Button>
        </FormItem>
        <FormItem :label-width="0">
          <Button @click="exportZip" type="primary">导出</Button>
        </FormItem>
      </Form>

      <div class="select-person-num" style="width: 100px">
        已选
        <span>{{ selectedUserList.length }}</span>
        人
      </div>
    </div>
    <div class="our-unit-select__content">
      <div class="content-item" v-scrollBar>
        <div v-if="unitTree.length">
          <el-tree highlight-current :data="unitTree" default-expand-all>
            <template #default="{ node, data }">
              <span style="display: inline-block; width: 100%; cursor: pointer" @click="personClick(data)">
                <span>
                  <Icon style="font-size: 20px; margin-right: 8px; margin-top: -3px"></Icon>
                  <span>{{ data.title }}</span>
                </span>
              </span>
            </template>
          </el-tree>

          <div v-if="unitTree.length < dataCount" style="display: flex; justify-content: center; margin-top: 20px">
            <div v-loading="toBottomSign">
              <Button @click="handlePageClick" type="primary">加载更多</Button>
            </div>
          </div>
        </div>
        <div v-else>
          <Icon type="ios-alert-outline" color="#FF9900" />
          该机构下无可选单位
        </div>
      </div>

      <div class="content-item" v-scrollBar>
        <div v-if="unitTree.length" v-loading="orgLoading">
          <!-- <el-tree
             highlight-current
            :data="unitNewTree"
            default-expand-all
          >
             <template #default="{ node, data }">
              <span style="display: inline-block; width: 100%; cursor: pointer" @click="personClick(data)">
                <span>
                  <Icon style="font-size: 20px; margin-right: 8px; margin-top: -3px"></Icon>
                  <span>{{ data.title }}</span>
                </span>
              </span>
            </template>
          </el-tree> -->
          <!-- 单位信息 -->
          <Tree :data="unitNewTree" expand :load-data="loadData" @on-select-change="changeTree"></Tree>
        </div>
        <div v-else>
          <Icon type="ios-alert-outline" color="#FF9900" />
          该机构下无可选单位
        </div>
      </div>

      <div class="content-item" v-scrollBar>
        <div v-if="userOptions.length > 0">
          <CellGroup v-for="(item, key) in userOptions" :key="key" @on-click="userClick(item)">
            <Cell>
              <Avatar icon="ios-person" class="margin-right-10" size="small" style="background: rgb(26, 118, 211)" />
              <span class="name-text">{{ item.userName }}</span>
            </Cell>
          </CellGroup>
          <div v-if="unitNewTree.length < unitDataCount"
            style="display: flex; justify-content: center; margin-top: 20px">
            <div v-loading="treeLoading">
              <Button @click="getUnitList" type="primary">加载更多</Button>
            </div>
          </div>
        </div>
        <div v-else>
          <Icon type="ios-alert-outline" color="#FF9900" />该机构下无可选人员
        </div>
      </div>
      <div class="select-button">
        <div class="select-button-wrapper" v-if="isCheckAll">
          <Button type="primary" @click="handleSelectAll">全选</Button>
          <Button type="primary" @click="handleClear">清空</Button>
        </div>
      </div>
      <div class="content-item" v-scrollBar ref="selectedUserList">
        <ul class="transferUl">
          <li v-for="(item, index) in selectedUserList" :key="index" class="user-item">
            <span class="name-text">{{ item.userName }}</span>
            <Icon type="ios-close-circle-outline" color="#E03535" size="20" @click="delUserList(item, index)" />
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { deepCopy } from 'view-design/src/utils/assist';

export default {
  props: {
    isCheckAll: {
      type: Boolean
    }
  },
  data() {
    return {
      ourUnitSearchForm: {
        strUserId: '',
        strMobile: '',
        idCardNo: '',
        strUnitName: ''
      },
      /* 机构树列表 */
      unitTree: [],
      unitNewTree: [],
      userOptions: [],
      selectedUserList: [],
      loading: false,
      hrsUnitName: '',
      deptName: '',
      orgLoading: false,
      toBottomSign: false,
      treeLoading: false,
      dataCount: 0,
      unitDataCount: 0,
      currentPage: 1,
      unitCurrentPage: 1,
      ourUnitSearchNewForm: null
    };
  },
  created() {
    this.loading = true;
    this.getDeptTree(0);
    this.initOrgTree([]);
  },
  mounted() { },
  methods: {
    handlePageScroll(e) {
      if (this.unitTree.length >= this.dataCount) return;
      if (this.toBottomSign) return;

      if (this.unitTree.length) {
        if (e.target.scrollTop + e.target.offsetHeight + 6 > e.target.scrollHeight) {
          // 视为触底
          console.log('触底');
          if (!this.toBottomSign) {
            this.currentPage++;
            this.toBottomSign = true;

            this.getDeptTree();
          }
        } else {
          this.toBottomSign = false;
        }
      }
    },
    handlePageClick() {
      if (this.unitTree.length >= this.dataCount) return;
      if (this.toBottomSign) return;

      if (this.unitTree.length) {
        // 视为触底
        console.log('触底');
        if (!this.toBottomSign) {
          this.currentPage++;
          this.toBottomSign = true;

          this.getDeptTree();
        }
      }
    },
    getUnitList() {
      if (this.unitNewTree.length >= this.unitDataCount) return;
      if (this.treeLoading) return;

      if (this.unitNewTree.length) {
        // 视为触底
        console.log('触底');
        if (!this.treeLoading) {
          this.unitCurrentPage++;
          this.treeLoading = true;

          this.personClick({}, 'loadmore');
        }
      }
    },

    // 点击单个选择
    userClick(item) {
      let isEx = this.selectedUserList.filter(n => {
        return n.userId == item.userId;
      });
      if (isEx.length > 0) {
        this.$Message.error('该用户已被添加');
        return;
      }
      if (this.isCheckAll) {
        this.selectedUserList.push(item);
      } else {
        this.selectedUserList = [item];
      }
      this.$nextTick(() => {
        this.$refs.selectedUserList.scrollTop = this.$refs.selectedUserList.scrollHeight;
      });
    },
    // 全选
    handleSelectAll() {
      if (this.userOptions.length == 0) {
        this.$Message.warning('该工作组下无可选人员');
      } else {
        if (this.selectedUserList.length === 0) {
          this.selectedUserList = deepCopy(this.userOptions);
        } else {
          const selectedUserIds = this.selectedUserList.map(item => {
            return item.userId;
          });
          for (let i in this.userOptions) {
            if (!selectedUserIds.includes(this.userOptions[i].userId)) {
              this.selectedUserList.push(this.userOptions[i]);
            }
          }
        }
      }
    },
    // 清空
    handleClear() {
      this.selectedUserList = [];
    },
    // 删除单个
    delUserList(item, index) {
      this.selectedUserList.splice(index, 1);
    },
    clickSearch() {
      this.currentPage = 1;
      this.unitCurrentPage = 1;
      this.unitTree = [];
      this.getDeptTree(0);
      this.personClick({
        ...this.ourUnitSearchForm
      });
    },
    resetForm() {
      this.currentPage = 1;
      this.unitTree = [];
      this.$refs['ourUnitSearchForm'].resetFields();
      this.getDeptTree(0);
    },
    exportZip() {
      let msg = this.$Message.loading({
        content: '正在导出单位人员信息',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      axios.get(`${baseUrl}/api/bizHrsUserInfo/exportAllUsersByUnit`, {
        // 添加token
        headers: {
          Authorization: sessionStorage.token
        },
        // responseType: 'blob'
      }).then(res => {
        if (res.data.resultCode == 20000) {
          // this.$Message.success('导出成功');
          this.$Message.success({
            // content: `导入成功，可前往“系统管理” - “导出用户文件管理页面”查看结果`,
            duration: 5,
            render: h => {
              return h('span', [
                '导出任务已启动，请稍后在导出用户文件管理中查看导出结果 - “',
                h(
                  'a',
                  {
                    on: {
                      click: () => {
                        this.userResult();
                      }
                    }
                  },
                  '导出用户文件管理'
                ),
                '"页面查看结果'
              ]);
            }
          });
        } else {
          this.$Message.error('导出失败');
        }
      })
        .finally(() => {
          msg();
        });
    },
    userResult() {
      this.$router.push({ name: 'exportUserManage' });
    },
    initOrgTree(checkedList = []) {
      this.getOrgData(0, checkedList).then(res => {
        this.unitNewTree = res;
      });
    },
    getOrgData(id, checkedList = []) {
      return new Promise((resolve, reject) => {
        this.orgLoading = true;
        this.$api.org
          .selectOrgById({
            id
          })
          .then(res => {
            this.orgLoading = false;
            resolve(
              res.data.resultData.map(n => {
                return n.isValuable
                  ? {
                    id: n.id,
                    title: n.title,
                    disabled: n.type === 1 ? true : false,
                    render: (h, { root, node, data }) => {
                      return h(
                        'span',
                        {
                          class: n.type === 1 && 'disbaled_node'
                        },
                        data.title
                      );
                    },
                    checked: checkedList.includes(n.id),
                    children: [],
                    loading: false
                  }
                  : {
                    id: n.id,
                    title: n.title,
                    disabled: n.type === 1 ? true : false,
                    render: (h, { root, node, data }) => {
                      return h(
                        'span',
                        {
                          class: n.type === 1 && 'disbaled_node'
                        },
                        data.title
                      );
                    },
                    checked: checkedList.includes(n.id)
                  };
              })
            );
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    getDeptTree(id) {
      let params = {
        // ...this.ourUnitSearchForm,
        // strId: '0'
        strUserId: this.ourUnitSearchForm.strUserId,
        pageSize: 20,
        strUnitName: this.ourUnitSearchForm.strUnitName ? this.ourUnitSearchForm.strUnitName : '',
        currentPage: this.currentPage
      };

      this.$api.user
        .queryBizHrsUnitInfo(params)
        .then(res => {
          this.dataCount = res.data.resultData.recordsTotal;

          this.unitTree = this.unitTree.concat(res.data.resultData.data);
          this.toBottomSign = false;
          this.changeData();
          if (res.data.resultData.data) {
            // this.userOptions = res.data.resultData.data.map(n => {
            //   return {
            //     userId: n.id,
            //     userName: n.strUserId,
            //     // unitId: n.companyId,
            //     // unitName: n.companyName,
            //     // branchId: n.departmentId,
            //     // branchName: n.departmentName,
            //     // mobile: n.mobile,
            //     ...n
            //   };
            // });
            //  console.log(this.userOptions,"this.userOptions")
          } else {
            this.userOptions = [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    changeData() {
      transfer(this.unitTree);

      function transfer(list) {
        list.forEach(res => {
          res.title = res.strUnitName;
          res.expand = true;
          // res.children = JSON.parse(JSON.stringify(res.childList));
          res.childList = null;

          if (res.children && res.children.length > 0) {
            transfer(res.children);
          }
        });
      }
    },
    loadData(item, callback) {
      this.getOrgData(item.id, []).then(res => {
        callback(res);
      });
    },
    changeTree(obj) {
      console.log(obj);
      if (obj.length > 0) {
        this.deptName = obj[0].title;
      } else {
        this.deptName = '';
      }
    },
    syncBizHrsUserInfo() {
      let hrsUnitName = this.hrsUnitName;
      let deptName = this.deptName;

      if (hrsUnitName && deptName) {
        this.$api.user.syncBizHrsUserInfo(hrsUnitName, deptName).then(res => {
          this.$Message.success('同步成功');
        });
      } else {
        this.$Message.info('请选择需要同步的单位');
      }
    },
    personClick(node = {}, type = '') {
      let msg = this.$Message.loading('正在获取人员列表');
      this.hrsUnitName = node.strUnitName ? node.strUnitName : '';
      this.ourUnitSearchForm.strUnitName = node.strUnitName;
      const params = {
        strUnitName: this.hrsUnitName ? this.hrsUnitName : '',
        strUserId: this.ourUnitSearchForm.strUserId,
        idCardNo: this.ourUnitSearchForm.idCardNo,
        strMobile: this.ourUnitSearchForm.strMobile,
        pageSize: 20,
        currentPage: this.unitCurrentPage
      };
      this.$api.user
        .queryBizHrsUserInfoByStrId(params)
        .then(response => {
          if (response.data.resultData) {
            this.unitDataCount = response.data.resultData.recordsTotal;

            let res = response.data.resultData.data.map(n => {
              return {
                userId: n.id,
                userName: n.strUserId,
                unitName: n.strUnitName,
                idCardNo: n.idCardNo,
                mobile: n.strMobile
                // ...n
              };
            });
            if (type == 'loadmore') {
              this.userOptions = this.userOptions.concat(res);
            } else {
              this.userOptions = res;
            }
          }
        })
        .finally(() => {
          this.treeLoading = false;
          msg();
        });
    }
  }
};
</script>

<style lang="less" scoped>
.our-unit-select {
  .our-unit-select__search {
    display: flex;
    justify-content: space-between;
    min-height: 44px;

    .ivu-form-item {
      margin-bottom: 10px;
    }

    .select-person-num {
      font-size: 16px;

      &>span {
        color: #e03535;
      }
    }
  }

  .our-unit-select__content {
    display: flex;
    height: 50vh;

    .select-button {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      height: 100%;
      line-height: 100%;

      &-wrapper {
        button {
          display: block;
          margin-bottom: 20px;
        }
      }
    }

    .content-item {
      flex: 1;
      position: relative;
      margin-right: 20px;
      padding: 10px 10px;
      overflow-x: hidden;
      border: 1px solid rgb(192, 191, 191);
      border-radius: 5px;
      height: 100%;
      overflow-y: auto;

      &:first-child {
        flex: 0.7;
      }

      &:nth-child(2) {
        flex: 0.7;
      }

      .transferUl>li {
        display: inline-flex;
        min-width: 100px;
        justify-content: space-between;
        align-items: center;
        list-style: none;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin-bottom: 10px;
        margin-right: 8px;
        line-height: 1.5;
        padding: 0 5px;
      }
    }

    .name-text {
      margin-right: 5px;
    }
  }
}
</style>
<style lang="less">
.user-select {
  .ivu-tree {
    background: #fff;
    padding: 0 !important;
  }

  .select-person-num {
    float: right;
    font-size: 16px;

    >span {
      color: #e03535;
    }
  }
}

.ivu-tree-title-selected,
.ivu-tree-title-selected:hover {
  background-color: #f9ebeb;
}
</style>
