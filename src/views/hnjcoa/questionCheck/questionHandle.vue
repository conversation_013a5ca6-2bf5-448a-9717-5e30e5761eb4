<template>
  <div class="question-handle">
    <Form
      class="header"
      @submit.native.prevent
      :model="searchForm"
      inline
      label-colon
      :label-width="100"
    >
      <FormItem label="问卷标题">
        <Input v-model="searchForm.title" clearable style="width: 200px" />
      </FormItem>
      <FormItem :label-width="0">
        <Button @click="currentPageChange(1)" oaType="oaBlue">搜索</Button>
      </FormItem>
      <FormItem :label-width="0">
        <Button @click="resetSearchForm" oaType="white">重置</Button>
      </FormItem>
    </Form>
    <div class="main-table">
      <Table border stripe :columns="cols" :data="dataList">
        <template #action="{ row, index }">
          <Button
            v-if="row.responseText === '未填报'"
            @click="checkQuestionDetail(row, 'edit')"
            oaType="oaEdit"
            style="margin-right: 5px"
            >填写问卷</Button
          >
          <Button
            @click="checkQuestionDetail(row, 'detail')"
            oaType="oaEdit"
            style="margin-right: 5px"
            >详情</Button
          >
        </template>
      </Table>
    </div>
    <div v-show="total > 10" class="footer">
      <Page
        @on-change="currentPageChange"
        :total="total"
        :current="searchForm.currentPage"
        :page-size="searchForm.pageSize"
        transfer
        show-total
        show-elevator
      />
    </div>
    <Modal
      v-model="questionDetailModal"
      :title="questionDetailForm.type === 'edit' ? '填写问卷' : '问卷详情'"
      :width="800"
      class-name="question-detail-modal"
      :footer-hide="questionDetailForm.type === 'detail'"
    >
      <div class="question-detail-main">
        <h2 class="question-title">{{ questionDetailForm.title }}</h2>
        <div class="question-detail-header">
          <p class="other-question-info">问卷说明：{{ questionDetailForm.surveyTip }}</p>
          <p class="other-question-info">截止时间：{{ questionDetailForm.lastTime }}</p>
        </div>
        <Form
          ref="questionDetailForm"
          :disabled="questionDetailForm.type === 'detail'"
          :model="questionDetailForm"
          inline
          label-position="top"
        >
          <FormItem
            v-for="(item, index) in questionDetailForm.questionDetailData"
            :label="index + 1 + '.' + item.topic"
            :key="item.id"
            :prop="'questionDetailData.' + index + '.value'"
            :rules="{
              required: item.isRequired,
              type: item.dataType,
              message: '请完善问题',
              trigger: 'blur,change'
            }"
          >
            <RadioGroup v-model="item.value" v-if="item.type === '1'">
              <Radio
                v-for="optionItem in item.bizQuestionOptionList"
                :label="optionItem.id"
                :key="optionItem.id"
                >{{ optionItem.content }}</Radio
              >
            </RadioGroup>
            <CheckboxGroup v-model="item.value" v-else-if="item.type === '2'">
              <Checkbox
                v-for="optionItem in item.bizQuestionOptionList"
                :label="optionItem.id"
                :key="optionItem.id"
                >{{ optionItem.content }}</Checkbox
              >
            </CheckboxGroup>
            <Input
              v-else
              v-model="item.value"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }"
            />
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button @click="questionDetailModal = false">返回</Button>
        <Button @click="submitQuestionForm" type="primary">提交</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import UserSelectModal from '@/views/components/userSelectModal/index.vue';
import { mapState } from 'vuex';

export default {
  name: 'question-handle',
  components: { UserSelectModal },
  data() {
    return {
      questionPeopleId: '',
      questionDetailDate: '',
      questionDetailModal: false,
      questionDetailForm: {
        id: '',
        type: '',
        title: '',
        lastTime: '',
        surveyTip: '',
        questionDetailData: []
      },
      searchForm: {
        title: '',
        type: 0,
        isRelease: 1,
        currentPage: 1,
        pageSize: 10
      },
      cols: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center'
        },
        {
          title: '标题',
          key: 'title',
          minWidth: 200,
          align: 'center',
          tooltip: true
        },
        {
          title: '创建人',
          key: 'createName',
          minWidth: 100,
          align: 'center'
        },
        {
          title: '发布时间',
          key: 'createTime',
          width: 200,
          align: 'center'
        },
        {
          title: '截至时间',
          key: 'lastTime',
          width: 180,
          align: 'center'
        },
        {
          title: '状态',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('span', {}, params.row.responseText);
          }
        },
        {
          title: '说明',
          key: 'surveyTip',
          minWidth: 200,
          align: 'center',
          tooltip: true
        },
        {
          title: '操作',
          slot: 'action',
          width: 150,
          align: 'center'
        }
      ],
      dataList: [],
      total: 0
    };
  },
  created() {
    this.getDataList();
  },
  methods: {
    submitQuestionForm() {
      if (new Date() > new Date(this.questionDetailForm.lastTime)) {
        this.$Message.error('已超过截止时间');
        return;
      }
      this.$refs.questionDetailForm.validate(valid => {
        if (valid) {
          const reqForm = [];
          const questionDetailData = this.questionDetailForm.questionDetailData;
          console.log(questionDetailData);
          for (let i in questionDetailData) {
            if (questionDetailData[i].type === '1') {
              reqForm.push({
                questionSurveyId: this.questionDetailForm.id,
                questionPeopleId: this.questionPeopleId,
                questionTopicId: questionDetailData[i].id,
                responseOption: questionDetailData[i].value
              });
            } else if (questionDetailData[i].type === '2') {
              for (let j in questionDetailData[i].value) {
                reqForm.push({
                  questionSurveyId: this.questionDetailForm.id,
                  questionPeopleId: this.questionPeopleId,
                  questionTopicId: questionDetailData[i].id,
                  responseOption: questionDetailData[i].value[j]
                });
              }
            } else if (questionDetailData[i].type === '3') {
              reqForm.push({
                questionSurveyId: this.questionDetailForm.id,
                questionPeopleId: this.questionPeopleId,
                questionTopicId: questionDetailData[i].id,
                responseContext: questionDetailData[i].value
              });
            }
          }
          const msg = this.$Message.loading({
            content: '正在提交问卷',
            duration: 0
          });
          console.log(reqForm);
          this.$api.questionnaire.createBatch(reqForm).then(res => {
            msg();
            if (res.data.resultCode === 20000) {
              this.$Message.success('提交成功！');
              this.getDataList();
              this.questionDetailModal = false;
            } else {
              this.$Message.error('查看详情出错');
            }
          });
        }
      });
    },
    showReissueModal(id) {
      this.readMember = '';
      this.reissueForm.id = id;
      this.reissueForm.bizQuestionPeopleList = [];
      this.reissueModal = true;
    },
    // 查看详情
    async checkQuestionDetail(item, type) {
      const msg = this.$Message.loading({
        content: '正在获取详情',
        duration: 0
      });
      this.questionDetailForm.type = type;
      this.questionDetailForm.id = item.id;
      this.questionDetailForm.title = item.title;
      this.questionDetailForm.lastTime = item.lastTime;
      this.questionDetailForm.surveyTip = item.surveyTip;
      let resData = null;
      if (type === 'edit') {
        resData = await this.$api.questionnaire.getFilling(item.id);
      } else {
        resData = await this.$api.questionnaire.detail(item.id);
      }
      msg();
      if (resData.data.resultCode === 20000) {
        this.questionDetailForm.questionDetailData =
          resData.data.resultData.bizQuestionTopicList.map(item => {
            let resultValue = null;
            if (item.type === '1') {
              resultValue =
                item.bizQuestionResponseList.length > 0
                  ? item.bizQuestionResponseList[0].responseOption
                  : '';
            } else if (item.type === '2') {
              resultValue = item.bizQuestionResponseList.map(item => {
                return item.responseOption;
              });
            } else if (item.type === '3') {
              resultValue =
                item.bizQuestionResponseList.length > 0
                  ? item.bizQuestionResponseList[0].responseContext
                  : '';
            }
            // console.log(item.type, resultValue)
            // 赋值校验使用
            const newObj = {
              value: resultValue,
              dataType: item.type === '2' ? 'array' : 'string'
            };
            return { ...item, ...newObj };
          });
        if (type === 'edit') {
          this.questionPeopleId = resData.data.resultData.bizQuestionPeopleList[0].id;
        }
        this.questionDetailModal = true;
        console.log(this.questionDetailForm.questionDetailData);
      } else {
        this.$Message.error('查看详情出错');
      }
    },
    // 管理列表--重置并搜索
    resetSearchForm() {
      this.searchForm.title = '';
      this.currentPageChange(1);
    },
    // 管理列表--分页
    currentPageChange(pageNo) {
      this.searchForm.currentPage = pageNo;
      this.getDataList();
    },
    getDataList() {
      this.$api.questionnaire.queryMyBizQuestion(this.searchForm).then(res => {
        if (res.data.resultCode === 20000) {
          this.dataList = res.data.resultData.data.map(item => {
            let responseText = '已填报';
            if (item.response == '0') {
              if (new Date() > new Date(item.lastTime)) {
                responseText = '超时';
              } else {
                responseText = '未填报';
              }
            }
            return {
              ...item,
              responseText: responseText
            };
          });
          this.total = res.data.resultData.recordsTotal;
        } else {
          this.dataList = [];
          this.total = 0;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.question-handle {
  height: calc(100vh - 210px);
  width: 100%;
  padding: 20px;
  background: #fff;
  .footer {
    padding-top: 15px;
    width: 100%;
    text-align: right;
  }
}
/deep/ .question-detail-modal {
  .question-detail-main {
    .ivu-form {
      margin-top: 20px;
    }
    .ivu-form-item-label {
      text-align: left;
    }
    .ivu-form-item-content {
      width: 750px;
    }
    .question-title {
      text-align: center;
    }
    .question-detail-header {
      overflow: hidden;
      padding: 0 5px;
      background: #eeeeee;
      .other-question-info {
        margin: 10px 0;
      }
    }
  }
}
</style>
