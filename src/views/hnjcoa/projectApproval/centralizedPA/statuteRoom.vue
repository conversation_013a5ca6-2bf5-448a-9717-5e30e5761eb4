<template>
  <div class="components">
    <Row justify="space-between">
      <Col span="24" class="filterCol">
        <span>
          <span style="margin-right: 10px">文件名称</span>
          <Input v-model="filterForm.name" placeholder="文件名称" style="width: 200px"></Input>
        </span>
        <span style="margin-left: 20px">
          <span style="margin-right: 10px">申报时间</span>
          <DatePicker
            :value="filterForm.time"
            format="yyyy-MM-dd"
            type="daterange"
            placement="bottom-end"
            placeholder="申报时间"
            style="width: 250px"
            @on-change="
              data => {
                this.filterForm.time = data;
              }
            "
          ></DatePicker>
        </span>
        <span style="margin-left: 20px">
          <span style="margin-right: 10px">申报年度</span>
          <DatePicker
            :value="filterForm.year"
            format="yyyy"
            type="year"
            placement="bottom-end"
            placeholder="申报年度"
            style="width: 100px"
            @on-change="
              data => {
                this.filterForm.year = data;
              }
            "
          ></DatePicker>
        </span>
        <span style="margin-left: 20px">
          <span style="margin-right: 10px">办理状态</span>
          <Select v-model="filterForm.status" style="width: 100px">
            <!-- <Option value="0">未申报</Option> -->
            <Option value="1">已申报</Option>
            <Option value="2">已驳回</Option>
            <Option value="3">已通过</Option>
          </Select>
        </span>
        <span style="margin-left: 20px">
          <span style="margin-right: 10px">申报单位</span>
          <Input
            v-model="filterForm.companyName"
            placeholder="申报单位"
            style="width: 200px"
          ></Input>
        </span>

        <Button type="primary" @click="handlePageChange(1)" style="margin: 0 10px">查询</Button>
        <Button type="default" @click="reset">重置</Button>
      </Col>
      <Col span="22" class="filterCol">
        <Button
          type="primary"
          v-if="batchType !== 'exportFile'"
          style="margin: 0 10px"
          @click="batch('check')"
          >批量审批</Button
        >
        <Button
          type="default"
          v-if="batchType !== 'check'"
          style="margin: 0 10px"
          @click="batch('exportFile')"
          >批量导出</Button
        >
        <template v-if="batchType !== '' && !batchCheckShow">
          <Button type="primary" style="margin: 0 10px" @click="confirmBatch">确定</Button>
          <Button type="default" style="margin: 0 10px" @click="cancelBatch">取消</Button>
        </template>
        <!-- <Button type="default" @click="reset">重置</Button> -->
      </Col>
    </Row>

    <Row>
      <Col span="24">
        <Table border stripe :columns="columns" :data="tableData" ref="tableData">
          <template slot="check" slot-scope="{ row }">
            <Checkbox  :disabled="batchType == 'check' && row.status == 2"
              v-model="row.checked"
              @on-change="data => checkedChange(data, row)"
            ></Checkbox>
          </template>
          <template slot="myindex" slot-scope="data">
            <!-- {{ index.status }} -->
            {{ data.index + (currentPageIndex - 1) * pageSize + 1 }}
          </template>
          <template slot="approvalFileName" slot-scope="{row}">
            <a @click="lookDetail(row)">{{ row.approvalFileName }}</a>
          </template>
          <template slot="qrcode" slot-scope="data">
            <div @click="qrcode(data.row)">
              <qrCodeMain
                v-if="showQrCode(data.row)"
                :currentData="data.row"
                :width="30"
                :height="30"
              ></qrCodeMain>
            </div>
          </template>
          <template slot="declareTime" slot-scope="{ row }">
            {{ row.declareTime?.substring(0, 10) || '/' }}
          </template>
          <template slot="status" slot-scope="{ row }">
            <span class="statusDot" :style="{ background: formatStatus(row.status).color }"></span>
            {{ formatStatus(row.status).label }}
          </template>
          <template slot="fileModalityValue" slot-scope="{ row }">
            {{ row.fileModalityValue || '--' }}{{ formatShowData(row.fileModalityTypeSecValue) }}
          </template>
          <template slot="action" slot-scope="{ row }">
            <a
              v-if="row.status != '2' && row.status != '3'"
              class="oparetionBtm"
              @click="check(row)"
              >审批</a
            >
            <a v-if="row.status == '3'" class="oparetionBtm" @click="changeStatus(row)">调整</a>
            <template v-if="row.status == '2' || row.status == '3'">
              <!-- <a class="oparetionBtm" @click="lookDetail(row)">查看详情</a> -->
              <template v-if="row.status == '3'">
                <a class="oparetionBtm" @click="process(row)">流程环节</a>
              </template>
            </template>
            <!-- <a v-if="row.status == '3'" class="oparetionBtm" @click="qrcode(row)">发文码</a> -->
          </template>
        </Table>
      </Col>
    </Row>

    <!--分页-->
    <Row class="paging" type="flex" justify="end">
      <Page
        v-if="dataTotal > pageSize"
        :total="dataTotal"
        :page-size="pageSize"
        @on-change="handlePageChange"
        show-elevator
        show-total
        :current="currentPageIndex"
      ></Page>
    </Row>

    <detail
      :show.sync="detailShow"
      :currentData="currentData"
      :pageType="detailShowType"
      @success="searchFunc"
    ></detail>
    <process :show.sync="processShow" :currentData="currentData"></process>
    <check :show.sync="checkShow" :currentData="currentData" @success="searchFunc"></check>
    <batchCheck :show.sync="batchCheckShow" :data="checkedData" @success="cancelBatch"></batchCheck>
  </div>
</template>

<script>
import check from '../components/check.vue';
import detail from '../components/detail.vue';
import process from '../components/process.vue';
import qrCodeMain from '../components/qrCodeMain.vue';
import batchCheck from '../components/batchCheck.vue';
import mixins from '@/views/hnjcoa/projectApproval/mixin/index.js';
export default {
  data() {
    const resetFilterForm = () => {
      return {
        name: '',
        year: '',
        time: ['', ''],
        status: '',
        companyName: ''
      };
    };
    return {
      action: this.$util.getAjaxUrl('workflow') + '/api/enclosureFile/uploadFormFiles',
      headers: { Authorization: sessionStorage.token },
      columns: [],
      tableData: [],
      dataTotal: 0,
      pageSize: 10,
      currentPageIndex: 1,
      //
      resetFilterForm: resetFilterForm,
      filterForm: resetFilterForm(),
      //
      checkShow: false,
      detailShow: false,
      processShow: false,
      qrcodeShow: false,
      pageType: 'add',
      currentData: {},
      checkedData: [],
      batchType: '',
      batchCheckShow: false,
      detailShowType: ''
    };
  },

  mixins: [mixins],

  components: {
    check,
    detail,
    process,
    qrCodeMain,
    batchCheck
  },

  mounted() {
    this.setColumns();
    this.searchFunc();
  },

  methods: {
    showQrCode(row) {
      return row.status == '3';
    },
    handlePageChange(page) {
      this.currentPageIndex = page || this.currentPageIndex;
      this.searchFunc();
    },

    setColumns(columns) {
      this.$nextTick(() => {
        this.columns = [
          {
            title: '发文码',
            width: 80,
            slot: 'qrcode',
            align: 'center'
            // slot: 'approvalFileName'
          },
          // {
          //   title: '选择',
          //   width: 75,
          //   key: 'check',
          //   slot: 'check'
          // },
          {
            title: '序号',
            width: 55,
            key: 'myindex',
            slot: 'myindex',
            align: 'center'
          },
          {
            title: '文件名',
            key: 'approvalFileName',
            align: 'center',
            slot: 'approvalFileName'
          },
          {
            title: '发文规格',
            key: 'fileModalityValue',
            slot: 'fileModalityValue',
            align: 'center'
          },
          {
            title: '申报时间',
            key: 'declareTime',
            slot: 'declareTime',
            align: 'center'
          },
          {
            title: '申报单位',
            key: 'companyName',
            align: 'center'
            // slot: 'companyName'
          },
          {
            title: '联系人',
            key: 'contactPerson',
            align: 'center'
          },
          {
            title: '审批状态',
            key: 'status',
            slot: 'status',
            width: 120,
            align: 'center'
          },
          {
            title: '操作',
            key: 'action',
            slot: 'action',
            width: 150,
            align: 'center'
          }
        ];
      });
    },

    checkedChange(data, row) {
      const target = this.checkedData.filter(item => item.id === row.id);
      if (target.length === 0) {
        this.checkedData.push(row);
      } else {
        this.checkedData = this.checkedData.filter(item => item.id !== row.id);
      }
    },

    batch(type) {
      // 在第二个元素插入一个元数
      if (this.columns[1].key !== 'check') {
        this.columns.splice(1, 0, {
          title: '选择',
          width: 75,
          key: 'check',
          slot: 'check'
        });
      }
      this.batchType = type;
    },

    cancelBatch() {
      this.checkedData = [];
      this.batchType = '';
      this.columns.splice(1, 1);
      this.searchFunc();
    },

    confirmBatch() {
      if (this.batchType === 'check') {
        this.batchCheckShow = true;
      } else if (this.batchType === 'exportFile') {
        let path = `/api/tLawApprovalpro/exportChooseFile`;
        let baseUrl = this.$util.baseUrl;
        axios({
          method: 'post',
          url: baseUrl + path,
          responseType: 'blob',
          headers: {
            Authorization: sessionStorage.token
          },
          data: this.checkedData
        }).then(res => {
          this.cancelBatch();
          const link = document.createElement('a');
          let blob = new Blob([res.data]);
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = '集中立项（法规室）.xlsx'; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          if (res.status == 200) {
            this.$Message.success('下载成功');
          } else {
            this.$Message.error('下载失败');
          }
        });
      }
    },

    searchFunc() {
      let params = {
        currentPage: this.currentPageIndex,
        pageSize: this.pageSize,
        approvalFileName: this.filterForm.name,
        status: this.filterForm.status,
        year: this.filterForm.year,
        companyName: this.filterForm.companyName,
        startTime: this.filterForm.time[0],
        endTime: this.filterForm.time[1]
      };
      params.approvalType = 1; //"1集中立项 2临时立项"
      this.$api.projectApproval.tLawApprovalpro_queryAllPage(params).then(res => {
        this.dataTotal = res.data.resultData.recordsTotal;
        this.tableData = res.data.resultData.data;
        // 勾选已被选中的数据
        this.tableData.forEach((item, index) => {
          this.checkedData.forEach(item2 => {
            if (item.id === item2.id) {
              this.$set(this.tableData[index], 'checked', true);
            }
          });
        });
      });
    },

    reset() {
      console.log(this.resetFilterForm());
      this.filterForm = this.resetFilterForm();
      this.searchFunc();
    },

    check(row) {
      console.log('row', row);
      this.currentData = row;
      this.checkShow = true;
    },

    lookDetail(row) {
      this.currentData = row;
      this.detailShow = true;
      this.detailShowType = 'lookDetail';
    },

    changeStatus(row) {
      this.currentData = row;
      this.detailShow = true;
      this.detailShowType = 'changeType';
    },

    process(row) {
      this.currentData = row;
      this.processShow = true;
    },

    qrcode(row) {
      this.currentData = row;
      this.qrcodeShow = true;
    },

    // 附件预览
    previewItem(item) {
      console.log(item);
      this.previewWpsOnline(item);
    },
    // 确认删除附件
    sureDelete(item) {
      this.$api.notice.deleteFormFiles(item.id).then(res => {
        if (res.data.resultCode == 20000) {
          this.$refs.upload.deleteSuccess();
        }
      });
    },
    downloadItem(item) {
      let msg = this.$Message.loading({
        content: '正在下载',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      axios({
        method: 'get',
        url: baseUrl + `/api/enclosureFile/downloadFormFiles/${item.id}`,
        responseType: 'blob',
        headers: {
          Authorization: sessionStorage.token
        }
      })
        .then(res => {
          const link = document.createElement('a');
          let blob = new Blob([res.data]);
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = item.fileName; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          if (res.status == 200) {
          } else {
            this.$Message.error('下载失败');
          }
        })
        .finally(() => {
          msg();
        });
    },
    exportFile() {
      let path = `/api/tLawApprovalpro/exportFile`;
      path += `?currentPage=${this.currentPageIndex}&pageSize=${this.pageSize}`;
      path += `&approvalFileName=${this.filterForm.name}&status=${this.filterForm.status}`;
      path += `&year=${this.filterForm.year}&companyName=${this.filterForm.companyName}`;
      path += `&startTime=${this.filterForm.time[0]}&endTime=${this.filterForm.time[1]}`;
      let baseUrl = this.$util.baseUrl;
      axios({
        method: 'get',
        url: baseUrl + path,
        responseType: 'blob',
        headers: {
          Authorization: sessionStorage.token
        }
      }).then(res => {
        const link = document.createElement('a');
        let blob = new Blob([res.data]);
        link.style.display = 'none';
        link.href = URL.createObjectURL(blob);
        link.download = '集中立项（法规室）.xlsx'; //下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        if (res.status == 200) {
        } else {
          this.$Message.error('下载失败');
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.filterCol {
  margin-bottom: 10px;
}

.oparetionBtm {
  margin-right: 10px;
}
.statusDot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
</style>
