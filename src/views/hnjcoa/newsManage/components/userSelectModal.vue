<template>
  <div class="user-select">
    <Modal
      v-model="userSelectModal"
      :transfer="false"
      :mask-closable="false"
      width="80%"
    >
      <div class="modal-header" slot="header">选择用户</div>
      <CommonFilterBar :filterArr="filterArr" @search="testSearch">
        <div class="select-person-num">
          已选
          <span>{{ userList.length }}</span>
          人
        </div>
      </CommonFilterBar>

      <div class="modal-content">
        <div class="content-item" v-scrollBar>
          <Tree :data="deptTree" :check-strictly="true" :render="personContent">
          </Tree>
        </div>
        <div class="content-item" v-scrollBar>
          <div v-if="userOptions.length > 0">
            <CellGroup
              v-for="(item, key) in userOptions"
              :key="key"
              @on-click="userClick(item)"
            >
              <Cell>
                <Avatar
                  icon="ios-person"
                  class="margin-right-10"
                  size="small"
                  style="background: rgb(26, 118, 211)"
                />
                <span class="name-text">{{ item.realName }}</span>
              </Cell>
            </CellGroup>
          </div>
          <div v-else>
            <Icon type="ios-alert-outline" color="#FF9900" />该机构下无可选人员
          </div>
        </div>
        <div class="select-button" v-if="!isMessageAudit">
          <div class="select-button-wrapper">
            <Button type="primary" @click="handleSelectAll">全选</Button>
            <Button type="primary" @click="handleClear">清空</Button>
          </div>
        </div>
        <div class="content-item" v-scrollBar ref="userList">
          <ul class="transferUl">
            <li
              v-for="(item, index) in userList"
              :key="index"
              class="user-item"
            >
              <span class="name-text">{{ item.userName }}</span>
              <Icon
                type="ios-close-circle-outline"
                color="#E03535"
                size="20"
                @click="delUserList(item, index)"
              />
            </li>
          </ul>
        </div>
      </div>
      <div class="modal-footer" slot="footer">
        <Button @click="cancel">取消</Button>
        <Button type="primary" @click="sureSelect">确认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import CommonFilterBar from "@/views/hnjcoa/components/commonFilterBar.vue";
export default {
  components: {
    CommonFilterBar,
  },
  props: {
    isMessageAudit: {
      type: Boolean,
      default: false,
    },
    defaultApi: {
      type: Array,
      default: () => {
        return ["org", "selectOrgAndQueryRealNameList"];
      },
    },
    orgApi: {
      type: Array,
      default: () => {
        return ["org", "pageQueryUserList"];
      },
    },
  },
  data() {
    return {
      filterArr: [
        {
          label: "姓名",
          name: "realName",
          value: "",
          labelWidth: 40,
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
      ],
      filterForm: {},
      userSelectModal: false,
      deptTree: [],
      userOptions: [],
      userList: [],
    };
  },
  mounted() {
    this.getDeptTree(0);
  },
  methods: {
    testSearch(params) {
      this.filterForm = JSON.parse(JSON.stringify(params));
      this.getDeptTree(0);
    },
    handleSelectAll() {
      // 要选进去的数组：this.userOptions
      // 已有的数组：this.userList
      let arr1 = this.userOptions.map((n) => {
        return {
          userId: n.id,
          userName: n.realName,
          companyId: n.companyId,
          companyName: n.companyName,
          departmentId: n.departmentId,
          departmentName: n.departmentName,
          mobile: n.mobile,
          post: n.post,
        };
      });
      let arr2 = JSON.parse(JSON.stringify(this.userList));
      if (arr1.length > 0) {
        if (arr2.length > 0) {
          let resArr = [];
          for (let i = 0; i < arr1.length; i++) {
            let arr3 = arr2.find((n) => {
              return n.userId == arr1[i].userId;
            });
            if (!arr3) {
              resArr.push(arr1[i]);
            }
          }
          this.userList.push(...resArr);
        } else {
          this.userList.push(...arr1);
        }
      } else {
        this.$Message.info("该机构下无可选人员");
      }
    },
    handleClear() {
      this.userList = [];
    },
    getDeptTree(id) {
      let params = {
        ...this.filterForm,
        parentId: id,
      };
      this.$api[this.defaultApi[0]][this.defaultApi[1]](params).then((res) => {
        this.deptTree = res.data.resultData.cscpOrgNode.map((el) => {
          return {
            ...el,
            expand: true,
          };
        });
        if (res.data.resultData.cscpUserDTOList) {
          this.userOptions = res.data.resultData.cscpUserDTOList;
        } else {
          this.userOptions = [];
        }
      });
    },
    resetForm() {
      this.filterForm = {
        realName: "",
      };
      this.getDeptTree(0);
    },
    personContent(h, { root, node, data }) {
      return h(
        "span",
        {
          style: {
            display: "inline-block",
            width: "100%",
            cursor: "pointer",
          },
          on: {
            click: () => {
              this.personClick(data);
            },
          },
        },
        [
          h("span", [
            h("Icon", {
              props: {
                // type: 'md-home'
              },
              style: {
                fontSize: "20px",
                marginRight: "8px",
                marginTop: "-3px",
              },
            }),
            h("span", data.title),
          ]),
        ]
      );
    },
    personClick(node) {
      let msg = this.$Message.loading("正在获取人员列表");
      this.$api[this.orgApi[0]]
        [this.orgApi[1]](node.id, {pageSize: 10000})
        .then((response) => {
          if (response.data.resultData) {
            this.userOptions = response.data.resultData.data;
          }
        })
        .finally(() => {
          msg();
        });
    },
    userClick(user) {
      let isEx = this.userList.filter((n) => {
        return n.userId == user.id;
      });
      if (isEx.length > 0) {
        this.$Message.error("该用户已被添加");
        return;
      }
      let u = {
        userId: user.id,
        userName: user.realName,
        companyId: user.companyId,
        companyName: user.companyName,
        departmentId: user.departmentId,
        departmentName: user.departmentName,
        mobile: user.mobile,
        post: user.post,
      };
      if (this.isMessageAudit) {
        this.userList = [];
        this.userList.push(u);
        return;
      }
      this.userList.push(u);
      this.$nextTick(() => {
        this.$refs.userList.scrollTop = this.$refs.userList.scrollHeight;
      });
    },
    delUserList(item, index) {
      this.userList.splice(index, 1);
    },
    sureSelect() {
      this.$emit("getUserList", this.userList);
      this.cancel();
    },
    cancel() {
      this.userList = [];
      this.userSelectModal = false;
      this.filterForm = {
        realName: "",
      };
    },
  },
};
</script>

<style lang="less" scoped>
.user-select {
  .modal-search {
    .ivu-form-item {
      margin-bottom: 10px;
    }
  }
  .modal-content {
    display: flex;
    height: 60vh;
    .select-button {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      height: 100%;
      line-height: 100%;
      &-wrapper {
        button {
          display: block;
          margin-bottom: 20px;
        }
      }
    }
    .content-item {
      flex: 1;
      position: relative;
      margin-right: 20px;
      padding: 10px 10px;
      overflow-x: hidden;
      border: 1px solid rgb(192, 191, 191);
      border-radius: 5px;
      &:first-child {
        flex: 0.7;
      }
      &:nth-child(2) {
        flex: 0.5;
      }
      .transferUl > li {
        display: inline-flex;
        min-width: 100px;
        justify-content: space-between;
        align-items: center;
        list-style: none;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin-bottom: 10px;
        margin-right: 8px;
        line-height: 1.5;
        padding: 0 5px;
      }
    }
  }
  .modal-footer {
    padding-right: 10px;
    text-align: right;
  }
  .name-text {
    margin-right: 5px;
  }
}
</style>
<style lang="less">
.user-select {
  .ivu-tree {
    background: #fff;
    padding: 0 !important;
  }
  .select-person-num {
    float: right;
    font-size: 16px;
    > span {
      color: #e03535;
    }
  }
}
</style>
