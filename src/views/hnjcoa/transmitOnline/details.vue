<template>
  <div class="detailsTransmitOnline">
    <div :class="`circulate-content ${pageType == 'page' ? 'mh' : 'mm'}`">
      <div class="circulate-head">
        <div class="head-title">{{ articleLookForm.title }}</div>
        <div class="head-notice">
          <span class="notice-create"
                style="font-weight: bold;color:red;">【{{ articleLookForm.durationClassificationName }}】</span>
          <span class="notice-create">发布单位：{{ articleLookForm.publishUnit }}</span>
          <span class="notice-create">发布时间：{{ articleLookForm.createTime }}</span>
        </div>
      </div>
      <div
        v-show="articleLookForm.enclosureFiles&&articleLookForm.enclosureFiles.length>0"
        class="circulate-file"
      >
        <div class="file-title">附件</div>
        <div class="file-text file1">
          <div class="beau">
            <ArticleUpload
              ref="enclosureUpload"
              :data="{ formDataId: articleLookForm.id }"
              :isDownload="true"
              :isLooking="false"
              :isPrint="articleLookForm.isPrint"
              :maxFileNum="10"
              :multiple="true"
              btnText="添加附件"
              @downloadFile="downloadItem"
              @previewItem="previewItem"
            ></ArticleUpload>
          </div>
        </div>
      </div>
      <div v-if="articleLookForm.remarks" class="circulate-body">
        <div class="circulate-title">备注信息</div>
        <div class="circulate-text">
          {{ articleLookForm.remarks }}
        </div>
      </div>
      <div v-if="articleLookForm.formDataId" class="circulate-file-zhengwen">
        <div class="file-title">处理单</div>
        <div class="file-text">
          <div :title="articleLookForm.titleFormdata" class="title" @click="handleTodo">
            {{ articleLookForm.titleFormdata }}
          </div>
        </div>
      </div>
      <div v-if="receive" class="circulate-table">
        <div class="table-title">发布范围</div>
        <div>
          <span v-for="item in tableData" :key="item.id">{{ item.receiptUserName }}、</span><span style="color: #409EFF">共{{ dataCount }}人</span>
        </div>
      </div>
      <div v-if="!receive" class="circulate-table">
        <div class="table-title">查阅统计</div>
        <div class="pane-tab">
          <div :class="`tab ${signInStatus == '100' ? 'cur' : ''}`" @click="getTableList('100')">
            送达{{ circulatetotal }}人
          </div>
          <div
            :class="`tab ${signInStatus == 1 ? 'cur' : ''}`"
            @click="getTableList(1)"
          >
            {{ noClick }}人未打开
          </div>
          <div
            :class="`tab ${signInStatus == 2 ? 'cur' : ''}`"
            @click="getTableList(2)"
          >
            {{ noSign }}人未签收
          </div>
        </div>
        <div class="table">
          <Form :label-width="70" :model="query" inline @keydown.enter.native="querySubmit">
            <FormItem :label-width="40" label="姓名">
              <Input v-model="query.userName" clearable></Input>
            </FormItem>
            <FormItem :label-width="40" label="状态">
              <Select v-model="query.receiptStatus" style="width:200px">
                <Option value="">全部</Option>
                <Option value="0">待签收</Option>
                <Option value="1">已签收</Option>
              </Select>
            </FormItem>
            <FormItem :label-width="0">
              <Button class="mr10" oaType="oaBlue" @click="querySubmit">查询</Button>
              <Button oaType="white" @click="resetQuery">重置</Button>
            </FormItem>
            <div class="tableright">
              <Table :columns="columns" :data="tableData" :loading="loading" border stripe>
              </Table>
              <div class="page-div">
                <Page
                  v-if="dataCount > pageSize"
                  :current="page"
                  :page-size="pageSize"
                  :total="dataCount"
                  show-elevator
                  show-total
                  style="text-align: right;"
                  @on-change="handlePageChange"
                />
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
    <div v-if="receive" slot="footer" class="circulate-footer">
      <Button @click="$emit('close')">关闭</Button>
      <Button class="ml10" type="primary" v-if="articleLookForm.receiptStatus==0" @click="handleSave">确认签收</Button>
    </div>
    <!-- 确认已阅弹框 -->
    <Modal
      v-model="isSureRead"
      :mask-closable="false"
      :transfer="false"
      :width="35"
      @on-cancel="isSureRead=false"
    >
      <template slot="header">确认签收</template>
      <div>确认签收吗？</div>
      <!--      <div class="class-content" v-scrollBar>-->
      <!--        <Form :model="readReplyDTO" ref="sureReadForm" :label-width="80">-->
      <!--          <FormItem label="回复内容">-->
      <!--            <Input-->
      <!--              v-model="readReplyDTO.replyContent"-->
      <!--              type="textarea"-->
      <!--              :autosize="{ minRows: 4, maxRows: 5 }"-->
      <!--              placeholder="请输入回复内容"-->
      <!--            ></Input>-->
      <!--          </FormItem>-->
      <!--        </Form>-->
      <!--      </div>-->
      <div slot="footer" class="class-footer">
        <Button class="class-btn" @click="isSureRead=false">取消</Button>
        <Button :loading="loading3" class="class-btn" type="primary" @click="handleSave"
        >确定
        </Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import {exportFile} from '@/utils/demoWeb';
import debounce from 'lodash/debounce';
import ArticleUpload from '@/views/hnjcoa/components/upload.vue';
import transferSwwd from "@/api/transferSwwd";

export default {
  components: {
    ArticleUpload,
  },
  props: {
    pageType: {
      type: String,
      default: ''
    },
    receive: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      routeName: '', //当前路由名称
      query: {
        userName: '',
        receiptStatus: ''
      },
      // 表单数据
      articleLookForm: {},
      dataCount: 0,
      pageSize: 10,
      page: 1,
      tableData: [],
      currentPage: 1,
      columns: [
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h('span', params.index + (this.currentPage - 1) * 10 + 1);
          }
        },
        {
          title: '姓名',
          key: 'receiptUserName',
          tooltip: true
        },

        {
          title: '部门',
          key: 'receiptUnitName',
          tooltip: true,

          align: 'center'
        },
        // {
        //   title: '回复内容',
        //   key: 'documentReply',
        //   align: 'center',
        //   slot: 'branchName'
        // },
        {
          title: '首次打开时间',
          key: 'openTime',
          align: 'center',
          render: (h, params) => {
            return h('span', params.row.openTime ? params.row.openTime : '-');
          }
        },
        {
          title: '签收时间',
          key: 'receiptTime',
          align: 'center',
          render: (h, params) => {
            return h('span', params.row.receiptTime ? params.row.receiptTime : '-');
          }
        }
      ],
      isJump: false,
      loading: false,
      reply: '1',
      // 表单数据
      isSureRead: false,
      readReplyDTO: {
        replyContent: '已阅'
      },
      documentsInfo: {},
      noClick: 0,
      noSign: 0,
      circulatetotal: 0,
      signInStatus: 100,
      loading3: false,
      action: this.$util.getAjaxUrl('workflow') + '/api/enclosureFile/uploadFormFiles',
      headers: {Authorization: sessionStorage.token}
    };
  },
  methods: {
    handleTodo() {
      let row = this.articleLookForm;
      let design = null;
      design = this.$router.resolve({
        name: 'form-render',
        query: {
          processDefinitionKey: row.processDefinitionKey,
          processInstanceId: row.processInstanceId || row.process_instance_id || row.procInstId,
          rootProcessInstanceId: row.rootProcessInstanceId,
          taskId: row.taskId || row.task_id,
          nodekey: row.taskDefinitionKey,
          formId: row.formId,
          status: 'finished',
          procTypeName: row.procTypeName,
          formDataId: row.formDataId || row.id
        }
      });
      this.openRenderScreen(true, design);
    },
    // 已阅状态
    changesignInStatus(id) {
      this.$api.transferSwwd.updatereceipt([{
        id: id,
        read: 1, // 阅读状态：0-未阅，1-已阅
        subId: this.articleLookForm.subId,
      }])
    },
    // 签收弹窗
    readNotice() {
      this.isSureRead = true;
    },
    // 确认签收
    handleSave() {
      this.loading = true;
      this.$api.transferSwwd.updatereceipt([{
        subId: this.articleLookForm.subId,
        id: this.articleLookForm.id,
        receiptStatus: 1, // "签收状态 0:未办 1:已阅已签收 2:已读未签收"
      }]).then((res) => {
        if (res.data.resultCode == 20000) {
          this.$Message.success('签收成功')
          this.$emit('close')
          this.$emit('getSelect')
        }
      }).catch(err => {
        this.loading = false;
        this.isSureRead = false;
      });
    },
    // 附件预览
    previewItem(item) {
      if (this.articleLookForm.isPrint == '0') {
        item.isTelegraph = true;
      } else {
        item.isTelegraph = false;
      }
      this.previewWpsOnline(item);
    },
    // 附件下载
    downloadItem(item) {
      const url = `/api/enclosureFile/downloadFormFiles/${item.id}`;
      const fileName =
        `${item.securityClassified ? `[${item.securityClassified}]` : ''}` + item.fileName;
      exportFile(url, fileName);
    },
    async initDetail(row = {}) {
      let that = this;
      const msg = this.$Message.loading({
        content: '正在加载数据',
        duration: 0
      });
      const res = await this.$api.transferSwwd.getreceipt(row.id, row.subId||100);
      if (this.receive) {
        this.changesignInStatus(row.id);
      }
      msg();
      try {
        const resultData = res.data.resultData || {};
        this.circulatetotal = res.data.resultData.sendCount
        this.noClick = res.data.resultData.unOpenCount
        this.noSign = res.data.resultData.uneceiptCount
        let articleLookForm = Object.assign({}, row, resultData);
        articleLookForm.title = this.removeHTMLTag(articleLookForm.title);
        this.articleLookForm = articleLookForm;
        this.articleLookForm.subId = row.subId;
        this.$set(this, 'articleLookForm', articleLookForm);
        this.getTableList();
        const enclosureFiles = await this.getFormFiles(row.id);
        if (enclosureFiles.length > 0) {
          this.articleLookForm.enclosureFiles = enclosureFiles;
        }
        that.$refs.enclosureUpload.uploadList = enclosureFiles;
      } catch (error) {
        throw new Error(error);
      }
    },
    async getFormFiles(id, type = 'enclosureFile') {
      let fileList = [];
      try {
        const {data = {}} = await this.$api.upload.getFormFiles(type, {
          formDataId: id
        });
        if (data.resultData && data.resultData.length > 0) {
          fileList = data.resultData;
        }
      } catch (error) {
        throw new Error(error);
      }

      return fileList;
    },
    querySubmit() {
      this.page = 1;
      this.getTableList(this.signInStatus);
    },
    handlePageChange(current) {
      this.page = current;
      this.currentPage = current;
      this.getTableList(this.signInStatus);
    },
    resetQuery() {
      this.query = {
        userName: '',
        createName: '',
        departmentName: '',
        processCreatTime: '',
        processDefinitionKey: ''
      };
      this.querySubmit();
    },
    async getTableList(signInStatus = 100) {
      this.signInStatus = signInStatus;
      this.loading = true;
      let params = {
        transferId: this.articleLookForm.id,
        pageSize: this.pageSize,
        receiptUserName: this.query.userName,
        receiptStatus: this.query.receiptStatus,
      }
      if (this.receive) {
        params.pageSize = 10000
      }
      if (signInStatus == 1) {
        params.openFlag = 0
      } else if (signInStatus == 2) {
        params.receiptStatus = 0
      }
      await this.$api.transferSwwd.queryTransferReadDetailPage(params).then(res => {
        if (res.data.resultCode == 20000) {
          let tableData = res.data.resultData.data;
          this.dataCount = res.data.resultData.recordsTotal;
          for (let item of tableData) {
            let createTime = '';
            createTime = moment(item.handleTime).format('YYYY-MM-DD HH:mm');
            item.createTime = createTime;
          }
          this.loading = false;
          this.tableData = tableData;
        }
        this.loading = false;
      });

      //this.loading = false;
    }
  }
};
</script>

<style lang="less" scoped>
.detailsTransmitOnline {
  .circulate-content {
    position: relative;
    padding: 10px 20px;
    height: calc(100% - 10px);
    overflow: auto;
    font-family: Microsoft YaHei;
    background-color: #fff;

    .circulate-head {
      text-align: center;

      .head-title {
        margin-bottom: 10px;
        font-size: 22px;
        font-weight: bold;
      }

      .head-notice {
        margin-bottom: 20px;
        font-size: 18px;

        .notice-create {
          margin-right: 40px;
          font-size: 14px;
          font-weight: 700;
          color: #95a5a6;
        }
      }
    }

    .circulate-body {
      max-width: 1360px;
      margin: 0 auto;
      // padding: 20px;
      .circulate-title {
        padding-left: 10px;
        border-left: 4px solid #ba0202;
        font-size: 18px;
        line-height: 18px;
      }

      .circulate-text {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 400;
        color: #1d1d26;
        line-height: 26px;
        white-space: pre-wrap;
      }
    }

    .circulate-file {
      max-width: 1360px;
      padding-top: 10px;
      padding-bottom: 10px;
      margin: 0 auto;

      .file-title {
        padding-left: 10px;
        border-left: 4px solid #ba0202;
        font-size: 18px;
        line-height: 18px;
      }

      .file-text {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        border-radius: 3px;
        font-size: 16px;
        color: #168ffd;
        text-decoration: underline;

        span {
          padding-right: 40px;
        }
      }

      .file1 {
        max-width: 920px;
      }
    }

    .circulate-file-zhengwen {
      max-width: 1360px;
      padding-top: 10px;
      padding-bottom: 10px;
      margin: 0 auto;

      .file-title {
        padding-left: 10px;
        border-left: 4px solid #ba0202;
        font-size: 18px;
        line-height: 18px;
      }

      .file-text {
        width: 920px;
        display: flex;

        border-radius: 3px;
        font-size: 16px;
        color: #168ffd;
        text-decoration: underline;
        cursor: pointer;
        justify-content: space-between;

        .title {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 920px;
        }

        .edit {
          width: 50px;
          margin-left: 6px;
          text-align: right;
        }
      }

      .file1 {
        max-width: 920px;
      }
    }

    .circulate-table {
      max-width: 1360px;
      margin: 0 auto;
      padding-top: 10px;
      padding-bottom: 10px;

      .table-title {
        padding-left: 10px;
        border-left: 4px solid #ba0202;
        font-size: 18px;
        line-height: 18px;
      }

      .pane-tab {
        // background: #fff0f1;
        display: flex;
        height: 60px;

        .tab {
          padding: 0 20px 0 20px;
          width: 160px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          font-size: 18px;
          color: #168ffd;
          cursor: pointer;
        }

        .cur {
          border-radius: 5px;
          background: rgba(234, 237, 240, 0.39);
        }
      }

      .table {
        padding-top: 10px;
      }
    }

    .readreply {
      max-width: 1360px;
      margin: 0 auto;
      padding-top: 10px;
      padding-bottom: 10px;

      .readreply-title {
        padding-left: 10px;
        border-left: 4px solid #ba0202;
        font-size: 18px;
        line-height: 18px;
      }

      .sureReadForm {
        margin-top: 10px;
      }
    }
  }

  .circulate-footer {
    border-top: 1px solid #e1e1e1;
    padding: 10px;
    display: flex;
    justify-content: flex-end;
    height: 50px;
    background: #fff;
  }

  .mh {
    height: 76vh;
    overflow-y: scroll;
  }

  .mm {
    height: 60vh;
    overflow-y: scroll;
  }
}

.user-list {
  margin: 10px 0;
  font-size: 18px;
}

.my-reply {
  margin-top: 10px;
  max-width: 1360px;
  margin: 0 auto;
  padding-top: 10px;
  padding-bottom: 10px;

  .my-reply-title {
    padding-left: 10px;
    border-left: 4px solid #ba0202;
    margin-bottom: 10px;
    font-size: 18px;
    line-height: 18px;
  }

  .my-reply-content {
    margin-top: 10px;
    display: flex;
    font-size: 16px;

    .content-label {
      // width: 80px;
      padding-left: 20px;
    }

    .content-content {
      flex: 1;
    }
  }

  .my-reply-file {
    margin-top: 10px;
    display: flex;
    font-size: 16px;

    .file-label {
      // width: 80px;
      padding-left: 20px;
    }

    .file-content {
      flex: 1;
    }
  }
}

.file-content {
  display: flex;
  flex-direction: column;
  width: 100%;

  .file-item {
    display: flex;
    align-items: center;
    // border-bottom: 1px solid #eee;
    // padding: 8px 0 0 0;
    cursor: pointer;

    i {
      font-style: normal;
    }

    span:first-child {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #3498db;

      &:hover {
        text-decoration: underline;
      }
    }

    span:nth-child(2) {
      margin-left: 20px;
      margin-right: 15px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #2949f0;

      &:hover {
        text-decoration: underline;
      }
    }

    span:last-child {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #3498db;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .file-item:last-child {
    border-bottom: 0 solid #eee;
  }
}

.icon-file {
  font-size: 18px;
  color: #02a7f0;
  margin-right: 10px;
  cursor: pointer;
  position: absolute;
  // right: 0;
  bottom: 0;
  z-index: 99;

  &:hover {
    text-decoration: underline;
  }
}
</style>
