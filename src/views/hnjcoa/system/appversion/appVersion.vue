<template>
  <div class="app-version components">
    <div class="app-version-wrapper">
      <CommonFilterBar
        :filterArr="filterArr"
        @search="testSearch"
        :labelWidth="60"
      >
        <Button
          type="primary"
          @click="handleAppVersionAdd(0)"
          style="float: right"
          icon="md-add"
        >新增</Button
        >
      </CommonFilterBar>

      <!-- 表格 -->
      <Table
        border
        stripe
        :columns="appVersionCol"
        :data="appVersionData"
        :loading="tableLoading"
        @on-selection-change="tableSelct"
      >
        <template slot-scope="{ row }" slot="forceUpdate">
          {{ row.forceUpdate == 1 ? "是" : "否" }}
        </template>
        <template slot-scope="{ row }" slot="commonUpdate">
          {{ row.commonUpdate == 1 ? "是" : "否" }}
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button oaType="oaDelete" @click="handleDelete(row)">删除</Button>
          <Button oaType="oaEdit" @click="handleAppVersionAdd(row)"
          >编辑</Button
          >
        </template>
      </Table>
      <!-- 分页 -->
      <Page
        :total="appVersionTotal"
        show-elevator
        show-total
        class="table-page"
        v-show="appVersionTotal >= 10"
        :current="appVersionPage"
        @on-change="changeAppVersionPage"
        :transfer="true"
        :page-size="pageSize"
      />
    </div>
    <!-- 编辑 / 新增 -->
    <Modal
      v-model="isAppVersion"
      :closable="false"
      :width="30"
      :mask-closable="false"
      :transfer="false"
    >
      <div class="appVersion-header" slot="header">
        {{ isEdit ? "编辑" : "新增" }}
      </div>
      <div class="appVersion-content">
        <!-- 新增表单 -->
        <Form
          :model="appVersionAddForm"
          :label-width="110"
          :rules="appVersionFormRules"
          ref="appVersionAddForm"
        >
<!--          <FormItem label="包名" prop="appPackageName">-->
<!--            <Input-->
<!--              v-model="appVersionAddForm.appPackageName"-->
<!--              placeholder="请输入包名"-->
<!--              class="input-50"-->
<!--            ></Input>-->
<!--          </FormItem>-->
<!--          <FormItem label="大小" prop="appSize">-->
<!--            <Input-->
<!--              v-model="appVersionAddForm.appSize"-->
<!--              placeholder="请输入大小"-->
<!--              class="input-50"-->
<!--            ></Input>-->
<!--          </FormItem>-->
          <FormItem label="版本号" prop="appVersionCode">
            <Input
              v-model="appVersionAddForm.appVersionCode"
              placeholder="请输入版本号"
              class="input-50"
            ></Input>
          </FormItem>
          <FormItem label="版本名" prop="appVersionName">
            <Input
              v-model="appVersionAddForm.appVersionName"
              placeholder="请输入版本名"
              class="input-50"
            ></Input>
          </FormItem>
          <FormItem label="下载地址" prop="downloadUrl">
            <Input
              v-model="appVersionAddForm.downloadUrl"
              placeholder="请输入下载地址"
              class="input-50"
            ></Input>
          </FormItem>
          <FormItem label="是否强制更新" prop="forceUpdate">
            <RadioGroup v-model="appVersionAddForm.forceUpdate" type="button">
              <Radio label="1">是</Radio>
              <Radio label="0">否</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="是否通用" prop="commonUpdate">
            <RadioGroup v-model="appVersionAddForm.commonUpdate" @on-change="clearUpdateScope" type="button">
              <Radio label="1">是</Radio>
              <Radio label="0">否</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem
            v-if="appVersionAddForm.commonUpdate == '0'"
            label="发布范围"
            prop="cscpAppUpdateScopeDTOList"
          >
            <Input
              @click.native="showSelectGroupModel"
              v-model="readMember"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 3 }"
              readonly
            />
          </FormItem>
          <FormItem label="升级内容" prop="updateContent">
            <Input
              v-model="appVersionAddForm.updateContent"
              placeholder="请输入升级内容"
              class="input-50"
            ></Input>
          </FormItem>
        </Form>
      </div>
      <div class="appVersion-footer" slot="footer">
        <Button
          type="primary"
          @click="handleSave"
          class="appVersion-btn"
          :loading="loading"
        >保存</Button
        >
        <Button class="appVersion-btn" @click="handleCancel">取消</Button>
      </div>
    </Modal>
    <tenantUnitSelect
      v-if="appVersionAddForm.commonUpdate == '0'"
      ref="userSelect"
      :show-tab="['tenantUnit']"
      @getUserList="getUserList"
    ></tenantUnitSelect>
  </div>
</template>

<script>
import CommonFilterBar from "@/views/hnjcoa/components/commonFilterBar.vue";
import tenantUnitSelect from "@/views/components/userSelectModal/index.vue";
import { deepCopy } from "view-design/src/utils/assist";
export default {
  name: "app-version",
  components: {
    CommonFilterBar,
    tenantUnitSelect,
  },
  data() {
    const validateScope = (rule, value, callback) => {
      if (this.appVersionAddForm.cscpAppUpdateScopeDTOList.length <= 0) {
        callback(new Error("请选择发布范围"));
        return;
      } else {
        callback();
        return;
      }
    };
    return {
      filterArr: [
        {
          label: "版本号",
          name: "appVersionCode",
          value: "",
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
        {
          label: "版本名",
          name: "appVersionName",
          value: "",
          type: 1,
        },
      ],
      filterForm: {},
      tableLoading: false,
      isEdit: false,
      isAppVersion: false,
      loading: false,
      appVersionPage: 1,
      pageSize: 10,
      appVersionTotal: 0,
      appVersionCol: [
        {
          title: "序号",
          width: 70,
          align: "center",
          render: (h, params) => {
            return h(
              "span",
              params.index + (this.appVersionPage - 1) * this.pageSize + 1
            );
          },
        },
        // {
        //   title: "包名",
        //   key: "appPackageName",
        //   align: "center",
        // },
        // {
        //   title: "大小",
        //   key: "appSize",
        //   align: "center",
        // },
        {
          title: "版本号",
          key: "appVersionCode",
          align: "center",
          width: 120
        },
        {
          title: "版本名",
          key: "appVersionName",
          align: "center",
          width: 120
        },
        {
          title: "下载地址",
          key: "downloadUrl",
          align: "center",
          minWidth: 200
        },
        {
          title: "是否强制更新",
          slot: "forceUpdate",
          align: "center",
          width: 150
        },
        {
          title: "是否通用",
          slot: "commonUpdate",
          align: "center",
          width: 110
        },
        {
          title: "创建时间",
          key: "createTime",
          align: "center",
          width: 190
        },
        // {
        //   title: "升级内容",
        //   key: "updateContent",
        //   align: "center",
        //   tooltip: true,
        // },
        {
          title: "操作",
          slot: "action",
          align: "center",
          width: 160
        },
      ],
      appVersionData: [],
      selectData: [],
      // 表单
      appVersionAddForm: {
        appPackageName: "",
        appSize: "",
        appVersionCode: "",
        appVersionName: "",
        downloadUrl: "",
        forceUpdate: "0",
        updateContent: "",
        commonUpdate: "1",
        cscpAppUpdateScopeDTOList: [],
      },
      appVersionFormRules: {
        // appPackageName: [
        //   {
        //     required: true,
        //     message: "请输入包名",
        //     trigger: "blur",
        //   },
        // ],
        // appSize: [
        //   {
        //     required: true,
        //     message: "请输入大小",
        //     trigger: "blur",
        //   },
        // ],
        appVersionCode: [
          {
            required: true,
            message: "请输入版本号",
            trigger: "blur",
          },
        ],
        appVersionName: [
          {
            required: true,
            message: "请输入版本名",
            trigger: "blur",
          },
        ],
        downloadUrl: [
          {
            required: true,
            message: "请输入下载地址",
            trigger: "blur",
          },
        ],
        forceUpdate: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        commonUpdate: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        updateContent: [
          {
            required: true,
            message: "请输入升级内容",
            trigger: "blur",
          },
        ],
        cscpAppUpdateScopeDTOList: [
          {
            required: true,
            validator: validateScope,
            trigger: "blur",
          },
        ],
      },
      readMember: "",
    };
  },
  mounted() {
    this.changeAppVersionPage(1);
  },
  methods: {
    clearUpdateScope(){
      this.readMember = '';
      this.appVersionAddForm.cscpAppUpdateScopeDTOList = [];
    },
    getUserList(userList) {
      this.appVersionAddForm.cscpAppUpdateScopeDTOList = userList;
      this.readMember = userList
        .map((n) => {
          return n.title;
        })
        .join(", ");
      this.$refs.appVersionAddForm.validateField("cscpAppUpdateScopeDTOList");
    },
    showSelectGroupModel() {
      let selectedUserList = [];
      if (
        this.appVersionAddForm.cscpAppUpdateScopeDTOList &&
        this.appVersionAddForm.cscpAppUpdateScopeDTOList.length > 0
      ) {
        selectedUserList = deepCopy(
          this.appVersionAddForm.cscpAppUpdateScopeDTOList
        );
      }
      this.$refs.userSelect.showUserSelect(selectedUserList);
    },
    testSearch(params) {
      this.filterForm = JSON.parse(JSON.stringify(params));
      this.changeAppVersionPage(1);
    },
    // 翻页
    changeAppVersionPage(page) {
      if (page) {
        this.appVersionPage = page;
      }
      const params = {
        currentPage: this.appVersionPage,
        pageSize: this.pageSize,
        ...JSON.parse(JSON.stringify(this.filterForm)),
      };
      this.getAppVersion(params);
    },
    // 请求
    getAppVersion(params) {
      // return;
      this.tableLoading = true;
      this.$api.app.queryCscpAppEditionPage(params).then((res) => {
        this.tableLoading = false;
        this.appVersionTotal = res.data.resultData.recordsTotal;
        this.appVersionData = res.data.resultData.data;
      });
    },
    // 新增 / 编辑
    handleAppVersionAdd(row) {
      if (row) {
        this.$api.app.findOne(row.id).then((res) => {
          // console.log(res);
          row = res.data.resultData;
          // 编辑
          this.isEdit = true;
          // row.appSize = row.appSize.toString();
          this.appVersionAddForm = JSON.parse(JSON.stringify(row));
          if (row.cscpAppUpdateScopeDTOList && row.cscpAppUpdateScopeDTOList.length > 0) {
            let userList = [...row.cscpAppUpdateScopeDTOList];
            userList = userList.map(v=> {
              v.title = v.companyName;
              v.id = v.companyId;
              return v
            });
            this.getUserList(userList);
          }
        });
      } else {
        // 新增
        this.appVersionAddForm.cscpAppUpdateScopeDTOList = [];
        this.appVersionAddForm = this.$options.data().appVersionAddForm;
        this.isEdit = false;
      }
      this.isAppVersion = true;
    },
    // 提交
    handleSave() {
      this.$refs.appVersionAddForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          let http = this.isEdit
            ? this.$api.app.update(this.appVersionAddForm)
            : this.$api.app.create(this.appVersionAddForm);
          http
            .then((res) => {
              if (res.data.resultCode == 20000) {
                this.$Message.success(this.isEdit ? "更新成功" : "新增成功");
                this.changeAppVersionPage(this.appVersionPage);
                this.handleCancel();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    // 取消
    handleCancel() {
      this.$refs.appVersionAddForm.resetFields();
      this.isAppVersion = false;
    },
    // 删除
    handleDelete(row, status) {
      this.$Modal.confirm({
        title: "警告",
        content: `确定删除包 ${row.appPackageName} 吗？`,
        loading: true,
        onOk: () => {
          this.$nextTick(() => {
            this.$api.app
              .delete(row.id)
              .then((res) => {
                res.data.resultCode == 20000 &&
                this.$Message.success("删除成功");
                this.changeAppVersionPage(this.appVersionPage);
              })
              .finally((error) => {
                this.$Modal.remove();
              });
          });
        },
      });
    },
    // 选择
    tableSelct(row) {
      this.selectData = row;
    },
  },
};
</script>
