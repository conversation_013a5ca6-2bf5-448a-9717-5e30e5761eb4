/* * 新增工作安排 * @Author: chenxize * @Date: 2022-11-28 09:14:53 * @Last Modified by: chenxize *
@Last Modified time: 2022-12-01 16:49:58 */
<template>
  <div class="workArrange-add components">
    <remote-css href="/css/all.css"></remote-css>
    <remote-css href="/css/bootstrap.min.css"></remote-css>
    <Tabs type="card" :value="tabName" @on-click="tabClick">
      <TabPane
        v-for="item in workArrangeLevel"
        :key="item.code"
        :label="item.name"
        :name="item.code"
      />
    </Tabs>
    <div class="fullCalendar-wrapper">
      <div class="fullCalendar-loading-mask" v-show="loading">
        <Spin size="large" fix></Spin>
      </div>
      <FullCalendar ref="fullCalendar" :options="calendarOptions" />
      <Dropdown
        @on-click="dropClick"
        transfer
        placement="right-start"
        trigger="custom"
        ref="contextMenu"
        :visible="currentVisible"
        @on-clickoutside="handleCancel"
      >
        <DropdownMenu slot="list">
          <DropdownItem name="edit" v-if="tabName == '3'">编辑</DropdownItem>
          <DropdownItem name="delete">删除</DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
  </div>
</template>

<script>
// import "@fullcalendar/core/vdom";
// import "@fullcalendar/common";
import FullCalendar from '@fullcalendar/vue';
import bootstrapPlugin from '@fullcalendar/bootstrap';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import moment from 'moment';
import { calendarFormatter } from '@/libs/calendarFormatter.js';
export default {
  name: 'workArrangeAdd',
  components: {
    FullCalendar,
    'remote-css': {
      render(createElement) {
        return createElement('link', {
          attrs: { rel: 'stylesheet', href: this.href }
        });
      },
      props: {
        href: { type: String, required: true }
      }
    },
    'remote-js': {
      render(createElement) {
        return createElement('script', {
          attrs: { type: 'text/javascript', src: this.src }
        });
      },
      props: {
        src: { type: String, required: true }
      }
    }
  },
  data() {
    return {
      tabName: '1',
      loading: false,
      workArrangeLevel: [
        {
          code: '1',
          name: '全办'
        },
        {
          code: '2',
          name: '处室'
        },
        {
          code: '3',
          name: '个人'
        }
      ],
      calendarOptions: {
        height: '650px',
        allDaySlot: false, //是否选择全天
        allDayText: '全天', //全天显示文本
        themeSystem: 'bootstrap', // 主题色(本地测试未能生效)
        // 自定义渲染视图
        views: {
          dayGridMonth: {
            titleFormat: { year: 'numeric', month: 'long' }
            // dayCellContent(item) {
            //   let mark = sessionStorage.getItem("joinholiday");
            //   let _date = new Date(item.date).toLocaleDateString().split("/");
            //   let _dateF = calendarFormatter.solar2lunar(
            //     _date[0],
            //     _date[1],
            //     _date[2]
            //   );
            //   let _datelunarFestival = _dateF.lunarFestival
            //     ? _dateF.lunarFestival
            //     : "";
            //   let datefestival = _dateF.festival ? _dateF.festival : "";
            //   if (item.dayNumberText == mark + "日") {
            //     return {
            //       html: `<p  style="padding-left: 4px;"><label>${_dateF.cDay}</label></p><p  style="padding-left: 4px;font-size: 11px;">(<span>${_dateF.IDayCn}<b style="color: #c71010;;padding-left: 4px;">${_datelunarFestival}${datefestival}</b></span>)</p)>`,
            //     };
            //   } else
            //     return {
            //       html: `<p  style="padding-left: 4px;"><label>${_dateF.cDay}</label></p><p  style="padding-left: 4px;font-size: 11px;">(<span>${_dateF.IDayCn}<b style="color: #c71010;;padding-left: 4px;">${_datelunarFestival}${datefestival}</b></span>)</p)>`,
            //     };
            // },
          },
          timeGrid: {
            dayMaxEventRows: 3 // 当月份视图时。一天的事件多于6个时进行折叠
          }
        },
        // 引入的插件，比如fullcalendar/daygrid，fullcalendar/timegrid引入后才可显示月，周，日
        plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin, bootstrapPlugin],
        initialView: 'dayGridMonth', // 默认为那个视图（月：dayGridMonth，周：timeGridWeek，日：timeGridDay）
        firstDay: 1, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推
        locale: 'zh-cn', // 切换语言，当前为中文
        eventColor: '#3BB2E3', // 全部日历日程背景色
        initialDate: moment().format('YYYY-MM-DD'), // 自定义设置背景颜色时一定要初始化日期时间
        timeGridEventMinHeight: '80', // 设置事件的最小高度
        aspectRatio: 1.65, // 设置日历单元格宽度与高度的比例。
        // displayEventTime: false, // 是否显示时间
        allDay: false, // 周，日视图时，all-day 不显示
        headerToolbar: {
          left: 'prevYear prev today next nextYear',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
        },
        buttonText: {
          today: '今天',
          month: '月',
          week: '周',
          day: '日',
          list: '日程'
        },
        slotLabelFormat: {
          hour: '2-digit',
          minute: '2-digit',
          // hour12: false, // 设置时间为24小时
          hourCycle: 'h23'
        },

        eventTimeFormat: {
          hour: '2-digit',
          minute: '2-digit',
          hourCycle: 'h23'
        },

        // 事件
        eventClick: this.handleEventClick, // 点击日历日程事件
        eventDblClick: this.handleEventDblClick, // 双击日历日程事件 (这部分是在源码中添加的)
        eventClickDelete: this.eventClickDelete, // 点击删除标签事件 (这部分是在源码中添加的)
        eventDrop: this.eventDrop, // 拖动日历日程事件
        eventResize: this.eventResize, // 修改日历日程大小事件
        select: this.handleDateClick, // 选中日历格事件
        dateClick: this.handleTest, // 点击格事件
        unselect: this.handleUnselect, // 选中日历格事件
        eventDidMount: this.eventDidMount, // 安装提示事件
        // loading: this.loadingEvent, // 视图数据加载中、加载完成触发（用于配合显示/隐藏加载指示器。）
        // selectAllow: this.selectAllow, //编程控制用户可以选择的地方，返回true则表示可选择，false表示不可选择
        eventMouseEnter: this.eventMouseEnter, // 鼠标滑过
        eventMouseLeave: this.eventMouseLeave,
        // allowContextMenu: false,
        editable: true, // 是否可以进行（拖动、缩放）修改
        eventStartEditable: true, // Event日程开始时间可以改变，默认true，如果是false其实就是指日程块不能随意拖动，只能上下拉伸改变他的endTime
        eventDurationEditable: true, // Event日程的开始结束时间距离是否可以改变，默认true，如果是false则表示开始结束时间范围不能拉伸，只能拖拽
        selectable: false, // 是否可以选中日历格
        selectMirror: true,
        selectMinDistance: 0, // 选中日历格的最小距离
        dayMaxEvents: true,
        weekends: true,
        navLinks: true, // 天链接
        selectHelper: false,
        slotEventOverlap: false, // 相同时间段的多个日程视觉上是否允许重叠，默认true允许
        events: [],
        customButtons: {
          // 定义可在headerToolbar / footerToolbar中使用的自定义按钮
          today: {
            text: '今天',
            click: this.todayClick
          },
          next: {
            click: this.nextClick
          },
          prev: {
            click: this.prevClick
          },
          prevYear: {
            click: this.prevYearClick
          },
          nextYear: {
            click: this.nextYearClick
          }
        }
      },
      lastCheak: '',
      nowCheakItemId: '',
      userId: '',
      userName: '',
      calendarApi: '',
      currentTime: '',

      editEvent: '',

      posX: 0,
      posY: 0,
      currentVisible: false,
      locator: null,
      trigger: 'custom',
      tipStart: '',
      tipEnd: ''
    };
  },
  computed: {
    locatorStyle() {
      return {
        position: 'fixed',
        left: `${this.posX}px`,
        top: `${this.posY}px`
      };
    }
  },
  mounted() {
    this.calendarApi = this.$refs.fullCalendar.getApi();
    this.currentTime = moment(this.calendarApi.getDate()).format('YYYY-MM-DD 00:00:00');
    this.userId = this.$byStoreGet('userInfo').id;
    this.userName = this.$byStoreGet('userInfo').realName;
    this.getScheduleModal();
    document.addEventListener('contextmenu', this.handleContextmenu, true);
  },
  activated() {
    // // 表单填写完后恢复状态
    // if (
    //   this.$byStoreGet("workArrangeItem") &&
    //   this.$byStoreGet("workArrangeItem").defaultLevel
    // ) {
    //   this.tabName = this.$byStoreGet("workArrangeItem").defaultLevel;
    // }
    this.getScheduleModal();
  },
  destroyed() {
    document.removeEventListener('contextmenu', this.handleContextmenu, true);
    document.removeEventListener('mouseup', this.handleContextmenu, true);
  },
  methods: {
    // 来自首页的相关方法
    resize() {
      this.calendarOptions.events = [...this.calendarOptions.events];
    },
    // 点击tab
    tabClick(name) {
      this.tabName = name;
      this.getScheduleModal();
    },
    handleEventClick(e) {
      if (this.nowCheakItemId == e.event.id) {
        this.$byStoreSet('workArrangeItem', {
          oldParent: this.$route.name,
          // 0新增、1编辑、2查看
          status: 2,
          itemId: this.editEvent.id
        });
        this.$router.push({ name: 'workArrangeAddForm' });
      } else {
        this.lastCheak = '';
      }
      this.nowCheakItemId = e.event.id;
    },
    createLocator() {
      // 获取Dropdown
      const contextmenu = this.$refs.contextMenu;
      // 创建locator
      const locator = document.createElement('div');
      locator.style.cssText = `position:fixed;left:${this.posX}px;top:${this.posY}px`;
      document.body.appendChild(locator);
      // 将locator绑定到Dropdown的reference上
      contextmenu.$refs.reference = locator;
      this.locator = locator;
    },
    removeLocator() {
      if (this.locator) document.body.removeChild(this.locator);
      this.locator = null;
    },
    handleContextmenu({ button, clientX, clientY }) {
      if (button === 2) {
        if (this.posX !== clientX) this.posX = clientX;
        if (this.posY !== clientY) this.posY = clientY;
        if (this.trigger !== 'custom') {
          this.createLocator();
          this.currentVisible = true;
        }
      }
    },
    handleCancel() {
      this.currentVisible = false;
      this.removeLocator();
    },
    eventDidMount(e) {
      // 添加右键事件
      e.el.addEventListener('contextmenu', jsEvent => {
        jsEvent.preventDefault();
        document.addEventListener('contextmenu', this.handleContextmenu, true);
        document.addEventListener('mouseup', this.handleContextmenu, true);
      });
    },
    // 点击今天
    todayClick() {
      this.calendarApi.today();
      this.getScheduleModal();
    },
    // 点击上一月
    prevClick() {
      this.calendarApi.prev();
      this.getScheduleModal();
    },
    // 点击下一月
    nextClick() {
      this.calendarApi.next();
      this.getScheduleModal();
    },
    // 点击上一年
    prevYearClick() {
      this.calendarApi.prevYear();
      this.getScheduleModal();
    },
    // 点击下一年
    nextYearClick() {
      this.calendarApi.nextYear();
      this.getScheduleModal();
    },
    eventMouseLeave() {
      this.trigger = 'custom';
      // this.editEvent=null
    },
    // 获取日程
    async getScheduleModal() {
      this.currentTime = moment(this.calendarApi.getDate()).format('YYYY-MM-DD 00:00:00');
      let start = moment(this.currentTime).startOf('month').format('YYYY-MM-DD 00:00:00');
      let end = moment(this.currentTime).endOf('month').format('YYYY-MM-DD 23:59:59');
      let data = {
        startTime: start,
        endTime: end,
        level: this.tabName
      };
      this.loading = true;
      let res = await this.$api.workArrange.getCalendarData(data);
      this.loading = false;
      if (res.data.resultData) {
        res.data.resultData.data.forEach(e => {
          e.title = e.name;
          e.start = e.startTime;
          e.end = e.endTime;
          e.backgroundColor = '#2ecc71'; // 该事件的背景颜色
          e.borderColor = '#2ecc71'; // 该事件的边框颜色
          e.textColor = '#FFF'; // 该事件的文字颜色
        });
        this.calendarOptions.events = res.data.resultData.data;
      }
    },
    handleTimeShow(s, e) {
      let sYear = s.substring(0, 4),
        sMonth = s.substring(5, 10),
        sTime = s.substring(11, 16),
        eYear = e.substring(0, 4),
        eMonth = e.substring(5, 10),
        eTime = e.substring(11, 16);
      if (sYear == eYear) {
        if (sMonth == eMonth) {
          return `${sTime} - ${eTime}`;
        } else {
          return `${sMonth} ${sTime} - ${eMonth} ${eTime}`;
        }
      } else {
        return `${sYear}-${sMonth} ${sTime} - ${eYear}-${eMonth} ${eTime}`;
      }
    },
    eventMouseEnter(event, jsEvent, view) {
      this.trigger = 'right';
      this.editEvent = event.event;
      this.tipStart = moment(this.editEvent.start).format('YYYY-MM-DD HH:mm:00');
      this.tipEnd = moment(this.editEvent.end || this.editEvent.start).format(
        'YYYY-MM-DD HH:mm:00'
      );
      tippy(event.el, {
        interactive: true,
        content: this.handleTimeShow(this.tipStart, this.tipEnd) + '\n' + this.editEvent.title,
        animation: 'scale', //显示动画
        theme: 'light' //显示主题
      });
      // 鼠标划过的事件
      if (event.event.classNames.length) {
      }
    },
    eventDrop(event, dayDelta, minuteDelta, allDay, revertFunc, jsEvent, ui, view) {
      this.update(event);
    },
    // 点击单元格
    handleTest(arg) {
      if (this.lastCheak == arg.dateStr) {
        this.$byStoreSet('workArrangeItem', {
          oldParent: this.$route.name,
          // 0新增、1编辑、2查看
          status: 0,
          defaultLevel: this.tabName,
          defaultStartTime: this.parseTime(arg.date, '{y}-{m}-{d} {h}:{i}'),
          defaultEndTime: this.parseTime(arg.date, '{y}-{m}-{d}') + ' 23:59'
        });
        this.$router.push({ name: 'workArrangeAddForm' });
      } else {
        this.nowCheakItemId = '';
      }
      this.lastCheak = arg.dateStr;
    },
    // 选中单元格 进入 新增 (已关闭选中功能)
    handleDateClick(arg) {},
    handleUnselect({ view }) {},
    // 右键菜单事件
    dropClick(name) {
      if (name == 'edit') {
        this.$byStoreSet('workArrangeItem', {
          oldParent: this.$route.name,
          // 0新增、1编辑、2查看
          status: 1,
          itemId: this.editEvent.id
        });
        this.$router.push({ name: 'workArrangeAddForm' });
      } else if (name == 'delete') {
        this.delete(this.editEvent.id);
      }
    },
    // 删除
    async delete(id) {
      this.$Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗？`,
        loading: true,
        onOk: () => {
          this.$api.workArrange
            .delete(id)
            .then(res => {
              if (res.data.resultCode == '20000') {
                this.$Message.success('删除成功');
                this.getScheduleModal();
              }
            })
            .finally(error => {
              this.$Modal.remove();
            });
        }
      });
    },
    // 编辑日程
    async update(e) {
      let event = e.event;
      let data = {
        id: event.id,
        startTime: moment(event.start).format('YYYY-MM-DD HH:mm:00'),
        endTime: moment(event.end || event.start).format('YYYY-MM-DD HH:mm:00')
      };
      await this.$api.workArrange.update(data);
      this.getScheduleModal();
    }
  }
};
</script>
<style lang="less" scoped>
.workArrange-add {
  .fullCalendar-wrapper {
    position: relative;
    .fullCalendar-loading-mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }

  /deep/ .fc-scroller {
    border-bottom: 2px solid #dee2e6;
    overflow: hidden auto !important;
    th {
      border-bottom-width: 0px;
    }
  }

  /deep/ .fc-scroller-liquid-absolute {
    // overflow: hidden !important;
    border-bottom-width: 0px;
  }
  /deep/ .fc-daygrid-event {
    // white-space:normal !important;
  }
  .flex_start {
    position: relative;
    line-height: 30px;
    height: 30px;
    div {
      margin-right: 50px;
    }
  }
  --bk: transparent;
  .flag:after {
    content: ' ';
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--bk);
    margin-left: 10px;
    margin-top: 5px;
  }
  .fc-button-group,
  .fc button {
    display: inline;
  }
  .fc-h-event .fc-event-main,
  .fc-v-event .fc-event-main {
    /* color: #555; */
    font-size: 16px;
  }
  .fc table > thead > tr > th div {
    font-weight: bold;
    /* color: #25992e; */
    font-size: 16px;
  }
  /deep/ .fc .fc-popover {
    z-index: 1120;
  }
  /deep/ .fc-timegrid-slot-lane,
  /deep/ .fc-daygrid-day,
  /deep/ .fc-list-event {
    cursor: pointer;
    &:hover {
      background-color: rgba(#3bb2e3, 0.2);
    }
  }
  /deep/ .tippy-content {
    white-space: pre-wrap;
  }
  /deep/ .fc-daygrid-dot-event .fc-event-title {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  // /deep/ .fc-timegrid-event-harness {
  //   display: none;
  // }
}
</style>
