<template>
  <div class="push_org_record components">
    <CommonFilterBar :filterArr="filterArr" @search="testSearch" @reset="reset">
      <Button type="primary" @click="handleBulkPush()" icon="md-add" style="float: right" class="mb10">批量推送</Button>
    </CommonFilterBar>
    <!-- <div> -->
    <Table stripe border ref="table" :columns="columns" :data="dataList" :loading="tableLoading"
      @on-select="handleSelect" @on-select-all="handleSelect" :row-key="'id'" :max-height="600">
      <template #operateTypeSlot="{ row }">
        {{ row.strOperaType == 'add' ? '新增' : row.strOperaType == 'update' ? '修改' : row.strOperaType == 'delete'
          ?
          '删除'
          :
          '' }}
      </template>
      <template #inSystemFlagSlot="{ row }">
        {{ row.inSystemFlag == 0 ? '否' : row.inSystemFlag == 1 ? '是' : '' }}
      </template>
      <template #responseStatusSlot="{ row }">

        {{ row.syncSuccess == 'true' && (row.syncStatus == '200' || row.syncStatus == '0') ? '成功' :
          row.syncStatus ==
            '205' ? '推送中' : '失败' }}
      </template>
      <template #requestModeSlot="{ row }">
        {{ row.requestMode == '0' ? 'MQ' : row.requestMode == '1' ? 'HTTP' : '通知拉取' }}
      </template>

      <!-- <template #failedReasonSlot="{ row }">
        <span v-if="!(row.syncSuccess == 'true' && row.syncStatus == '200')">[{{row.syncStatus}}] {{row.syncMessage}}</span>
      </template> -->
      <template #actionSlot="{ row }">
        <Button oaType="oaEdit"
          v-if="(row.appCode == 'westone_uas_system' && isWw) || row.appCode != 'westone_uas_system'"
          @click="handlePushAgain(row)">再次推送</Button>
        <Button oaType="oaDelete" @click="handleDelete(row)">删除</Button>
      </template>
    </Table>
    <Page class="table-page" v-if="total > pageSize" :total="total" :page-size="pageSize" @on-change="handlePageChange"
      @on-page-size-change="pageSizeChange" show-sizer show-elevator show-total :current="currentPage"
      :page-size-opts="[10, 20, 50, 100, 200]" placement="top">
    </Page>
  </div>
  <!-- </div> -->
</template>

<script>
import CommonFilterBar from '@/views/hnjcoa/components/commonFilterBar.vue';
import { isWwBySysConfig } from "@/utils/demoWeb";
export default {
  components: {
    CommonFilterBar
  },
  data() {
    return {
      sevenDaysAgo: '',
      currentDate: '',
      filterArr: [
        {
          label: '系统名称',
          name: 'appName',
          value: '',
          labelWidth: 90,
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        },
        {
          label: '编号',
          name: 'appCode',
          value: '',
          labelWidth: 40,
          type: 1
        },
        {
          label: '操作类型',
          name: 'strOperaType',
          value: '',
          labelWidth: 70,
          type: 2,
          selectArr: [{ value: 'add', label: '新增' }, { value: 'update', label: '修改' }, { value: 'delete', label: '删除' }]
        },
        {
          label: '机构名称',
          name: 'strUnitName',
          value: '',
          labelWidth: 70,
          type: 1
        },
        {
          label: '响应状态',
          name: 'syncSuccess',
          value: '',
          labelWidth: 70,
          type: 2,
          selectArr: [{ value: 'true', label: '成功' }, { value: 'false', label: '失败' }, { value: 'ing', label: '推送中' }]
        },
        {
          label: "推送时间",
          name: "startEndTime",
          labelWidth: 70,
          value: "",
          type: 3,
        },
        {
          label: '数据来源Id',
          name: 'sourceId',
          value: '',
          labelWidth: 90,
          type: 1
        },
      ],
      filterForm: {},
      tableLoading: false,
      total: 0,
      currentPage: 1,
      pageSize: 100,
      dataList: [],
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h('span', params.index + (this.currentPage - 1) * this.pageSize + 1);
          }
        },
        {
          key: 'appName',
          title: '系统名称',
          align: 'center'
        },
        {
          key: 'appCode',
          title: '编号',
          align: 'center',
          width: 90
        },
        {
          key: 'strOperaType',
          title: '操作类型',
          align: 'center',
          slot: 'operateTypeSlot',
          width: 95
        },
        {
          key: 'inSystemFlag',
          title: '是否内部系统推送',
          align: 'center',
          slot: 'inSystemFlagSlot',
          width: 150
        },
        {
          key: 'strUnitName',
          title: '机构名称',
          align: 'center',
          width: 150
        },
        {
          key: 'strId',
          title: '机构strId',
          align: 'center',
          width: 150
        },
        {
          key: 'requestMode',
          title: '推送方式',
          align: 'center',
          slot: 'requestModeSlot',
          width: 150
        },
        {
          key: 'syncSuccess',
          title: '响应状态',
          align: 'center',
          slot: 'responseStatusSlot',
          width: 95
        },
        {
          key: 'syncMessage',
          title: '返回信息',
          align: 'center',
          tooltip: true,
          // slot: 'failedReasonSlot',
          width: 150
        },
        {
          key: 'sourceId',
          title: '数据来源Id',
          align: 'center',
          width: 150
        },
        {
          title: '推送时间',
          key: 'createTime',
          align: 'center',
          width: 165,
          sortable: true,
          render: (h, params) => {
            return h('span', {}, params.row.createTime.substring(0, 16));
          }
        },
        {
          title: '操作',
          width: 150,
          key: 'action',
          align: 'center',
          slot: 'actionSlot'
        }
      ],
      isWw: true,
      // 选中的数据
      selectedData: [],
    }
  },
  async created() {
  },
  async mounted() {
    // 获取当前日期以及七天前的日期，格式为YYYY-MM-DD
    const date = new Date(+new Date() + 8 * 3600 * 1000);
    console.log(date.toISOString(), 'date')
    this.currentDate = date.toISOString().split('T')[0];
    this.sevenDaysAgo = new Date(date.setDate(date.getDate() - 7)).toISOString().split('T')[0];
    const { orgName } = this.$route.query;
    if (orgName) {
      this.filterForm = {
        ...this.filterForm,
        strUnitName: orgName
      };
      // 如果有orgName，则说明是从机构变更记录跳转过来的
      this.filterArr.forEach((item) => {
        if (item.name === 'strUnitName') {
          item.value = orgName;
        }
      });
    } else {
      this.filterForm = {
        ...this.filterForm,
        startEndTime: [this.sevenDaysAgo, this.currentDate]
      };
      // 设置默认时间范围为最近七天
      this.filterArr.forEach((item) => {
        if (item.name === 'startEndTime') {
          item.value = [this.sevenDaysAgo, this.currentDate];
        }
      });
    }
    this.getRecordList();
    this.isWw = await isWwBySysConfig();
  },
  methods: {
    getRecordList() {
      this.tableLoading = true;
      let { appName, appCode, strOperaType, strUnitName, syncSuccess, startEndTime, sourceId } = this.filterForm || {};
      console.log(startEndTime, 'startEndTime');
      this.$api.applicationManager.queryTSyncOrgHistroyRecordPage({
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        appName,
        appCode,
        strOperaType,
        strUnitName,
        syncSuccess,
        sourceId,
        createTimeStart: startEndTime && startEndTime[0] ? `${startEndTime[0]} 00:00:00` : '',
        createTimeEnd: startEndTime && startEndTime[1] ? `${startEndTime[1]} 23:59:59` : ''
      }).then((res) => {
        if (res.data.resultCode === 20000) {
          this.dataList = res.data.resultData.data || [];
          this.total = res.data.resultData.recordsTotal;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    testSearch(params) {
      this.currentPage = 1; // 重置当前页码
      this.filterForm = JSON.parse(JSON.stringify(params));
      if (this.filterForm.startEndTime && this.filterForm.startEndTime.length > 0 && this.filterForm.startEndTime[0] && this.filterForm.startEndTime[1]) {
        this.filterForm.startEndTime = [this.$Date(this.filterForm.startEndTime[0]).format('YYYY-MM-DD'), this.$Date(this.filterForm.startEndTime[1]).format('YYYY-MM-DD')];
      } else {
        this.filterForm.startEndTime = [];
      }
      this.getRecordList();
    },
    handlePageChange(index) {
      this.currentPage = index;
      this.getRecordList();
    },
    pageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置当前页码
      this.getRecordList();
    },
    handleDelete({ id }) {
      this.$Modal.confirm({
        title: '确定删除',
        loading: true,
        onOk: () => {
          this.$api.applicationManager.deleteOrgHistroyRecord(id).then((res) => {
            if (res?.data?.resultCode === 20000) {
              this.$Message.success('删除成功');
              this.getRecordList();
            } else {
              this.$Message.error(res?.data?.resultMsg || res?.resultMsg || '失败');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },
    handlePushAgain({ appId, orgId, inSystemFlag }) {
      this.$Modal.confirm({
        title: '确定再次推送',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[inSystemFlag == 1 ? 'syncOrgInSystem' : 'syncOrg']({
            appId,
            orgId,
            operateType: 0,
          }).then((res) => {
            if (res?.data?.resultCode === 20000) {
              this.getRecordList();
              this.$Message.success('再次推送成功');
            } else {
              // this.$Message.error(res?.data?.resultMsg || res?.resultMsg || '失败');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },
    handleBulkPush() {
      if (this.selectedData.length === 0) {
        this.$Message.warning('请至少选择一条数据');
        return;
      }
      const params = this.selectedData.map((item) => {
        return {
          appId: item.appId,
          orgId: item.orgId,
          appName: item.appName,
          strUnitName: item.strUnitName,
          inSystemFlag: item.inSystemFlag,
        };
      });
      this.$Modal.confirm({
        title: '确定批量推送',
        loading: true,
        onOk: () => {
          this.$api.applicationManager.batchSyncOrgInSystem(params).then((res) => {
            if (res?.data?.resultCode === 20000) {
              this.getRecordList();
              this.$Message.success('批量推送成功');
            } else {
              this.$Message.error(res?.data?.resultMsg || res?.resultMsg || '失败');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },
    handleSelect(selection) {
      this.selectedData = selection;
    },
    reset() {
      this.filterForm = {};
      this.filterArr.forEach((item) => {
        if (item.name === 'startEndTime') {
          item.value = [this.sevenDaysAgo, this.currentDate];
        } else {
          item.value = '';
        }
      });
    }
  }
};
</script>

<style lang="less" scoped></style>
