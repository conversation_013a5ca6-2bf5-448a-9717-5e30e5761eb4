<template>
  <div class="import-result components">
    <div class="import-result-wrapper">
      <CommonFilterBar :filterArr="filterArr" @search="testSearch">
        <!-- <template slot="right"> -->
        <!-- 批量删除 -->
        <Button @click="handleDelList" type="primary" style="float: right">批量删除</Button>
        <!-- </template> -->
      </CommonFilterBar>

      <Table ref="table" row-key="id" stripe border :columns="columns" :data="tableData" :loading="tableLoading"
        @on-selection-change="handleSelectionChange">
        <template slot-scope="{ row }" slot="messageBodySlot">
          <span class="message-body-text" :title="row.messageBody">{{ row.messageBody }}</span>
        </template>
        <template slot-scope="{ row }" slot="failReasonSlot">
          <span class="message-body-text" :title="row.failReason">{{ row.failReason }}</span>
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button oaType="oaDelete" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
      <Page :total="total" show-elevator show-total class="margin-top-10" style="float: right" v-show="total >= 10"
        :current="currentPage" @on-change="changeCurrentPage" :transfer="true" :page-size="pageSize" />
    </div>
  </div>
</template>

<script>
import CommonFilterBar from "@/views/hnjcoa/components/commonFilterBar.vue";
import { downloadTemplate } from "@/utils/demoWeb.js";
export default {
  name: "import-result",
  components: {
    CommonFilterBar,
  },
  data() {
    return {
      filterArr: [
        {
          label: "业务队列名称",
          name: "businessQueue",
          labelWidth: 120,
          value: "",
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
        // 消息体内容
        {
          label: "消息体内容",
          name: "messageBody",
          labelWidth: 120,
          value: "",
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
        // 消费失败原因
        {
          label: "消费失败原因",
          name: "failReason",
          labelWidth: 120,
          value: "",
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
        // 创建时间
        {
          label: "创建时间",
          name: "startEndTime",
          labelWidth: 80,
          value: "",
          type: 3, // 1-输入框   2-下拉菜单   3-日期时间段
        },
      ],
      filterForm: {},
      loading: false,
      tableLoading: false,
      tableData: [],
      columns: [
        {
          type: "selection",
          width: 60,
          align: "center",
        },
        {
          title: "序号",
          width: 70,
          align: "center",
          render: (h, params) => {
            return h(
              "span",
              params.index + (this.currentPage - 1) * this.pageSize + 1
            );
          },
        },
        {
          title: "业务队列名称",
          key: "businessQueue",
          align: "center",
        },
        {
          title: "消息体内容",
          key: "messageBody",
          align: "center",
          slot: "messageBodySlot",
          width: 220,
        },
        {
          title: "消费失败原因",
          key: "failReason",
          align: "center",
          slot: "failReasonSlot",
          width: 220,
        },
        {
          title: "重试次数",
          key: "retryCount",
          align: "center",
        },
        {
          title: "状态",
          key: "status",
          align: "center",
          render: (h, params) => {
            return h(
              "span",
              params.row.status == '0' ? "未处理" : "已处理"
            );
          },
        },
        {
          title: "创建时间",
          key: "createTime",
          align: "center",
        },
        {
          title: "操作",
          slot: "action",
          align: "center",
        },
      ],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      isModal: false,
      isEdit: false,
      selectedRows: [], // 选中的行数据
    };
  },
  created() {
  },
  mounted() {
    this.changeCurrentPage(1);
  },
  methods: {
    testSearch(params) {
      this.filterForm = JSON.parse(JSON.stringify(params));
      this.changeCurrentPage(1);
    },
    // 翻页
    changeCurrentPage(page) {
      if (page) {
        this.currentPage = page;
      }
      let { businessQueue, messageBody, failReason, startEndTime } = this.filterForm || {};
      const params = {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        businessQueue: businessQueue || null,
        messageBody: messageBody || null,
        failReason: failReason || null,
        createTimeStart: startEndTime && startEndTime.length ? `${startEndTime[0]} 00:00:00` : null,
        createTimeEnd: startEndTime && startEndTime.length ? `${startEndTime[1]} 23:59:59` : null,
      };
      this.getTableData(params);
    },
    // 请求列表
    getTableData(params) {
      this.tableLoading = true;
      this.$api.applicationManager.getDeadLetterQueue(params).then((res) => {
        this.tableLoading = false;
        this.total = res.data.resultData.recordsTotal;
        this.tableData = res.data.resultData.data;
      });
    },
    handleDelete(row) {
      console.log(row);
      this.$Modal.confirm({
        title: "提示",
        content: `确定删除该条数据吗？`,
        loading: true,
        onOk: () => {
          this.$api.applicationManager
            .deleteDeadLetterQueue([row.id])
            .then((res) => {
              res.data.resultCode == 20000
                ? this.$Message.success("删除成功")
                : "";
              this.changeCurrentPage(this.currentPage);
            })
            .finally((error) => {
              this.$Modal.remove();
            });
        },
      });
    },
    handleSelectionChange(selectedRows) {
      this.selectedRows = selectedRows;
    },
    handleDelList() {
      if (this.selectedRows.length === 0) {
        this.$Message.warning("请至少选择一条数据");
        return;
      }
      const ids = this.selectedRows.map((row) => row.id);
      this.$Modal.confirm({
        title: "提示",
        content: `确定删除选中的 ${this.selectedRows.length} 条数据吗？`,
        loading: true,
        onOk: () => {
          this.$api.applicationManager
            .deleteDeadLetterQueue(ids)
            .then((res) => {
              res.data.resultCode == 20000
                ? this.$Message.success("删除成功")
                : "";
              this.changeCurrentPage(this.currentPage);
            })
            .finally(() => {
              this.$Modal.remove();
            });
        },
      });
    },
    resetForm() {
      this.filterForm = {
        createName: "",
        type: "",
      };
      this.changeCurrentPage(1);
    },
  },
};
</script>
<style lang="less" scoped>
.message-body-text {
  display: inline-block;
  max-width: 200px;
  /* 根据实际需求调整宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
