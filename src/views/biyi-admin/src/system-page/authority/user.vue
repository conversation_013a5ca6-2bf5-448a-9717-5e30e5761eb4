<template>
  <div class="user-mg components" style="display: flex; flex-direction: column;">
    <Row>
      <Col v-if="!canPush" span="6">
      <!-- 左侧机构树 -->
      <div class="user-tree">
        <CommonFilterBar :filterArr="treeFilterArr" @search="treeSearch" @reset="treeReset">
        </CommonFilterBar>
        <!-- 当前选择的机构名称 -->
        <div v-if="treeOrgId" style="margin-bottom: 10px;">当前选择的机构：
          <span style="font-weight: 900;">{{ orgInfo.title || '无' }}
          </span>
        </div>
        <Tree class="treeClass" :data="treeData" @on-select-change="selectChange" :load-data="loadData">
        </Tree>
      </div>
      </Col>
      <Col :span="canPush ? '24' : '18'">
      <div class="user-table">
        <CommonFilterBar :filterArr="filterArr" @search="testSearch">
          <Button style="float: right" code="cscp.user.add_common" type="primary" icon="md-add"
            @click="handleCreateOrEditItemUser({ code: 'add_common' })">新增</Button>
        </CommonFilterBar>
        <div v-if="canPush" style="display: flex; margin-top: -10px; margin-bottom: 10px;">
          <!-- 升级 -->
          <div class="flex  flex-grow-1">
            <Button oaType="oaBlue" @click="handleBatchPush">批量推送(HTTP)</Button>
            <Button oaType="oaBlue" @click="handleBatchPush('mq')">批量推送(MQ)</Button>
          </div>
          <div>
            <div class="flex">
              <div class="  flex-grow-1"></div>
              <Button v-if="orgId" oaType="oaBlue" @click="oneHandleBatchPush">一键推送(HTTP)</Button>
              <Button v-if="orgId" oaType="oaBlue" @click="oneHandleBatchPush('mq')">一键推送(MQ)</Button>
            </div>
            <div v-if="orgName" class="m-l">一键推送{{ orgName }}的用户</div>
          </div>

          <Tooltip v-if="pushData.appCode == 'westone_uas_system' && this.isWw" placement="bottom" theme="light"
            max-width="220">
            <Button oaType="oaBlue">一键推送</Button>
            <div slot="content">
              <div style="margin-bottom: 5px;">
                <Button oaType="oaBlue" @click="handleWestoneAllPush('')">一键推送所有 </Button>
              </div>
              <div>
                <Button oaType="oaBlue" @click="handleWestoneAllPush('unPushed')">一键推送所有（未推送）</Button>
              </div>
            </div>
          </Tooltip>
        </div>
        <div v-else class="flex-end" style="display: flex; margin-top: -10px;flex-wrap: wrap;">
          <div class="flex-grow-1" style="display: flex; flex-wrap: wrap">
            <Button code="cscp.user.add" class="mb10 mr10" type="primary" icon="md-add"
              @click="handleCreateOrEditItem">新增用户</Button>

            <Button code="cscp.user.addAll" class="mb10 mr10" type="primary" icon="md-add"
              @click="handleCreateOrEditItem">新增(统一新增平台)</Button>
            <Button class="mb10 mr10" type="primary" icon="md-add" code="cscp.user.crossUnitImport"
              @click="onAllUnitUserModalOpen">跨单位用户导入</Button>

            <Button v-if="$store?.state?.user?.userInfo?.companyId" code="cscp.user.template" class="mb10 mr10"
              @click="handleTemplate">下载导入模板</Button>
            <Upload :action="action" :headers="headers" :show-upload-list="false" :multiple="false" :max-size="51200"
              :on-exceeded-size="handleMaxSize" :before-upload="handleBeforeUpload" :on-success="handleSuccess"
              :on-error="handleError" :on-format-error="handleFormatError" :format="['xls', 'xlsx']"
              accept=".xls,.xlsx">
              <Button v-if="$store?.state?.user?.userInfo?.companyId" type="primary" code="cscp.user.import"
                @click="handleImport($event)" class="mb10 mr10">导入用户</Button>
            </Upload>
            <Button type="primary" code="cscp.user.export" class="mb10 mr10" @click="handleExport">导出用户</Button>
            <Button type="primary" code="cscp.user.resetAllPassword" class="mb10"
              @click="showResetAllPasswordFrm">重置所有密码</Button>
            <Button type="primary" code="cscp.user.resetAllPassword" class="mb10 ml10"
              @click="handleRepeatName">重复登录名处理</Button>
            <Button v-if="isAdmin || isUnitAdmin" type="primary" class="mb10 ml10 mr10" code="cscp.user.del"
              @click="deleteUser({}, 'all')">批量禁用</Button>
            <!-- 只有点击了左侧组织树才显示 -->
            <Button type="primary" v-if="treeOrgId" class="mb10" icon="md-add" if
              @click="onCrossLevelUserModalOpen">跨层级用户导入</Button>
          </div>
          <!-- <div class="flex-grow-0 flex" style="align-items: center;"> -->
          <!-- </div> -->
          <div class="flex-grow-0">所有上级单位部门推送完成之后，用户才能登录。</div>
        </div>

        <Table stripe border ref="table" :columns="columns" :data="data" @on-selection-change="handleSelect"
          :loading="isDisabled">
          <template #userCodeSlot="{ row }">
            <QrcodeCreator :uniqueId="`gr_${row.strId || row.id}`" :showText="`gr_${row.strId || row.id}`"
              :previewOptions="{ title: '个人码' }"></QrcodeCreator>
          </template>
          <template #checkboxSlot="{ row }">
            <Checkbox :value="selectedRowIds.includes(row.id)" @on-change="(e) => handleSelectRow(e, row)">
            </Checkbox>
          </template>
          <template #actionSlot="{ row }" v-if="!canPush">
            <div v-if="isRegionAdminANDmoderator(row) && isUnitAdminANDmoderator(row)">
              <Button v-if="row.status" oaType="oaEdit" code="cscp.user.edit"
                @click="handleCreateOrEditItem({ ...row, code: '' })">编辑(系统)</Button>
              <!-- <Button v-if="row.status" oaType="oaEdit" @click="handleLock({ ...row })">锁定</Button> -->
              <!-- <Poptip> -->
              <Button v-if="row.status" oaType="oaEdit" code="cscp.user.updatePassword"
                @click="resetPassword(row)">重置密码</Button>
              <Button v-if="isAdmin || isUnitAdmin" oaType="oaDelete" code="cscp.user.del"
                @click="deleteUser(row, 'row')">禁用</Button>
              <Button v-if="!row.status" oaType="oaEdit" @click="handleEnable(row)">启用</Button>
              <Button v-if="isAdmin" oaType="oaEdit" @click="handlePushApplication(row, false)">推送</Button>
              <Button code="cscp.user.authorization" oaType="oaEdit" @click="handleAppAuthorization(row)">授权</Button>
              <!-- 授权记录 -->
              <Button code="cscp.user.authorization" oaType="oaEdit" @click="handlePageHistory(row)">授权历史记录</Button>
              <!-- <Button v-if="isAdmin" oaType="oaEdit" @click="handlePushApplication(row, 'mq')">MQ推送</Button> -->
            </div>
            <div v-else>
              <Button oaType="oaEdit" @click="handlePushApplication(row, true)">推送</Button>
            </div>
          </template>
          <template v-else #actionSlot="{ row }">
            <Button oaType="oaEdit" @click="handlePush(row)">推送</Button>
            <!-- <Button oaType="oaEdit" @click="handlePush(row, 'mq')">MQ推送</Button> -->
          </template>

        </Table>
        <Page class="table-page" v-if="dataTotal > pageSize" :total="dataTotal" :page-size="pageSize"
          @on-change="handlePageChange" show-elevator show-total :current="currentPageIndex"></Page>
      </div>
      </Col>
    </Row>


    <Modal v-model="editPasswordModal" :closable="false" :mask-closable="false" :width="500">
      <template slot="header">重置密码</template>
      <Form ref="editPasswordForm" :model="editPasswordForm" :label-width="100" label-position="right"
        :rules="passwordValidate">
        <FormItem label="新密码" prop="newPass">
          <div style="display: flex">
            <Input v-model="editPasswordForm.newPass" :placeholder="passwordRule" type="password" password
              :maxlength="20"></Input>
          </div>
        </FormItem>
        <FormItem label="确认新密码" prop="rePass">
          <Input v-model="editPasswordForm.rePass" placeholder="请再次输入新密码" type="password" password
            :maxlength="20"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancelResetAllPasswordFrm">取消</Button>
        <Button type="primary" @click="resetAllPassword" :loading="isSubmitDisabled">保存</Button>
      </div>
    </Modal>
    <!-- 推送应用 -->
    <Modal v-if="showApplicationModal" v-model="showApplicationModal" :width="1400" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" :footer-hide="!isAuthorized ? true : false" :title="titleName"
      @on-cancel="onPushApplicationCancel">
      <!-- <PushApplicationMq v-if="showApplicationModalName == 'mq'" canPush :pushData="pushApplicationData"
        pushType="User">
      </PushApplicationMq> -->

      <!-- 推送 -->
      <PushApplication v-if="!isAuthorized" canPush :pushData="pushApplicationData" pushType="User" :status="1"
        :moderatorFlag="moderatorFlag">
      </PushApplication>
      <!-- 授权 -->
      <AppAuthorization v-else ref="pushApp" :isAuthorized="true" :authorizedAppList="authorizedAppList"
        :authorizedAppListForEdit="appAutoPushList" pushType="User" :status="1">
      </AppAuthorization>
      <div v-if="isAuthorized" slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="onPushApplicationOk" :loading="isSubmitDisabled">确定</Button>
          <Button @click="onPushApplicationCancel">取消</Button>
        </Row>
      </div>
    </Modal>
    <Modal v-if="showAllUnitUserModal" v-model="showAllUnitUserModal" :width="800" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" footer-hide title="跨单位用户导入" @on-cancel="onAllUnitUserModalCancel">
      <div>
        <Button code="cscp.user.template" class="mb10 mr10" @click="handleAllUnitTemplate">下载跨单位导入模板</Button>
        <Upload :action="action" :headers="headers" :show-upload-list="false" :multiple="false" :max-size="51200"
          :on-exceeded-size="handleMaxSize" :before-upload="handleBeforeUpload" :on-success="handleSuccess"
          :on-error="handleError" :on-format-error="handleFormatError" :format="['xls', 'xlsx']" accept=".xls,.xlsx">
          <Button type="primary" code="cscp.user.import" @click="handleAllUnitImport" class="mb10 mr10">导入跨单位用户</Button>
        </Upload>
      </div>
    </Modal>
    <!-- 跨层级用户导入 -->
    <Modal v-if="showCrossLevelUserModal" v-model="showCrossLevelUserModal" :width="800" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" footer-hide title="跨层级用户导入" @on-cancel="onCrossLevelUserModalCancel">
      <div>
        <Button code="cscp.user.template" class="mb10 mr10" @click="handleCrossLevelTemplate">下载跨层级用户导入模板</Button>
        <Upload :action="action" :headers="headers" :show-upload-list="false" :multiple="false" :max-size="51200"
          :on-exceeded-size="handleMaxSize" :before-upload="handleBeforeUpload" :on-success="handleSuccess"
          :on-error="handleError" :on-format-error="handleFormatError" :format="['xls', 'xlsx']" accept=".xls,.xlsx">
          <Button type="primary" code="cscp.user.import" @click="handleCrossLevelImport"
            class="mb10 mr10">导入跨层级用户</Button>
        </Upload>
      </div>
    </Modal>
    <!-- 授权记录 -->
    <Modal v-if="showHistoryModal" v-model="showHistoryModal" :width="1400" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" footer-hide title="授权历史记录" @on-cancel="showHistoryModal = false">
      <!-- 搜索表单 -->
      <CommonFilterBar :filterArr="historyFilterArr" @search="handleHistorySearch"></CommonFilterBar>
      <Table stripe border :columns="historyColumns" :data="historyList" :loading="tableLoading">
        <template #operateTypeSlot="{ row }">
          {{ row.strOperaType == 'add' ? '新增' : row.strOperaType == 'update' ? '修改' : row.strOperaType == 'delete' ?
            '删除'
            :
            '' }}
        </template>
        <template #inSystemFlagSlot="{ row }">
          {{ row.inSystemFlag == 0 ? '否' : row.inSystemFlag == 1 ? '是' : '' }}
        </template>
        <template #responseStatusSlot="{ row }">
          {{ row.syncSuccess == 'true' && (row.syncStatus == '200' || row.syncStatus == '0') ? '成功' : row.syncStatus ==
            '205' ? '推送中' : '失败' }}
        </template>
        <template #requestModeSlot="{ row }">
          {{ row.requestMode == '0' ? 'MQ' : row.requestMode == '1' ? 'HTTP' : '通知拉取' }}
        </template>
      </Table>
      <Page class="table-page" v-if="historyTotal > historyPageSize" :total="historyTotal" :page-size="historyPageSize"
        @on-change="handleHistorgPageChange" show-elevator show-total :current="historgCurrentPage"></Page>
    </Modal>

    <!-- 删除有数据的用户，弹出删除原因弹窗 -->
    <Modal v-if="showDeleteReasonModal" v-model="showDeleteReasonModal" :width="700" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" title="禁用用户" @on-cancel="cancelDeleteUser">
      <!-- 账号为什么不能直接删除原因 -->
      <div style="padding-left: 30px; margin-bottom: 20px;">
        <p v-for="(item, index) in delecteUserData" :key="index">{{ item.loginName + '：' + item.reason }}</p>
      </div>
      <Form ref="deleteReasonForm" :model="deleteReasonForm" :rules="deleteRules" :label-width="140"
        label-position="right">
        <!-- 管理员审批 -->
        <FormItem label="管理员审批：" prop="applyType">
          <RadioGroup v-model="deleteReasonForm.applyType">
            <Radio label="0">省直、市区划管理员审批</Radio>
            <Radio label="1">上级区划管理员审批</Radio>
          </RadioGroup>
        </FormItem>
        <!-- 申请人姓名 -->
        <FormItem label="申请人姓名：" prop="applyRealName">
          <Input v-model="deleteReasonForm.applyRealName" placeholder="请输入申请人姓名"></Input>
        </FormItem>
        <!-- 申请人手机号 -->
        <FormItem label="申请人手机号：" prop="applyMobile">
          <Input v-model="deleteReasonForm.applyMobile" placeholder="请输入申请人手机号"></Input>
        </FormItem>
        <FormItem label="禁用原因：" prop="applyDeleteExplain">
          <Input v-model="deleteReasonForm.applyDeleteExplain" type="textarea" placeholder="请输入禁用原因"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancelDeleteUser">取消</Button>
        <Button type="primary" :loading="isSubmitDisabled" @click="confirmDeleteUser">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import base64 from '@/utils/base64.js';
import CommonFilterBar from '@/views/hnjcoa/components/commonFilterBar.vue';
import QrcodeCreator from '@/views/researchActivity/components/qrcodeCreator.vue';
import { downloadTemplate, isCipherMachineBySysConfig, isWwBySysConfig } from '@/utils/demoWeb.js';
export default {
  name: 'userList',
  components: {
    CommonFilterBar,
    QrcodeCreator,
    // 推送
    PushApplication: () => import('./applicationManager/index.vue'),
    // 授权
    AppAuthorization: () => import('./appAuthorization/index.vue'),
    // PushApplicationMq: () => import('./applicationManager/index_mq.vue'),
  },
  props: {
    canPush: { // 是否可以推送
      type: Boolean,
      default: false
    },
    pushData: { // canPush为true时可用
      type: Object,
      default: () => ({})
    },
    orgId: { // canPush为true时可用
      type: String,
      default: ''
    },
    orgName: { // canPush为true时可用
      type: String,
      default: ''
    },
  },
  data() {
    return {
      showDeleteReasonModal: false, // 删除有数据的用户，弹出删除原因弹窗
      delecteUserData: [], // 删除用户数据
      deleteReasonForm: {
        applyType: '0', // 管理员审批方式 0-省直、市区划管理员审批 1-上级区划管理员审批
        applyDeleteExplain: '',
        applyRealName: '', // 申请人姓名
        applyMobile: '' // 申请人手机号
      },
      deleteRules: {
        applyType: [
          { required: true, message: '请选择管理员审批方式', trigger: 'change' }
        ],
        applyRealName: [
          { required: true, message: '请输入申请人姓名', trigger: 'blur' }
        ],
        applyMobile: [
          { required: true, message: '请输入申请人手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        applyDeleteExplain: [
          { required: true, message: '请输入禁用原因', trigger: 'blur' }
        ]
      },
      titleName: '', // 推送应用弹窗标题
      isAuthorized: false, // 是否授权
      authorizedAppList: [], // 勾选的应用列表
      isFirst: true, // 是否第一次加载
      // 机构数据
      treeData: [],
      treeOrgId: '', // 左侧树组织id
      isAllUnitUploadLoading: false,
      showCrossLevelUserModal: false, // 跨层级用户导入弹窗
      // 跨单位用户导入弹窗
      showAllUnitUserModal: false,
      filterArr: [
        {
          label: '用户名',
          name: 'loginName',
          value: '',
          labelWidth: 60,
          type: 1 //1-输入框2-下拉菜单3-日期时间段
        },
        {
          label: '真实姓名',
          name: 'realName',
          value: '',
          type: 1
        },
        {
          label: '用户唯一信任号',
          name: 'strId',
          value: '',
          labelWidth: 120,
          type: 1
        },
        {
          label: '是否激活',
          name: 'status',
          value: '1',
          labelWidth: 120,
          type: 2,
          selectArr: [{ value: '', label: '所有' }, { value: '1', label: '激活' }, { value: '0', label: '未激活' }]
        },
        ...!this.canPush ? [
          {
            label: '手机',
            name: 'mobile',
            value: '',
            labelWidth: 60,
            type: 1
          },
          // {
          //   label: '部门',
          //   name: 'departmentName',
          //   value: '',
          //   labelWidth: 40,
          //   type: 1
          // }
        ] : [],
        {
          label: '身份证号',
          name: 'idCardNo',
          value: '',
          labelWidth: 80,
          type: 1
        }
        // {
        //   label: '是否推送到VPN',
        //   name: 'westoneUserPushedStatus',
        //   value: '',
        //   labelWidth: 120,
        //   type: 2,
        //   selectArr: [{ value: '', label: '所有' }, { value: 'pushed', label: '是' }, { value: 'unPushed', label: '否' }]
        // }
      ],
      treeFilterArr: [
        {
          label: '机构名称',
          name: 'departmentName',
          value: '',
          labelWidth: 70,
          type: 1 //1-输入框2-下拉菜单3-日期时间段
        }
      ],
      filterForm: {},
      action: '',
      headers: { Authorization: sessionStorage.token },
      isDisabled: false,
      isSubmitDisabled: false,
      editPasswordModal: false,
      query: '',
      dataTotal: 0,
      currentPageIndex: 1,
      pageSize: this.canPush ? 50 : 10,
      data: [],
      userInfo: {},
      tableUserInfo: {},
      columns: [
        ...this.canPush ? [
          {
            title: ' ',
            width: 55,
            align: 'center',
            slot: 'checkboxSlot',
            renderHeader: (h) => {
              return h('Checkbox', {
                props: {
                  value: !!(this.data && this.data.length && !this.data.find((item) => !this.selectedRowIds.includes(item.id))),
                  disabled: !this.data.length
                },
                on: {
                  input: (value) => {
                    if (value) {
                      this.data.forEach((item) => {
                        if (!this.selectedRowIds.includes(item.id)) {
                          this.selectedRowIds.push(item.id);
                        }
                      });
                    } else {
                      this.data.forEach((item) => {
                        if (this.selectedRowIds.includes(item.id)) {
                          this.selectedRowIds = this.selectedRowIds.filter((sItem) => sItem !== item.id);
                        }
                      });
                    }
                  }
                }
              });
            }
          }
        ] : [],
        ...!this.canPush ? [{
          type: 'selection',
          width: 60,
          align: 'center'
        }] : [],
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h('span', params.index + (this.currentPageIndex - 1) * this.pageSize + 1);
          }
        },
        {
          title: '个人码',
          width: 80,
          align: 'center',
          slot: 'userCodeSlot',
        },
        {
          key: 'loginName',
          title: '用户名',
          align: 'center',
          width: this.canPush ? 150 : undefined
        },
        {
          key: 'realName',
          title: '真实姓名',
          align: 'center',
          width: this.canPush ? 150 : undefined
        },
        {
          key: 'mobile',
          title: '手机',
          align: 'center',
          width: 150
        },
        {
          key: 'idCardNo',
          title: '身份证',
          align: 'center',
          width: 150
        },
        {
          key: 'strId',
          title: '用户唯一信任号',
          align: 'center',
          width: 150
        },
        {
          key: 'dataIsIntegrity',
          title: '数据完整性',
          align: 'center',
          width: 120,
          render: (h, params) => {
            return 'div', [h('span', {}, params.row.dataIsIntegrity == '1' ? '不完整' : '完整')];
          }
        },
        // {
        //   key: 'westoneUserId',
        //   title: '是否推送到VPN',
        //   align: 'center',
        //   width: 150,
        //   render: (h, params) => {
        //     return 'div', [h('span', {}, (params.row.westoneUserId != '' && params.row.westoneUserId != null) ? '已推送' : '未推送')];
        //   }
        // },
        {
          key: 'companyName',
          title: '单位',
          align: 'center'
        },
        {
          key: 'departmentName',
          title: '部门',
          align: 'center'
        },
        ...!this.canPush ? [
          // {
          //   key: 'appVersionName',
          //   title: 'App版本号',
          //   align: 'center',
          //   width: 120
          // },
          // {
          //   key: 'examineStatusName',
          //   title: '审核状态',
          //   align: 'center',
          //   render: (h, params) => {
          //     let obj = {
          //       color:
          //         params.row.examineStatusName == '通过'
          //           ? '#19be6b'
          //           : params.row.examineStatusName == '驳回'
          //           ? '#ed4014'
          //           : ''
          //     };
          //     return h('span', { style: obj }, params.row.examineStatusName);
          //   },
          //   width: 120
          // },
          {
            key: 'status',
            title: '状态',
            align: 'center',
            render: (h, params) => {
              return 'div', [h('span', {}, params.row.status == '1' ? '激活' : '未激活')];
            },
            width: 70
          }
        ] : [],
        // {
        //   key: 'sort',
        //   title: '部门排序',
        //   align: 'center',
        //   width: 100
        // },
        {
          title: '创建时间',
          key: 'createTime',
          align: 'center',
        },
        {
          title: '操作',
          align: 'center',
          fixed: 'right',
          key: 'handle',
          minWidth: !this.canPush ? 200 : 150,
          slot: 'actionSlot'
        }
      ],
      msg: '',
      passwordRule: '',
      userDetail: '',
      editPasswordForm: {
        newPass: '',
        rePass: ''
      },
      passwordValidate: {
        newPass: [
          {
            required: true,
            message: '请输入新密码',
            trigger: 'blur'
          },
          {
            trigger: 'blur',
            validator: (rule, value, callback) => {
              var num = 0;
              var rule1 = /\d+/;
              var rule2 = /[a-z]+/;
              var rule3 = /[A-Z]+/;
              var rule4 = /[~!@#\$%^&*\(\)\{\};,.\?\/'"]/;
              var rule5 = /^.{10,20}$/;
              var flag1 = rule1.test(value);
              var flag2 = rule2.test(value);
              var flag3 = rule3.test(value);
              var flag4 = rule4.test(value);
              var flag5 = rule5.test(value);

              if (!flag5) {
                callback(new Error('密码长度必须为10-20位'));
                return;
              }

              if (flag1) {
                num = num + 1;
              }
              if (flag2) {
                num = num + 1;
              }
              if (flag3) {
                num = num + 1;
              }
              if (flag4) {
                num = num + 1;
              }

              if (!(num >= 4 && flag5)) {
                callback(new Error('密码必须同时包含大写字母、小写字母、数字和特殊字符'));
                return;
              } else {
                callback();
              }
            }
          }
        ],
        rePass: [
          {
            required: true,
            message: '请再次输入新密码',
            trigger: 'blur'
          },
          {
            validator: this.valideRePassword,
            trigger: 'blur'
          }
        ]
      },
      // 推送
      selectedRowIds: [],
      // 推送应用
      showApplicationModal: false,
      pushApplicationData: {},
      showApplicationModalName: '',
      isWw: true,
      isAdmin: false,
      isUnitAdmin: false,
      regionAdmin: false,
      selectedData: [],
      // 应用列表
      appList: [],
      // 已推送列表
      pushedAppList: [],
      appAutoPushList: [], // 自动推送的应用列表
      // 单个用户信息
      // 授权记录
      showHistoryModal: false,
      historyList: [],
      historyColumns: [
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h('span', params.index + (this.historgCurrentPage - 1) * this.historyPageSize + 1);
          }
        },
        {
          key: 'appName',
          title: '系统名称',
          align: 'center'
        },
        {
          key: 'appCode',
          title: '编号',
          align: 'center',
          width: 90
        },
        {
          key: 'strOperaType',
          title: '操作类型',
          align: 'center',
          slot: 'operateTypeSlot',
          width: 95
        },
        {
          key: 'inSystemFlag',
          title: '是否内部系统推送',
          align: 'center',
          slot: 'inSystemFlagSlot',
          width: 150
        },
        {
          key: 'strCname',
          title: '用户姓名',
          align: 'center',
          width: 95
        },
        {
          key: 'loginName',
          title: '用户名',
          align: 'center',
          width: 120
        },
        {
          key: 'strId',
          title: '用户strId',
          align: 'center',
          width: 150
        },
        {
          key: 'strMobile',
          title: '用户手机号',
          align: 'center',
          width: 120
        },
        {
          key: 'requestMode',
          title: '推送方式',
          align: 'center',
          slot: 'requestModeSlot',
          width: 150
        },
        {
          key: 'syncSuccess',
          title: '响应状态',
          align: 'center',
          slot: 'responseStatusSlot',
          width: 95
        },
        {
          key: 'syncMessage',
          title: '返回信息',
          align: 'center',
          tooltip: true,
          // slot: 'failedReasonSlot',
          width: 150
        },
        {
          title: '创建时间',
          key: 'createTime',
          align: 'center',
          width: 120,
          sortable: true,
          render: (h, params) => {
            return h('span', {}, params.row.createTime.substring(0, 16));
          }
        },
      ],
      historyFilterArr: [
        {
          label: '授权时间',
          name: 'createTime',
          value: [],
          labelWidth: 100,
          type: 3 //1-输入框2-下拉菜单3-日期时间段
        }
      ],
      historyFilterForm: {},
      tableLoading: false,
      historyTotal: 0,
      historyPageSize: 10,
      historgCurrentPage: 1,
      moderatorFlag: false, // 是否是版主
      // 选种的机构信息
      orgInfo: {},
    };
  },
  computed: {
  },
  methods: {
    // 如果是版主并且是区划管理员 只能操作区划下的单位或者机构 其余只能推送查看
    isRegionAdminANDmoderator(row) {
      // 如果是版主但不是单位管理员不是区划管理员 直接返回false
      if (this.userInfo.moderatorAppIdList.length > 0 && !this.regionAdmin && !this.userInfo.roleCodes.includes("unit_role")) {
        return false
      }
      if (this.userInfo.moderatorAppIdList.length > 0 && this.regionAdmin) {
        const orgCode = this.userInfo.cscpOrgDTO?.orgCode || '';
        // 如果orgCode的第五和第六位是00则是市级区划机构 如果不是则是县级区划机构
        if (orgCode.length >= 6 && orgCode.substring(4, 6) === '00') {
          // 市级区划机构
          return row.departmentCode?.startsWith(orgCode.substring(0, 4));
        } else {
          // 县级区划机构
          return row.departmentCode?.startsWith(orgCode.substring(0, 6));
        }
      } else {
        // 非版主用户
        return true;
      }
    },
    // 如果是版主 并且是单位管理员 只能操作单位下的机构和部门 其余只能推送查看
    isUnitAdminANDmoderator(row) {
      // 如果是版主但不是单位管理员不是区划管理员 直接返回false
      if (this.userInfo.moderatorAppIdList.length > 0 && !this.regionAdmin && !this.userInfo.roleCodes.includes("unit_role")) {
        return false
      }
      if (this.userInfo.moderatorAppIdList.length > 0 && this.userInfo.roleCodes.includes("unit_role")) {
        // 如果是单位管理员
        return row.departmentCode?.startsWith(this.userInfo.cscpOrgDTO?.orgCode || '');
      } else {
        // 非单位管理员
        return true;
      }
    },
    renderTreeNode(h, { node, data }) {
      // 判断标题里是否有“虚拟机构”
      if (typeof data.title === 'string' && data.title.includes('虚拟机构')) {
        const parts = data.title.split('（虚拟机构）');
        return h('span', [
          parts[0],
          h('span', { style: { color: 'red' } }, '（虚拟机构）')
        ]);
      }
      return h('span', data.title);
    },
    cancelDeleteUser() {
      this.showDeleteReasonModal = false;
      this.$refs.deleteReasonForm.resetFields(); // 重置表单
    },
    confirmDeleteUser() {
      this.$refs.deleteReasonForm.validate(valid => {
        if (valid) {
          this.isSubmitDisabled = true; // 提交时禁用按钮
          let msg = this.$Message.loading({
            content: '正在给对应审核人员发送禁用申请...',
            duration: 0
          });
          let params = {
            applyType: this.deleteReasonForm.applyType,
            applyDeleteExplain: this.deleteReasonForm.applyDeleteExplain,
            applyRealName: this.deleteReasonForm.applyRealName,
            applyMobile: this.deleteReasonForm.applyMobile,
            deleteUserIds: this.delecteUserData.map(item => item.deleteId)
          };
          this.$api.user.submitDeleteAudit(params).then(res => {
            if (res && res.data.resultCode == 20000) {
              this.$Message.success('发送成功！');
              this.showDeleteReasonModal = false;
              this.$refs.deleteReasonForm.resetFields(); // 重置表单
              this.getUserList();
            }
          }).finally(() => {
            msg();
            this.isSubmitDisabled = false; // 提交完成后启用按钮
          });
        }
      });
    },
    onAllUnitUserModalOpen() {
      this.isAllUnitUploadLoading = false
      this.showAllUnitUserModal = true
    },
    onCrossLevelUserModalOpen() {
      this.showCrossLevelUserModal = true
    },
    onAllUnitUserModalCancel() {
      this.showAllUnitUserModal = false
    },
    onCrossLevelUserModalCancel() {
      this.showCrossLevelUserModal = false
    },
    // 锁定用户-三员
    handleLock(row) {
      this.$Modal.confirm({
        title: '警告',
        content: '确认锁定该用户吗？',
        onOk: () => {
          let msg = this.$Message.loading({
            content: '正在锁定用户',
            duration: 0
          });
          let params = {
            id: row.id,
            status: 0
          };
          this.$api.three.updateUserStatus(params).then(res => {
            this.$Message.success('锁定成功');
            this.getUserList();
          }).finally(() => {
            msg();
          });
        }
      });
    },
    valideRePassword(rule, value, callback) {
      if (value !== this.editPasswordForm.newPass) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    },
    getPasswordRule() {
      this.$api.login.cscpUserPasswordRule().then(res => {
        this.passwordRule = res.data;
      });
    },
    testSearch(params) {
      this.currentPageIndex = 1; // 重置当前页码
      this.filterForm = JSON.parse(JSON.stringify(params));
      this.init();
    },
    treeSearch(params) {
      const { departmentName } = JSON.parse(JSON.stringify(params));
      if (departmentName) {
        // 如果有值走查询接口
        let msg = this.$Message.loading({
          content: '正在更新数据',
          duration: 0
        });
        this.$api.org.getOrgHierarchyByNameNewest(departmentName).then(res => {
          // type 为 1表示该机构为虚拟机构
          this.treeData = res.data.resultData.map((item) => {
            const transformChildren = (node) => {
              node.title = node.type === 1 ? node.title + '（虚拟机构）' : node.title; // 如果是虚拟机构，标题后面加上（虚拟机构）
              if (node.children) {
                node.children = node.children.map(transformChildren); // 递归处理子节点
              }
              return node;
            };
            return transformChildren(item);
          });
          // 递归设置所有节点的展开状态
          const expandAllNodes = (nodes) => {
            nodes.forEach(node => {
              // 如果 node.isValuable为 1  children 为null 表示有下级
              if (node.isValuable === 1 && !node.children) {
                node.children = [];
                node.loading = false; // 设置loading为false
              } else {
                node.expand = true; // 展开节点
              }
              if (node.children && node.children.length > 0) {
                expandAllNodes(node.children);
              }
            });
          };

          expandAllNodes(this.treeData);
        }).finally(() => {
          msg();
        });
      } else {
        // 如果没有值，走初始化组织树接口
        this.initOrgTree()
      }
    },
    treeReset() {
      this.treeFilterForm = {};
      this.treeOrgId = '';
      // this.initOrgTree();
      this.init();
    },
    handleMaxSize(file) {
      this.$Message.error('文件' + file.name + '超出指定大小限制');
    },
    handleBeforeUpload(file) {
      this.msg = this.$Message.loading({
        content: '正在上传',
        duration: 0
      });
    },
    handleFormatError(file, fileList) {
      this.$Message.error('文件格式错误');
    },
    handleSuccess(response, file, fileList) {
      if (response.resultCode == 20000) {
        this.$Message.success({
          // content: `导入成功，可前往“系统设置” - “导入结果页面”查看结果`,
          duration: 5,
          render: h => {
            return h('span', [
              '导入成功，可前往“系统设置” - “',
              h(
                'a',
                {
                  on: {
                    click: () => {
                      this.viewResult();
                    }
                  }
                },
                '导入结果'
              ),
              '"页面查看结果'
            ]);
          }
        });
        this.msg();
        this.$emit('setIsEdit');
        this.init();
      } else {
        this.$Message.error('导入失败');
      }
    },
    handleError(error, file, fileList) {
      // console.log(error);
      this.msg();
      this.$Message.error('导入失败');
    },
    viewResult() {
      this.$router.push({ name: 'importResult' });
    },
    userResult() {
      this.$router.push({ name: 'exportUserManage' });
    },
    // 下载导入模板
    handleTemplate() {
      downloadTemplate(this, '人员导入模板.xlsx');
    },
    // 下载跨单位导入模板
    handleAllUnitTemplate() {
      downloadTemplate(this, '人员导入模板（按单位）.xlsx');
    },
    // 跨单位导入
    handleAllUnitImport() {
      this.action =
        this.$util.getAjaxUrl('') +
        `/api/userImportAndExport/uploadUserNew`;
    },
    // 下载跨层级导入模板
    handleCrossLevelTemplate() {
      downloadTemplate(this, '人员多层级导入模板.xlsx');
    },
    // 导入跨层级用户
    handleCrossLevelImport() {
      let id = this.treeOrgId ? this.treeOrgId : 0;
      console.log(id, 'id');
      this.action =
        this.$util.getAjaxUrl('') +
        `/api/userImportAndExport/dcj/uploadUser/${id}`;
    },
    // 导入用户
    handleImport() {
      this.action =
        this.$util.getAjaxUrl('workflow') +
        `/api/userImportAndExport/uploadUser/${this.$store.state.user.userInfo.companyId}`;
    },
    // 导出用户
    handleExport() {
      let msg = this.$Message.loading({
        content: '正在导出当前用户',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      axios({
        method: 'get',
        params: {
          ...this.filterForm,
          id: this.canPush ? this.orgId : this.treeOrgId,
        },
        url:
          baseUrl +
          `/api/userImportAndExport/exportUser`,
        // responseType: 'blob',
        headers: {
          Authorization: sessionStorage.token
        }
      })
        .then(res => {
          console.log(res);
          if (res.data.resultCode == 20000) {
            // this.$Message.success('导出成功');
            this.$Message.success({
              // content: `导入成功，可前往“系统管理” - “导出用户文件管理页面”查看结果`,
              duration: 5,
              render: h => {
                return h('span', [
                  '导出成功，可前往“系统管理” - “',
                  h(
                    'a',
                    {
                      on: {
                        click: () => {
                          this.userResult();
                        }
                      }
                    },
                    '导出用户文件管理'
                  ),
                  '"页面查看结果'
                ]);
              }
            });
          } else {
            this.$Message.error('导出失败');
          }
        })
        .finally(() => {
          msg();
        });
    },
    // handleExport() {
    //   let msg = this.$Message.loading({
    //     content: '正在导出当前用户',
    //     duration: 0
    //   });
    //   let baseUrl = this.$util.baseUrl;
    //   axios({
    //     method: 'get',
    //     params: {
    //       ...this.filterForm,
    //       id: this.canPush ? this.orgId : this.treeOrgId,
    //     },
    //     url:
    //       baseUrl +
    //       `/api/userImportAndExport/exportUser`,
    //     responseType: 'blob',
    //     headers: {
    //       Authorization: sessionStorage.token
    //     }
    //   })
    //     .then(res => {
    //       const link = document.createElement('a');
    //       let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
    //       link.style.display = 'none';
    //       link.href = URL.createObjectURL(blob);
    //       link.download = `用户表.xlsx`; //下载的文件名
    //       document.body.appendChild(link);
    //       link.click();
    //       document.body.removeChild(link);
    //       if (res.status == 200) {
    //         this.$Message.success('导出成功');
    //       } else {
    //         this.$Message.error('导出失败');
    //       }
    //     })
    //     .finally(() => {
    //       msg();
    //     });
    // },
    handleStatus(row) {
      let s = row.status == '1' ? '0' : '1';
      this.$Modal.confirm({
        title: '提示',
        content: `确定${row.status == 1 ? '锁定' : '激活'}用户 ${row.realName} 吗？`,
        loading: true,
        onOk: () => {
          this.$api.user
            .updateUserStatus(row.id, s)
            .then(res => {
              if (res && res.data.resultCode == 20000) {
                // this.data[row._index].status = s;
                this.$Message.success(`${row.status == 1 ? '锁定' : '激活'}成功`);
                this.getUserList();
              }
            })
            .then(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    resetAllPassword() {
      this.$Message.destroy();
      this.$refs['editPasswordForm'].validate(valid => {
        if (valid) {
          this.isSubmitDisabled = true;
          let msg = this.$Message.loading({
            content: '正在更新密码',
            duration: 0
          });
          const data = {
            newPassword: base64.encode(this.editPasswordForm.newPass)
          };
          this.$api.login
            .resetAllPassword(data)
            .then(response => {
              if (response && response.data.resultCode == 20000) {
                if (response.data.resultData) {
                  this.$Message.success('更新密码成功！');
                  this.editPasswordModal = false;
                } else {
                  this.$Message.success('更新密码失败！');
                }
              }
            })
            .finally(() => {
              msg();
              this.isSubmitDisabled = false;
            });
        }
      });
    },
    handleRepeatName() {
      let msg = this.$Message.loading({
        content: '正在处理重复登录名数据',
        duration: 0
      });
      this.$api.user.fixDuplicateLogin().then(res => {
        if (res.data.resultCode === 20000) {
          this.$Message.success('处理成功！');
          this.init()
          msg()
        }
      })
    },
    async init() {
      this.getUserList(1);
      const isCipherMachine = await isCipherMachineBySysConfig();

      if (!isCipherMachine) {
        this.columns = this.columns.filter(item => item.key !== 'dataIsIntegrity');
      }
    },
    getUserList(page) {
      let msg = this.$Message.loading({
        content: '正在更新数据',
        duration: 0
      });
      this.isDisabled = true;
      const { userType } = JSON.parse(sessionStorage.getItem('setUserInfo'))
      const httpConfig = {
        ...this.filterForm,
        // sort: "orderBy",
        // companyId: this.canPush ? this.orgId : undefined,
        currentPage: page || this.currentPageIndex,
        pageSize: this.pageSize,
        orgId: this.canPush ? this.orgId : this.treeOrgId,
        userType
      };
      this.$api.user.getNewCscpUserListByOrgId(httpConfig)
        .then(response => {
          // 把身份证号中间设置*
          this.data = response.data.resultData.data.map(item => {
            if (item.idCardNo) {
              item.idCardNo = item.idCardNo.replace(/^(\d{3})\d{11}(\d{3}[\dXx])$/, '$1***********$2');
            }
            return item;
          });
          this.dataTotal = response.data.resultData.recordsTotal;
        })
        .finally(() => {
          msg();
          this.isDisabled = false;
        });
    },
    // 重置密码成功
    resetPassword(row) {
      this.$Modal.confirm({
        title: '提示',
        content: `确定重置用户 ${row.loginName} 的密码吗？`,
        loading: true,
        onOk: () => {
          this.$api.login.resetPassword(row.id)
            .then(res => {
              if (res && res.data.resultCode == 20000) {
                this.$Message.success('重置密码成功！');
                // this.getUserList();
              }
            })
            .then(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    showResetAllPasswordFrm() {
      this.editPasswordModal = true;
      this.$refs.editPasswordForm.resetFields();
      this.editPasswordForm = {
        newPass: '',
        rePass: ''
      };
    },
    cancelResetAllPasswordFrm() {
      this.editPasswordModal = false;
    },
    saveEditPass() {
      this.$Message.destroy();
      this.$refs['editPasswordForm'].validate(valid => {
        if (valid) {
          this.isSubmitDisabled = true;
          let msg = this.$Message.loading({
            content: '正在更新密码',
            duration: 0
          });
          const data = {
            userId: this.userDetail.id,
            userName: this.userDetail.loginName,
            newPassword: base64.encode(this.editPasswordForm.newPass)
          };
          this.$api.login
            .cscpUserPasswordByAdmin(data)
            .then(response => {
              if (response && response.data.resultCode == 20000) {
                this.$Message.success('更新密码成功！');
                this.editPasswordModal = false;
              }
            })
            .finally(() => {
              msg();
              this.isSubmitDisabled = false;
            });
        }
      });
    },
    async handleCreateOrEditItem(row) {
      if (row) {
        this.$byStoreSet('user-edit', { row });
        this.$router.push({
          name: 'user-edit'
        });
      } else {
        this.$byStoreSet('user-add', { row: { code: '' } });
        this.$router.push({
          name: 'user-add'
        });
      }
    },

    // 开放给普通用户的新增、编辑
    handleCreateOrEditItemUser(row) {
      let type = row.code.split('_')[0];
      if (type == 'edit') {
        this.$byStoreSet('user-edit', { row });
        this.$router.push({
          name: 'user-edit-common'
        });
      } else {
        this.$byStoreSet('user-add', { row });
        this.$router.push({
          name: 'user-add-common'
        });
      }
    },
    handlePageChange(index) {
      this.currentPageIndex = index;
      this.getUserList();
    },

    deleteUser(row, type = 'row') {
      let ids = []
      let loginName = ""
      // 拿到所有的督促用户
      if (type == 'row') {
        let dcData = row.personLabel ? row.personLabel.indexOf('dc') > -1 || row.personLabel.indexOf('ducha') > -1 ? row : {} : {};
        // 如果是督查用户 提示不能删除
        if (dcData.id) {
          this.$Message.warning(`当前用户${dcData.loginName}为督查用户！不能删除！`);
          return;
        } else if (row.id == this.userInfo.id) {
          this.$Message.warning('单位管理员不能删除自己！');
          return;
        }
        ids.push(row.id)
        loginName = row.loginName
      } else {
        console.log(this.selectedData, 'this.selectedData');
        // 如果是督查用户 提示不能删除
        let dcData = this.selectedData.filter(item => item.personLabel ? item.personLabel.indexOf('dc') > -1 || item.personLabel.indexOf('ducha') > -1 : false);
        let dcName = dcData.length > 0 ? dcData.map(item => item.loginName).join(',') : '';
        // 如果选中的用户有当前用户，不能删除
        if (dcData.length > 0) {
          this.$Message.warning(`选中的用户${dcName}为督查用户！不能删除！`);
          return;
        } else if (this.selectedData.some(item => item.id == this.userInfo.id)) {
          this.$Message.warning(`选中的用户${this.userInfo.userName}为单位管理员！不能删除！`);
          return;
        }
        ids = this.selectedData.map(item => item.id);
        loginName = this.selectedData.length > 0 ? this.selectedData.map(item => item.loginName).join(',') : '';
      }
      if (ids.length == 0) {
        this.$Message.warning('请选择要删除的用户！');
        return;
      }
      this.$Modal.confirm({
        title: '提示',
        content: `确定禁用用户 ${loginName} ？`,
        loading: true,
        onOk: () => {
          this.$api.user
            .disableCscpUser(ids)
            .then(res => {
              if (res && res.data.resultCode == 20000) {
                if (res.data.resultData && res.data.resultData.length > 0) {
                  // 如果返回的不是空数组，说明这些数据不能直接删除需要填写当前操作人的信息以及删除原因
                  if (type == 'row') {
                    // 单个删除
                    this.showDeleteReasonModal = true;
                    this.delecteUserData = res.data.resultData.map((item) => {
                      return {
                        ...item,
                        loginName: row.loginName,
                      };
                    })
                  } else {
                    // 批量删除
                    this.showDeleteReasonModal = true;
                    this.delecteUserData = res.data.resultData.map((item) => {
                      return {
                        ...item,
                        loginName: this.selectedData.find(selected => selected.id === item.deleteId)?.loginName || '',
                      };
                    })
                  }
                } else {
                  this.$Message.success('禁用成功！');
                  this.getUserList();
                }
              }
            })
            .finally(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    pushUserAuto(userId) {
      this.$api.user.syncUserBusiness({ userId, flag: 'delete' })
    },
    // 推送
    handleSelectRow(status, data) {
      if (status) { // 勾选
        this.selectedRowIds.push(data.id);
      } else { // 取消勾选
        this.selectedRowIds = this.selectedRowIds.filter((item) => item !== data.id);
      }
    },
    handleSelect(selection) {
      console.log(selection, 'selection');
      this.selectedData = selection;
    },
    handlePush({ id }, name = '') {
      this.$Modal.confirm({
        title: '确定推送',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[this.pushData.inSystemFlag == 0 ? 'syncUser' : name == 'mq' ? 'sendUserMessage' : 'syncUserInSystem']({
            appId: this.pushData.id,
            userId: id
          }).then((res) => {
            if (res?.data?.resultCode === 20000) {
              this.$Message.success('推送成功');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },
    handleBatchPush(name = '') {
      if (!this.selectedRowIds.length) {
        return this.$Message.warning('请先勾选');
      }
      this.$Modal.confirm({
        title: '确定批量推送',
        loading: true,
        onOk: () => {
          // this.$api.applicationManager[this.pushData.appCode == 'westone_uas_system' ? 'syncUserBatch' : name == 'mq' ? 'sendUserBatchMessage' : 'syncUserBatchInSystem']({
          this.$api.applicationManager[name == 'mq' ? 'sendUserBatchMessage' : 'syncUserNew']({
            appId: this.pushData.id,
            userIds: this.selectedRowIds
          }).then((res) => {
            if (res?.data?.resultCode === 20000) {
              this.selectedRowIds = [];
              this.$Message.success('推送成功');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },
    oneHandleBatchPush(name = '') {

      this.$Modal.confirm({
        title: '确定一键推送',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[name == 'mq' ? 'syncUserBatchByCompanyMessage' : 'syncUserBatchByCompany']({
            appId: this.pushData.id,
            orgId: this.orgId
          }).then((res) => {
            if (res?.data?.resultCode === 20000) {

              this.$Message.success('推送成功');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },

    handleWestoneAllPush(pushedStatus) {
      this.$Modal.confirm({
        title: '确定一键批量推送所有',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[pushedStatus === 'unPushed' ? 'syncUnPushedWestoneUserBatch' : 'syncWestoneUserBatch']({
            appId: this.pushData.id
          }).then((res) => {
            if (res?.data?.resultCode === 20000) {
              this.selectedRowIds = [];
              this.$Message.success('推送成功，请稍后查看推送结果');
            }
          }).finally(() => {
            this.$Modal.remove();
          });
        }
      });
    },
    // 推送应用
    handlePushApplication(data, moderatorFlag = false) {
      this.titleName = '推送'
      this.isAuthorized = false; // 重置授权状态
      this.moderatorFlag = moderatorFlag; // 是否版主推送应用
      // this.showApplicationModalName = name
      this.showApplicationModal = true;
      this.pushApplicationData = data;
    },
    // 授权应用
    async handleAppAuthorization(data) {
      await this.getApplicationList().catch(err => {
        this.$Message.error('获取应用列表失败，请稍后重试');
      });
      this.titleName = "授权"
      this.isAuthorized = true; // 设置授权状态为true
      this.tableUserInfo = data
      console.log(data.pushAppCode, 'pushAppCode');
      this.pushedAppList = this.appList.filter(item => {
        return data.pushAppCode ? data.pushAppCode.split(',').includes(item.appCode) : false;
      });
      console.log(this.pushedAppList, 'pushedAppList');
      this.authorizedAppList = [...this.pushedAppList]
      this.showApplicationModal = true;
    },
    // 授权记录
    handlePageHistory(data) {
      this.showHistoryModal = true;
      this.tableUserInfo = data;
      this.historgCurrentPage = 1;
      this.historyPageSize = 10;
      this.handleHistoryPageChange(1);
    },
    // 授权记录分页
    handleHistoryPageChange(index = 1) {
      this.tableLoading = true;
      this.historgCurrentPage = index;
      const { appName, appCode, status, createTime } = this.historyFilterForm;
      console.log(this.historyFilterForm, 'historyFilterForm');
      this.$api.applicationManager.queryTSyncUserHistroyRecordPageByRole({
        userId: this.tableUserInfo.id,
        createTimeStart: createTime && createTime.length > 0 && createTime[0] ? `${createTime[0]} 00:00:00` : '',
        createTimeEnd: createTime && createTime.length > 0 && createTime[1] ? `${createTime[1]} 23:59:59` : '',
        currentPage: this.historgCurrentPage,
        pageSize: this.historyPageSize
      }).then(res => {
        if (res && res.data.resultCode == 20000) {
          this.historyList = res.data.resultData.data;
          this.historyTotal = res.data.resultData.recordsTotal;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    handleHistorgPageChange(index) {
      this.handleHistoryPageChange(index);
    },
    handleHistorySearch(params) {
      this.historyFilterForm = JSON.parse(JSON.stringify(params));
      this.handleHistoryPageChange(1);
    },
    onPushApplicationCancel() {
      this.showApplicationModal = false;
      this.pushApplicationData = {};
    },

    // 授权弹窗确定
    onPushApplicationOk() {
      console.log(this.$refs.pushApp.selectedRowData);
      let pushAppCode = this.$refs.pushApp.selectedRowData.map(app => app.appCode)
        .filter(appCode => !!appCode)
        .join(',');
      console.log(pushAppCode, 'pushAppCode');
      if (!pushAppCode) {
        this.$Message.warning('请选择要授权的应用');
        return;
      }
      this.isSubmitDisabled = true;
      this.$api.applicationManager.updateCscpUser({
        id: this.tableUserInfo.id,
        pushAppCode
      }).then(res => {
        if (res && res.data.resultCode == 20000) {
          this.$Message.success('授权成功！');
          this.showApplicationModal = false;
          this.tableUserInfo = {};
          this.getUserList(this.currentPageInde);
        }
      }).finally(() => {
        this.isSubmitDisabled = false;
      });
    },
    // 启用
    handleEnable(row) {
      this.$Modal.confirm({
        title: '提示',
        content: `确定启用用户 ${row.loginName} 吗？`,
        loading: true,
        onOk: () => {
          this.$api.user.enableCscpUser([row.id])
            .then(res => {
              if (res && res.data.resultCode == 20000) {
                this.$Message.success('启用成功！');
                this.getUserList();
              }
            })
            .then(() => {
              this.$Modal.remove();
            });
        }
      })
    },
    // 初始化机构树
    initOrgTree(checkedList = []) {
      let msg = this.$Message.loading({
        content: '正在更新数据',
        duration: 0
      });
      this.getTree(0, checkedList).then(res => {
        this.treeData = [
          {
            children: res,
            title: "全部",
            expand: true,
          }
        ]
        msg()
      });
    },
    //获取树的内容
    getTree(id = 0, checkedList = []) {
      return new Promise((resolve, reject) => {
        this.orgLoading = true;
        this.$api.org
          .selectOrgById({
            id
          })
          .then(res => {
            this.orgLoading = false;
            resolve(
              res.data.resultData.map(n => {
                return n.isValuable
                  ? {
                    id: n.id,
                    title: n.type == 1 ? `${n.title} （虚拟机构）` : n.title,
                    disabled: false,
                    checked: checkedList.includes(n.id),
                    children: [],
                    loading: false
                  }
                  : {
                    id: n.id,
                    title: n.type == 1 ? `${n.title} （虚拟机构）` : n.title,
                    disabled: false,
                    checked: checkedList.includes(n.id)
                  };
              })
            );
          })
          .catch(err => {
            reject(err);
            this.orgLoading = false;
          });
      });
    },
    // 机构展开获取数据
    loadData(item, callback) {
      let params = [];
      this.getTree(item.id, params).then(res => {
        callback(res);
      });
    },
    // 机构点击获取用户
    selectChange(item) {
      this.currentPageIndex = 1; // 重置页码
      if (item[0].title == '全部') {
        this.treeOrgId = '';
        this.init()
      } else {
        this.treeOrgId = item[0].id;
        this.orgInfo = { ...item[0] }
        this.init()
      }
    },
    //获取应用列表
    async getApplicationList() {
      const params = {
        currentPage: 1,
        pageSize: 1000,
        // autoPush: 1,
        status: 1
      }
      const { data } = await this.$api.applicationManager.queryTSyncAppSystemManagePage(params);
      // 自动推送的应用
      this.appList = data.resultData.data;
      this.appAutoPushList = data.resultData.data.filter(item => item.autoPush == 1);
    },
  },
  async created() {
    // historyFilterArr 里的创建时间默认一个月内 yy-mm-dd 格式
    const now = new Date(+new Date() + 8 * 3600 * 1000);
    const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    this.historyFilterArr.forEach(item => {
      if (item.name === 'createTime') {
        item.value = [
          oneMonthAgo.toISOString().substring(0, 10),
          now.toISOString().substring(0, 10)];
      }
    });
    this.historyFilterForm.createTime = [
      oneMonthAgo.toISOString().substring(0, 10),
      now.toISOString().substring(0, 10)
    ];
    if (!this.canPush) {
      this.initOrgTree()
    }
    const userType = this.$byStoreGet('userInfo').userType;
    this.userInfo = JSON.parse(sessionStorage.getItem('setUserInfo'))
    let roleCodes = JSON.parse(sessionStorage.getItem('setUserInfo')).roleCodes || [];
    // this.isAdmin = userType === 'SYSTEM_USER' || roleCodes.includes('region_role');
    this.isAdmin = userType === 'SYSTEM_USER';
    this.isUnitAdmin = roleCodes.includes('unit_role')
    this.regionAdmin = roleCodes.includes('region_role');
    this.isWw = await isWwBySysConfig();
    if (!this.isWw) {
      this.columns = this.columns.filter(item => item.key !== 'westoneUserId');
      this.filterArr = this.filterArr.filter(item => item.name !== 'westoneUserPushedStatus');
    }
    this.filterForm.status = '1'; // 默认只查询激活用户
    await this.init();

    this.getPasswordRule();

  },
  activated() {
    if (this.isFirst) {
      // 首次进入逻辑
      this.isFirst = false;
    } else {
      this.getUserList(this.currentPageIndex);
      // 非首次进入逻辑
    }
  },
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;

}

.flex-grow-1 {
  min-width: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
}

.m-l {
  margin-left: 10px;
  text-align: right;
}

.user-tree {
  padding-right: 5px !important;
  width: calc(100% - 10px) !important;
  /* height: 100%; */
  border-right: 1px solid #e8eaec;

  .treeClass {
    max-height: calc(75vh - 150px) !important;
    overflow-y: auto;
  }
}
</style>
