<template>
  <div class="organization components" :class="canPush ? 'can_push' : ''">
    <CommonFilterBar :filterArr="filterArr" @search="testSearch">
      <div v-if="canPush" style="display: flex; margin-top: -10px; margin-bottom: 10px">
        <!-- 升级 -->
        <Button oaType="oaBlue" @click="handleBatchPush('')">批量推送(HTTP)</Button>
        <Button oaType="oaBlue" @click="handleBatchPush('mq')">批量推送(MQ)</Button>
        <!--        <Button oaType="oaBlue" v-if="pushData.appCode == 'westone_uas_system'" @click="handleWestoneAllPush">一键批量推送所有</Button>-->
      </div>
      <template v-else>
        <!-- 隐藏顶部新增  by heda 2024/8/27 start -->
        <Button v-if="!$store?.state?.user?.userInfo?.companyId" class="ml10" style="float: right" code="cscp.org.add"
          icon="md-add" type="primary" @click="handleAdd('', 1)" :disabled="loading">新增</Button>

        <!-- 隐藏顶部新增 by heda 2024/8/27 end -->
        <div style="float: right">
          <!-- 社会信用号查询 -->
          <Button class="ml10" type="primary" @click="creditCodeShow = true">社会信用号查询</Button>
          <Button class="ml10" type="primary" @click="handleTemplate('组织机构路径导入模板')"
            :disabled="loading">下载组织机构路径导入模板</Button>
          <template v-if="isAdmins()">
            <Button :code="isRegion ? '' : 'cscp.org.add'" type="primary" @click="handleTemplate('组织机构多层级导入模板')"
              style="margin-left: 10px">
              下载多层级导入模板
            </Button>

            <Button style="margin-left: 10px" :code="isRegion ? '' : 'cscp.org.add'" type="primary"
              @click="uploadLevelCompany">
              <Upload style="margin-top: 3px" :action="actions" :headers="headers" :show-upload-list="false"
                :multiple="false" :max-size="51200" :on-exceeded-size="handleMaxSize"
                :before-upload="handleBeforeUpload" :on-success="handleSuccessOrg" :on-error="handleError"
                :on-format-error="handleFormatError" :format="['xls', 'xlsx']" accept=".xls,.xlsx">
                组织机构多层级导入
              </Upload>
            </Button>
          </template>
          <Button style="margin-left: 10px" :code="isRegion ? '' : 'cscp.org.add'" type="primary"
            @click="handleTemplate('部门导入模板')">
            下载内设机构导入模板
          </Button>

          <Button v-if="isAdmin || isRegion" style="margin-left: 10px" :code="isRegion ? '' : 'cscp.org.add'"
            type="primary" @click="handleTemplate('单位导入模板')">
            下载单位导入模板
          </Button>
        </div>
      </template>
    </CommonFilterBar>

    <Upload ref='uploadRef' :action="action" :headers="headers" :show-upload-list="false" :multiple="false"
      :max-size="51200" :on-exceeded-size="handleMaxSize" :before-upload="handleBeforeUpload"
      :on-success="handleSuccess" :on-error="handleError" :on-format-error="handleFormatError" :format="['xls', 'xlsx']"
      accept=".xls,.xlsx">
      <template>
        <span></span>
      </template>
    </Upload>

    <!-- 表格 -->
    <div style="flex: 1; height: 0;" ref="tableBox" @scroll="handleTableScroll">
      <vxe-table :key="tableKey" :row-drag-config="rowDragConfig" @row-dragend="rowDragendEvent" ref="tableRef" border
        show-overflow :virtual-y-config="{ enabled: true, gt: 0 }" header-align="center" :loading="loading"
        :data="tempDeptList" :scroll-x="{ gt: 50 }" :scroll-y="{ gt: 20 }" :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'id', drag: true }" height="650px"
        :loading-config="{ icon: 'vxe-icon-indicator roll', text: '加载中...' }" :tree-config="{
          ...treeConfig,
          loadMethod({ row }) {
            return handleLoadData(row);
          }
        }" :checkbox-config="this.canPush ? { labelField: 'id', highlight: true } : {}"
        @checkbox-change="selectChangeEvent" @toggle-tree-expand="selectChangeEventExpand" empty-text="暂无数据">
        <vxe-column v-for="col in isUnitCrm ? orgColumnsCrm : orgColumns" :key="col.field" :title="col.title"
          :width="col.width" :tree-node="col.tree" :type="col.slot === 'checkboxSlot' && canPush ? 'checkbox' : ''"
          :drag-sort="col.field == 'title' ? true : false" dragTip="999">
          <template #default="{ row }">
            <div v-if="col.slot == 'crmTenantType'">
              {{ crmTenantTypeMap[row.detailsData.crmTenantType] }}
            </div>
            <div v-else-if="col.slot == 'orgType'">
              {{ optionsMap[row.detailsData?.type] }}
            </div>
            <div v-else-if="col.slot == 'orgCodeSlot'">
              {{ row.orgCode || row.detailsData?.orgCode }}
            </div>
            <div v-else-if="col.field == 'title'">
              <div v-html="highLightTextFun(row.title, keyWord, 'red')"></div>
            </div>
            <div v-else-if="col.slot == 'action'">
              <div v-if="canPush">
                <i class="i-blue" @click="handlePush(row)">推送</i>
                <!-- <i class="i-blue" @click="handlePush(row, 'mq')">MQ推送</i> -->
              </div>
              <div v-else-if="isRegionAdminANDmoderator(row) && isUnitAdminANDmoderator(row)"
                style="display: flex; justify-content: center; flex-wrap: wrap">
                <i class="i-blue" v-if="hasPermission(isRegion ? '' : 'cscp.org.add')"
                  @click="handleAdd(row, 1)">新增(系统)</i>
                <!-- 如果是顶级机构禁止编辑 -->
                <i class="i-blue" v-if="hasPermission(isRegion ? '' : 'cscp.org.edit')" @click="handleAdd(row, 0)"
                  :style="row.detailsData?.parentId == 0 || row.detailsData?.parentId === '0' ? 'pointer-events: none; color: #ccc;' : ''">编辑(系统)</i>
                <i class="i-blue" v-if="hasPermission(isRegion ? '' : 'cscp.org.view')"
                  @click="handleView(row)">查看成员</i>

                <i class="i-blue" v-if="row.detailsData?.crmTenantType && row.detailsData?.type == 2"
                  @click="handleViewDepart(row)">查看单位信息</i>
                <i class="i-red" v-if="hasPermission(isRegion ? '' : 'cscp.org.delete')"
                  :style="row.detailsData?.parentId == 0 || row.detailsData?.parentId === '0' ? 'pointer-events: none; color: #ccc;' : ''"
                  @click="handleDelete(row)">删除</i>
                <!-- 组织机构路径导入 -->
                <!-- <i class="i-blue" @click="handleUploadLevelAll(row)">组织机构路径导入</i> -->
                <i v-if="isAdmins()" class="i-blue" @click="handlePushApplication(row, false)">推送</i>
                <i class="i-blue" v-if="hasPermission(isRegion ? '' : 'cscp.org.authorization')"
                  @click="handleAppAuthorization(row)">授权</i>
                <!-- <i class="i-blue" v-if="hasPermission(isRegion ? '' : 'cscp.org.authorization')"
                  @click="handlePageHistory(row)">授权历史记录</i> -->

                <!-- <i v-if="isAdmins()" class="i-blue" @click="handlePushApplication(row, 'mq')">MQ推送</i> -->

                <!-- <i class="i-blue" @click="synchro(row)">关联</i> -->
                <!-- 更多操作 -->
                <Dropdown trigger="hover" style="margin-left: 8px;">
                  <i class="i-blue">
                    更多 <Icon type="ios-arrow-down"></Icon>
                  </i>
                  <DropdownMenu slot="list">
                    <DropdownItem v-if="hasPermission(isRegion ? '' : 'cscp.org.authorization')"
                      @click.native="handlePageHistory(row)">授权历史记录</DropdownItem>
                    <DropdownItem v-if="hasPermission(isRegion ? '' : 'cscp.org.importStructure')"
                      @click.native="handleUploadLevelAll(row)">组织机构路径导入</DropdownItem>
                    <DropdownItem v-if="hasPermission(isRegion ? '' : 'cscp.org.export')"
                      @click.native="handleExportAsync(row)">导出机构
                    </DropdownItem>
                    <template v-if="row.detailsData?.type != 3">
                      <DropdownItem v-if="
                        isRegion ? [1, 2].includes(row.detailsData?.type) : (isAdmin && row.detailsData?.type == 1 && hasPermission('cscp.org.importUnit'))
                      " @click.native="handleImportDept(row, 'company')">导入单位</DropdownItem>
                      <DropdownItem
                        v-if="row.detailsData?.type == 2 && hasPermission(isRegion ? '' : 'cscp.org.exportDepartment')"
                        @click.native=" handleExport(row)">导出内设机构</DropdownItem>
                      <DropdownItem
                        v-if="row.detailsData?.type == 2 && hasPermission(isRegion ? '' : 'cscp.org.importDepartment')"
                        @click.native="handleImportDept(row)">内设机构导入
                      </DropdownItem>
                    </template>
                    <DropdownItem @click.native="synchro(row)">关联</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </div>
              <!-- 版主推送 -->
              <div v-else style="display: flex; justify-content: center; flex-wrap: wrap">
                <i class="i-blue" @click="handlePushApplication(row, true)">推送</i>
                <!-- <i class="i-blue" @click="handleAppAuthorization(row)">授权</i> -->
              </div>
            </div>
            <div v-else>{{ row[col.field] }}</div>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <!-- 查看成员 -->
    <Drawer :mask-closable="false" :transfer="false" width="60" v-model="viewMemberDrawer" title="查看成员">
      <div class="view-member">
        <CommonFilterBar :filterArr="modalFilterArr" ref="modalFilter" @search="modalSearch">
          <!-- <Button type="primary" icon="md-add" :code="isRegion ? '' : 'cscp.org.viewadd'" style="float: right"
            @click="handleAddMember" class="ml10" v-if="!isUnit">新增</Button> -->
          <Button type="primary" icon="md-add" style="float: right" @click="handleBatchPushUser">批量推送</Button>
        </CommonFilterBar>
        <div class="view-member-table">
          <Table :draggable="true" @on-drag-drop="onDragDrop" row-key="id" :columns="memberColumns" :data="memberData"
            stripe border @on-selection-change="handleMemberSelectionChange">
            <template slot-scope="{ row }" slot="departmentHead">
              {{ row.departmentHead ? '是' : '否' }}
            </template>
            <template slot-scope="{ row }" slot="status">
              {{ row.status ? '启用' : '禁用' }}
            </template>
            <template slot-scope="{ row }" slot="action">
              <span v-if="!isUnit">
                <Button oaType="oaEdit" :code="isRegion ? '' : 'cscp.org.add'" @click="handleAction(row, 0)"
                  v-if="!row.departmentHead" class="margin-right-10">设置领导</Button>
                <Button oaType="oaEdit" :code="isRegion ? '' : 'cscp.org.view'" @click="handleAction(row, 1)"
                  v-else>取消领导</Button>
              </span>
              <!-- 复制用户名 -->
              <Button oaType="oaEdit" @click="handleCopyUserName(row)" class="margin-right-10">复制用户名</Button>
              <Button oaType="oaDelete" :code="isRegion ? '' : 'cscp.org.delete'"
                @click="handleAction(row, 2)">移除</Button>
            </template>
          </Table>
          <Page :total="memberTotal" show-elevator show-total class="margin-top-10" v-show="memberTotal >= 10"
            :current="currentPage" @on-change="changeMember" :transfer="true" :page-size="pageSize"
            style="float: right" />
        </div>
      </div>
    </Drawer>
    <!-- 添加或修改部门对话框 -->
    <Modal :title="title" v-model="open" width="60%" :mask-closable="false" :transfer="false" v-if="open">
      <Form ref="form" :model="form" :rules="rules" :label-width="160" label-position="right">
        <Row>
          <Col span="24" v-if="!isEdit && form.title">
          <FormItem label="上级机构" prop="title">
            <Input v-model="form.title" disabled />
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="机构类型" prop="type">
            <!-- 如果是顶级机构编辑不能修改机构类型 -->
            <Select v-model="form.type" placeholder="请选择" @on-change="typeChange"
              :disabled="currentUploadRow ? currentUploadRow.detailsData?.parentId == 0 && isEdit : false">
              <!-- :disabled="(item.value == 3 && !form.title) || (item.value == 3 && form.parentType == 1) ||   (item.value != 3 && isPersonUser)" -->
              <Option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"
                :disabled="((!isAdmin && (item.label == '单位' || item.label == '虚拟机构')) && !isRegion) || (isVirtualOrg && item.label == '内设机构') ? true : false" />
            </Select>
          </FormItem>
          </Col>
          <Col span="24" style="position: relative">
          <FormItem label="机构名称" prop="orgName">
            <Input v-model="form.orgName" placeholder="机构名称" @on-blur="orgNameBlur" clearable></Input>
            <div v-if="!isWw" class="select-org-btn">
              <Button type="primary" @click="showSelectOrgModel" size="small">选择</Button>
              <Button @click="clearOrg" size="small" style="margin-left: 5px; margin-right: 5px">清空</Button>
            </div>
          </FormItem>
          </Col>
          <!-- 机构标签 -->
          <Col span="24">
          <FormItem label="机构标签" prop="orgLabel">
            <div style="background: #fff" class="border" @click="orgLabelClick">
              <Tag v-for="(item, index) in form.orgLabelList" :key="index"
                @on-close="handleClosePersonOrg(item, index)">{{
                  item.title }}</Tag>
            </div>
          </FormItem>
          </Col>
          <Col span="24" v-if="dynamicTheme === 'red'">
          <FormItem label="密级" prop="securityLevelCode">
            <Select v-model="form.securityLevelCode" placeholder="请选择密级" @on-change="securityChange">
              <Option v-for="item in securityType" :key="item.value" :label="item.name" :value="item.code" />
            </Select>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="机构简称">
            <Input v-model="form.orgAbbreviation" placeholder="机构简称" clearable></Input>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="别名">
            <Input v-model="form.aliasName" placeholder="别名" clearable></Input>
          </FormItem>
          </Col>
          <Col span="24">
          <!-- 单位级别 -->
          <FormItem label="单位级别">
            <Select v-model="form.orgLevelType" clearable @on-clear="onClear('orgLevelType')">
              <Option v-for="item in unitLevelList" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="已授权应用" prop="defaultDepart">
            <div style="background: #fff" class="border" @click="openApplicationModal">
              <Tag v-for="(item, index) in form.authorizeAppList" :key="index">{{ item.appName }}</Tag>
            </div>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem prop="orderBy" label="排序">
            <InputNumber style="background: #fff" v-model="form.orderBy" :max="99999" />
          </FormItem>
          </Col>
          <!-- 机构分类 -->
          <Col span="24">
          <FormItem v-if="form.type == 2" label="机构分类">
            <Select v-model="form.orgClassify" clearable style="width: 200px" @on-clear="onClear('orgClassify')">
              <Option v-for="item in orgClassifyList" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem v-if="form.type == 2" label="单位类型">
            <Select v-model="form.unitType" clearable style="width: 200px">
              <Option v-for="item in unitTypeList" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </FormItem>
          </Col>
          <!-- 系统条线 -->
          <Col span="24">
          <FormItem v-if="form.type == 2" label="系统条线">
            <Select v-model="form.orgLine" clearable multiple>
              <Option v-for="item in orgLineList" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </FormItem>
          </Col>
          <!-- 值班电话 -->
          <Col span="24" v-if="form.type == 2">
          <FormItem label="值班电话" prop="dutyPhone">
            <Input v-model="form.dutyPhone" placeholder="值班电话" maxlength="50" clearable></Input>
          </FormItem>
          </Col>
          <!-- 传真电话 -->
          <Col span="24" v-if="form.type == 2">
          <FormItem label="传真电话" prop="faxPhone">
            <Input v-model="form.faxPhone" placeholder="传真电话" maxlength="50" clearable></Input>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="备注" prop="description">
            <Input v-model="form.description" placeholder="备注" clearable></Input>
          </FormItem>
          </Col>
          <Col v-if="form.type == 2" span="24">
          <FormItem label="统一社会信用代码" prop="creditCode">
            <Input v-model="form.creditCode" placeholder="统一社会信用代码" maxlength="50" clearable
              @on-blur="creditBlur"></Input>
            <!-- 展示已经存在的机构 -->
            <div v-if="creditCodeOrg && creditCodeOrg.length > 0" class="credit-code-exist">已存在的机构：
              <span v-for="(item, index) in creditCodeOrg" :key="index" class="credit-code-item">
                {{ item.orgName }}
              </span>
            </div>
          </FormItem>
          </Col>
          <Col span="24" v-if="isEdit">
          <FormItem label="父级机构" prop="parentName">
            <Input v-model="parentName" placeholder="父级机构" maxlength="50" clearable
              :disabled="currentUploadRow.isDistrictOrStreet"></Input>
            <div class="select-org-btn">
              <Button v-if="!currentUploadRow.isDistrictOrStreet" type="primary" @click="openOrgModel"
                size="small">选择</Button>
              <Button type="primary" @click="openOrgModel(true)" size="small"
                style="margin-left: 5px; margin-right: 5px;">修改层级</Button>
            </div>
          </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <Button type="primary" :loading="submitLoading" @click="submitForm">确 定</Button>
        <Button @click="cancel">取 消</Button>
      </div>
    </Modal>
    <!-- 选分管领导Modal -->
    <UserSelect ref="branchLeaderIdSelect" @getUserList="getBranchLeaderId" :isMessageAudit="true"></UserSelect>
    <!-- 选部门领导Modal -->
    <DepartmentSelect ref="departmentHeadIdListSelect" @getWorkGroupList="getDepartmentHeadIdList">
    </DepartmentSelect>
    <!-- 查看成员 - 新增选人 -->
    <UserSelect ref="viewMemberAdd" @getUserList="getViewMemberAdd"></UserSelect>
    <Department ref="department" @getDepartment="getDepartment"></Department>

    <!-- 查看单位客户经理、客户管理员、账户数、合同年限等相关信息 -->
    <Drawer :mask-closable="false" :transfer="false" width="40" v-model="viewDepartCustomerDrawer" title="查看信息">
      <div class="view-member">
        <div class="task-row-col">
          <Row>
            <Col span="6" class="task-detail-name">客户经理姓名：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.nameOfAccountManager }}</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 客户经理电话：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.customerManagerTelephone }}</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 客户管理员姓名：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.customerAdministratorName }}</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 客户管理员联系电话：</Col>
            <Col span="18" class="task-detail-value">{{
              rowDepart.customerAdministratorTelephone
            }}</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 账号数：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.numberOfAccounts }}</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 项目合同期限：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.projectContractPeriod }} 月</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 项目总金额：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.totalProjectAmount }} 万元</Col>
          </Row>
          <Row>
            <Col span="6" class="task-detail-name"> 客户标识：</Col>
            <Col span="18" class="task-detail-value">{{ rowDepart.groupNumber }}</Col>
          </Row>
        </div>
      </div>
    </Drawer>
    <ModelModal v-model="isShowModel" :selecteds="selectModelList" @onSelect="onSelectModel" />
    <!-- 选择机构 -->
    <Modal v-if="showOrgModal" ref="showOrgModal" title="选择机构" v-model="showOrgModal" :mask-closable="false" width="60%"
      :transfer="false" :footer-hide="true">
      <OrgSelectModal ref="tenantUnitSelect" @getUnitList="getOrgList" @getCancel="showOrgModal = false"
        :isMessageAudit="true"></OrgSelectModal>
    </Modal>
    <!-- 推送应用 -->
    <Modal v-if="showApplicationModal" v-model="showApplicationModal" :width="1400" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" :footer-hide="isPush ? true : false" :title="titleName"
      @on-cancel="onPushApplicationCancel">
      <!-- <PushApplicationMq v-if="showApplicationModalName == 'mq'" canPush :pushData="pushApplicationData" pushType="Org">
      </PushApplicationMq> -->
      <!-- 推送 -->
      <PushApplication v-if="isPush" canPush :pushData="pushApplicationData" pushType="Org" :status="1"
        :moderatorFlag="moderatorFlag">
      </PushApplication>
      <!-- 授权 -->
      <AppAuthorization v-else ref="pushApp" :isAuthorized="isAuthorized" :isEdit="isEdit"
        :authorizedAppListForEdit="appAutoPushList" pushType="Org" :status="1"
        :authorizedAppList="form.authorizeAppList">
      </AppAuthorization>
      <div v-if="!isPush" slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="onPushApplicationOk" :loading="isSubmitDisabled">确定</Button>
          <Button @click="onPushApplicationCancel">取消</Button>
        </Row>
      </div>
    </Modal>

    <!-- :submitInterface="''" -->
    <unitSynchro :show.sync="unitSynchroShow" :submitParams="{ id: currentData.id }"></unitSynchro>
    <!-- 设置父级机构弹窗 -->
    <Modal title="设置机构" v-model="treeShow" :mask-closable="false" width="30%" :transfer="false">
      <div class="set-org" v-loading="orgLoading">
        <CommonFilterBar :filterArr="unitRoleFilterArr" ref="roleFilter" @search="unitSearch" @reset="unitReset">
        </CommonFilterBar>
        <Tree :data="orgIdOptions" expand :load-data="loadData" @on-select-change="changeTree">
        </Tree>
      </div>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handleOrgOk">确定</Button>
          <Button @click="treeShow = false; parentOrgData = {}">取消</Button>
        </Row>
      </div>
    </Modal>
    <!-- 审核删除 -->
    <Modal title="删除申请" v-model="deleteShow" :mask-closable="false" width="30%" :transfer="false">
      <Form ref="formDeleted" :model="formDelete" :rules="rulesDelete" :label-width="100" label-position="right">
        <Row>
          <Col span="24">
          <FormItem label="删除原因" prop="auditExplain">
            <Input v-model="formDelete.auditExplain" type="textarea" :rows="4" placeholder="请输入删除原因" clearable></Input>
          </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handleDeleteOk">确定</Button>
          <Button @click="deleteShow = false; formDelete = { auditExplain: '' }">取消</Button>
        </Row>
      </div>
    </Modal>

    <!-- 导出地址 -->
    <Modal title="导出机构" v-model="exportShow" :mask-closable="false" width="30%" :transfer="false">
      <Form ref="formExport" :model="formExport" :label-width="40" label-position="right">
        <Row>
          <Col span="24">
          <FormItem label="" prop="exportUrl">
            <span>导出成功，请在<span @click="navigateToExport(exportUrl)"
                style="color: #2d8cf0; cursor: pointer;">"导出文件管理"</span>查询下载</span>
            <!-- <p @click="copyText(exportUrl)" clearable>{{ exportUrl }}</p> -->
          </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button @click="exportShow = false; parentOrgData = {}">取消</Button>
        </Row>
      </div>
    </Modal>


    <!-- 授权记录 -->
    <Modal v-if="showHistoryModal" v-model="showHistoryModal" :width="1400" :styles="{ minHeight: '80%' }"
      :mask-closable="false" :transfer="false" footer-hide title="授权历史记录" @on-cancel="showHistoryModal = false">
      <!-- 搜索表单 -->
      <CommonFilterBar :filterArr="historyFilterArr" @search="handleHistorySearch"></CommonFilterBar>
      <Table stripe border :columns="historyColumns" :data="historyList" :loading="tableLoading">
        <template #operateTypeSlot="{ row }">
          {{ row.strOperaType == 'add' ? '新增' : row.strOperaType == 'update' ? '修改' : row.strOperaType == 'delete'
            ?
            '删除'
            :
            '' }}
        </template>
        <template #inSystemFlagSlot="{ row }">
          {{ row.inSystemFlag == 0 ? '否' : row.inSystemFlag == 1 ? '是' : '' }}
        </template>
        <template #responseStatusSlot="{ row }">

          {{ row.syncSuccess == 'true' && (row.syncStatus == '200' || row.syncStatus == '0') ? '成功' :
            row.syncStatus ==
              '205' ? '推送中' : '失败' }}
        </template>
        <template #requestModeSlot="{ row }">
          {{ row.requestMode == '0' ? 'MQ' : row.requestMode == '1' ? 'HTTP' : '通知拉取' }}
        </template>

      </Table>
      <Page class="table-page" v-if="historyTotal > historyPageSize" :total="historyTotal" :page-size="historyPageSize"
        @on-change="handleHistorgPageChange" show-elevator show-total :current="historgCurrentPage"></Page>
    </Modal>

    <!-- 选择机构标签 -->
    <Modal title="选择机构标签" v-model="personShow" :mask-closable="false" width="30%" :transfer="false">
      <div class="set-org" v-loading="orgLoading">
        <Tree :data="orgLabelList" :check-strictly="true" show-checkbox expand @on-check-change="changePersonTree">
        </Tree>
      </div>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handlePersonOk">确定</Button>
          <Button @click="personShow = false">取消</Button>
        </Row>
      </div>
    </Modal>

    <!-- 社会信用号查询弹窗 -->
    <Modal title="社会信用代码查询" v-model="creditCodeShow" :mask-closable="false" width="30%" :transfer="false"
      @on-cancel="onCancel">
      <!-- <CommonFilterBar :filterArr="creditCodeFilterArr" @search="creditCodeSearch" @reset="creditCodeShow = false">
      </CommonFilterBar> -->
      <Form ref="formCreditCodeRef" :model="formCreditCode" :rules="formCreditCodeRules" :label-width="120"
        label-position="left">
        <Row>
          <Col span="16">
          <FormItem label="社会信用代码" prop="creditCode">
            <Input v-model="formCreditCode.creditCode" placeholder="请输入18位大写字母和数字" clearable></Input>
          </FormItem>
          </Col>
          <!-- 查询按钮 -->
          <Col span="6" style="text-align: left; margin-left: 10px;">
          <Button type="primary" @click="creditCodeSearch">查询</Button>
          </Col>
        </Row>
        <!-- 查询结果 -->
        <Row v-if="creditCodeData.length > 0">
          <Col span="24">
          <Tree :data="creditCodeData"></Tree>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button @click="onCancel">取消</Button>
        </Row>
      </div>
    </Modal>
  </div>
</template>

<script>

import MenuEdit from '@/views/hnjcoa/components/menuEdit.vue';
import CommonFilterBar from '@/views/hnjcoa/components/commonFilterBar.vue';
import UserSelect from '@/views/components/userSelectModal';
import DepartmentSelect from './workGroupModal.vue';
import formatDate from '../../mixins/date.js';
import Department from '@/views/hnjcoa/document/number/components/departmentSelect.vue';
import OrgSelectModal from './components/selectOrg.vue';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import {
  highLightText,
  searchTree,
  downloadTemplate,
  initColumnsByCipherMachine,
  isWwBySysConfig
} from '@/utils/demoWeb.js';
import unitSynchro from '@/views/biyi-admin/src/system-page/authority/components/unitSynchro.vue';
import QrcodeCreator from '@/views/researchActivity/components/qrcodeCreator.vue';
import org from '@/api/organization';

export default {
  mixins: [formatDate],
  props: {
    canPush: {
      // 是否可以推送
      type: Boolean,
      default: false
    },
    pushData: {
      type: Object,
      default: () => ({})
    },
    isRegion: {
      // 是否是区划机构管理
      type: Boolean,
      default: false
    }
  },
  components: {
    // VXETable,
    UserSelect,
    DepartmentSelect,
    Department,
    CommonFilterBar,
    // MenuEdit,
    OrgSelectModal,
    // QrcodeCreator,
    // 推送
    PushApplication: () => import('./applicationManager/index.vue'),
    // 授权
    AppAuthorization: () => import('./appAuthorization/index.vue'),
    // PushApplicationMq: () => import('./applicationManager/index_mq.vue'),
    ModelModal: () =>
      import('@/views/biyi-admin/src/system-page/authority/components/modelModal.vue'),
    unitSynchro
  },

  data() {
    const validateCreditCode = (rule, value, callback) => {
      // 正则表达式：18位大写字母和数字
      const pattern = /^[A-Z0-9]{18}$/;
      if (!value || pattern.test(value)) {
        callback();
      } else {
        callback(new Error('请输入18位大写字母和数字'));
      }
    };
    const validateSmsKey = (rule, value, callback) => {
      if (value === '' && this.form.smsName) {
        callback(new Error('短信KEY不能为空'));
      } else {
        callback();
      }
    };
    const validateSmsName = (rule, value, callback) => {
      if (value === '' && this.form.smsKey) {
        callback(new Error('短信账号名不能为空'));
      } else {
        callback();
      }
    };
    return {
      // 已经存在的信用代码机构
      creditCodeOrg: [],
      creditCodeShow: false,
      creditCodeFilterArr: [
        {
          label: '社会信用代码',
          name: 'creditCode',
          value: '',
          labelWidth: 120,
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      formCreditCode: {
        creditCode: ''
      },
      // 是否修改层级
      isUpdatehHierarchy: false,
      creditCodeData: [],
      formCreditCodeRules: {
        creditCode: [
          { required: true, message: '请输入社会信用代码', trigger: 'blur' },
          { validator: validateCreditCode, trigger: 'blur' }
        ]
      },
      personShow: false,
      isWw: '',
      appAutoPushList: [], // 应用自动推送列表
      tableOrgInfo: {},
      // 应用列表
      appList: [],
      // 已推送列表
      pushedAppList: [],
      selectedMember: [],
      getOrgInfo: {},
      userInfo: {},
      // 替换的父组织数据
      parentOrgData: {},
      checkedKeys: [],
      parentName: '',
      orgIdOptions: [],
      orgLoading: false,
      unitRoleFilterArr: [
        {
          label: '机构名称',
          name: 'orgName',
          value: '',
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      treeShow: false,
      deleteShow: false,
      exportShow: false,
      exportUrl: "",
      formExport: {},
      deleteRow: {},
      formDelete: {
        auditExplain: ''
      },
      isPersonUser: false,
      dynamicTheme: localStorage.themeColor,
      securityType: [],
      orgLabelList: [],
      selectLabelList: [],
      mList: [],
      baseList: [
        {
          title: '统一机构用户支撑平台',
          // PC浏览器标签图
          labTitle: 'PC浏览器标签图',
          code: 'PC_BROWSERLOGO',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 1,
          imageTip: '建议尺寸1038px*368px，大小不超过2MB'
        },
        {
          title: 'PC登录背景',
          // PC登录背景图
          labTitle: 'PC登录背景图',
          code: 'PC_LOGINBG',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 2,
          imageTip: '建议尺寸1920px*1080px，大小不超过2MB'
        },
        {
          title: 'PC登录LOGO',
          // PC登录logo
          labTitle: 'PC登录LOGO',
          code: 'PC_LOGINLOGO',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 3,
          imageTip: '建议尺寸440px*230px，大小不超过2MB'
        },
        {
          title: 'PC首页背景',
          // PC首页背景图
          labTitle: 'PC首页背景图',
          code: 'PC_INDEXBG',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 4,
          imageTip: '建议尺寸1920px*1080px，大小不超过2MB'
        },
        {
          title: 'PC首页LOGO',
          // PC首页logo
          labTitle: 'PC首页LOGO',
          code: 'PC_INDEXLOGO',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 5,
          imageTip: '建议尺寸355px*55px，大小不超过2MB'
        },

        {
          title: '移动终端桌面logo',
          // app轮播图
          labTitle: '移动终端桌面logo',
          code: 'APP_ZMLOGO',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 6,
          imageTip: '建议尺寸120px*120px，大小不超过2MB'
        },
        {
          title: '移动终端登录logo',
          // app轮播图
          labTitle: '移动终端登录logo',
          code: 'APP_DLLOGO',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 7,
          imageTip: '建议尺寸391px*35px，大小不超过2MB'
        },
        {
          title: '移动终端首页logo',
          // app轮播图
          labTitle: '移动终端首页logo',
          code: 'APP_SYLOGO',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 8,
          imageTip: '建议尺寸456px*49px，大小不超过2MB'
        },
        {
          title: 'APP轮播图',
          // app轮播图
          labTitle: 'APP轮播图',
          code: 'APP_BANNER',
          tenantId: '',
          companyId: '',
          remarks: '',
          sort: 9,
          imageTip: '建议尺寸128px*128px，大小不超过2MB'
        }
      ],
      unitLevelList: [
        {
          value: '0',
          label: '省'
        },
        {
          value: '1',
          label: '市'
        },
        {
          value: '2',
          label: '县'
        },
        {
          value: '3',
          label: '乡镇'
        }
      ],
      imageClass: {
        APP_BANNER: 'upload-img-banner',
        PC_LOGINBG: 'upload-img-loginbg',
        PC_LOGINLOGO: 'upload-img-loginlogo',
        PC_INDEXBG: 'upload-img-indexbg',
        PC_INDEXLOGO: 'upload-img-indexlogo',
        PC_BROWSERLOGO: 'upload-img-browserlogo'
      },
      submitLoading: false,
      filterArr: [
        {
          label: '机构名称',
          name: 'name',
          value: '',
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      filterForm: {},
      modalFilterArr: [
        {
          label: '真实姓名',
          name: 'realName',
          value: '',
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      modalFilterForm: {},
      action: '',
      actions: '',
      headers: { Authorization: sessionStorage.token },
      detailType: [],
      orgLevel: [],
      // 其他
      companyId: '',
      departmentId: '',
      isEdit: false,
      options: [
        {
          value: 1,
          label: '虚拟机构'
        },
        {
          value: 2,
          label: '单位'
        },
        {
          value: 3,
          label: '内设机构'
        }
      ],
      optionsMap: {
        1: '虚拟机构',
        2: '单位',
        3: '内设机构'
      },
      crmTenantTypeMap: {
        1: '标准型单位',
        2: '项目型单位',
        3: '试用型单位'
      },
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      tempDeptList: [],
      tempDeptFlatList: [],
      // 表格数据列
      orgColumns: [
        ...(this.canPush
          ? [
            {
              title: ' ',
              width: 55,
              align: 'center',
              slot: 'checkboxSlot'
            }
          ]
          : []),
        {
          title: '机构名称',
          key: 'title',
          // align: "center",
          tree: true,
          //width: 500,
          render: (h, params) => {
            return h('span', {
              domProps: {
                innerHTML: highLightText(params.row.title, this.keyWord, 'red')
              }
            });
          }
        },
        {
          title: '机构类型',
          slot: 'orgType',
          align: 'center',
          width: 150
        },
        {
          title: '机构编码',
          key: 'orgCode',
          field: 'orgCode',
          align: 'center',
          minWidth: 150,
          tooltip: true,
          resizable: true,
          slot: 'orgCodeSlot'
        },
        {
          title: '统一社会信用代码',
          slot: 'creditCode',
          field: 'creditCode',
          align: 'center',
          minWidth: 150,
        },
        {
          title: '排序',
          slot: 'orderBy',
          field: 'orderBy',
          align: 'center',
          minWidth: 100
        },
        {
          key: 'dataIsIntegrity',
          field: 'dataIsIntegrity',
          title: '数据完整性',
          align: 'center',
          width: 120
        },

        {
          title: '单位类型',
          slot: 'crmTenantType',
          align: 'center',
          resizable: true,
          width: 150
        },

        {
          title: '客户标识',
          key: 'groupNumber',
          align: 'center',
          width: 240,
          resizable: true,
          render: (h, params) => {
            return h('span', {
              domProps: {
                innerHTML: highLightText(params.row.detailsData.groupNumber, this.keyWord, 'red')
              }
            });
          }
        },

        {
          title: '操作',
          slot: 'action',
          align: 'center',
          // fixed: 'right',
          resizable: true,
          // width: 520,
        }
      ],
      orgColumnsCrm: [
        // {
        //   title: "序号",
        //   type: "index",
        //   align: "center",
        //   width: 70,
        // },
        ...(this.canPush
          ? [
            {
              field: 'checkboxSlot',
              title: ' ',
              width: 55,
              align: 'center',
              slot: 'checkboxSlot'
              // renderHeader: (h) => {
              //   return h('Checkbox', {
              //     props: {
              //       value: !!(this.tempDeptFlatList && this.tempDeptFlatList.length && !this.tempDeptFlatList.find((item) => !this.selectedRowIds.includes(item.id))),
              //       disabled: !this.tempDeptFlatList.length
              //     },
              //     on: {
              //       input: (value) => {
              //         if(value){
              //           this.selectedRowIds = this.tempDeptFlatList.map((item) => item.id);
              //         }else{
              //           this.selectedRowIds = [];
              //         }
              //       }
              //     }
              //   });
              // }
            }
          ]
          : []),
        // {
        //   title: '单位码',
        //   align: 'center',
        //   width: 80,
        //   slot: 'unitCodeSlot',
        //   field: 'unitCodeSlot'
        // },
        {
          title: '机构名称',
          key: 'title',
          field: 'title',
          // align: "center",
          tree: true,
          //width: 500,
          render: (h, params) => {
            return h('span', {
              domProps: {
                innerHTML: highLightText(params.row.title, this.keyWord, 'red')
              }
            });
          }
        },
        {
          title: '机构类型',
          slot: 'orgType',
          field: 'orgType',
          align: 'center',
          width: 100
        },
        {
          title: '机构编码',
          key: 'orgCode',
          field: 'orgCode',
          align: 'center',
          width: 200,
          tooltip: true,
          resizable: true,
          slot: 'orgCodeSlot'
        },
        {
          title: '统一社会信用代码',
          slot: 'creditCode',
          field: 'creditCode',
          align: 'center',
          minWidth: 150
        },
        {
          title: '排序',
          slot: 'orderBy',
          field: 'orderBy',
          align: 'center',
          width: 70
        },
        {
          key: 'dataIsIntegrity',
          field: 'dataIsIntegrity',
          title: '数据完整性',
          align: 'center',
          width: 120
        },
        {
          title: '操作',
          slot: 'action',
          field: 'action',
          align: 'center',
          // fixed: 'right',
          // resizable: true,
          maxWidth: 520,
          width: 450
        }
      ],
      // 部门树选项
      deptOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      defaultParams: {
        label: 'addrName',
        value: 'addrCode',
        children: 'sysOrgAddrList'
      },
      // 表单参数
      form: {
        securityLevelCode: '',
        securityLevelCodeName: '',
        // level: 1,
        dossierNumber: '', //全宗号
        parentId: '0',
        parentName: '',
        title: '',
        type: '',
        branchLeaderId: '',
        departmentHeadIdList: [],
        hasSms: 1,
        hasWatermark: 1,
        showHomeCpj: '0',
        checkUp: 0,
        orgName: '',
        orgAbbreviation: '',
        aliasName: '', // 别名
        authorityDepartment: '',
        detailType: '',
        unitLevel: '',
        orderBy: 1,
        groupNumber: '',
        crmTenantType: '',
        nameOfAccountManager: '',
        customerManagerTelephone: '',
        customerAdministratorName: '',
        customerAdministratorTelephone: '',
        numberOfAccounts: '',
        projectContractPeriod: '',
        totalProjectAmount: '',
        /* 流程分屏编辑 */
        splitview: 0,
        /* 流程类型分屏范围 */
        modelDataTypeList: [],
        /* 流程名称分屏范围 */
        modelNameList: [],
        /* 短信账号KEY */
        smsKey: '',
        /* 短信账号名 */
        smsName: '',
        /* 单位类型 */
        unitType: '',
        /* 统一社会信用代码 */
        creditCode: '',
        authorizeAppList: [], // 已授权应用列表
        orgClassify: '', // 机构分类
        orgLine: '', // 系统条线
      },
      rulesDelete: {
        auditExplain: [{ required: true, message: '删除原因不能为空', trigger: 'blur' }],
      },
      // 表单校验
      rules: {
        creditCode: [
          {
            required: false,
            trigger: 'blur',
            validator: validateCreditCode
          }
        ],
        title: [
          {
            required: true,
            message: '上级部门不能为空',
            trigger: 'blur'
          }
        ],
        orgName: [{ required: true, message: '机构名称不能为空', trigger: 'blur' }],
        type: [
          {
            required: true,
            message: '机构类型不能为空',
            trigger: 'blur',
            type: 'number'
          }
        ],
        orderBy: [
          {
            required: true,
            message: '排序序号不能为空',
            trigger: 'blur',
            type: 'number'
          }
        ],
        smsName: [
          {
            trigger: 'blur',
            validator: validateSmsName
          }
        ],
        smsKey: [
          {
            validator: validateSmsKey,
            trigger: 'blur'
          }
        ],
        dutyPhone: [
          {
            required: false,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value && value.includes('，')) {
                callback(new Error('值班电话请使用英文逗号分隔'));
              } else if (value) {
                // 座机号
                const phonePattern = /^(\d{7,8}|\d{3,4}-?\d{7,8})(,(\d{7,8}|\d{3,4}-?\d{7,8}))*$/;
                if (!phonePattern.test(value)) {
                  callback(new Error('请输入正确的值班电话格式'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          }
        ],
        faxPhone: [
          {
            required: false,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value && value.includes('，')) {
                callback(new Error('传真电话请使用英文逗号分隔'));
              } else if (value) {
                // 座机号
                const phonePattern = /^(\d{7,8}|\d{3,4}-?\d{7,8})(,(\d{7,8}|\d{3,4}-?\d{7,8}))*$/;

                if (!phonePattern.test(value)) {
                  callback(new Error('请输入正确的传真电话格式'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          }
        ],
      },
      // 查看成员抽屉
      viewMemberDrawer: false,
      memberData: [],
      memberColumns: [
        // 复选框
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          type: 'index',
          width: 70,
          title: '序号',
          align: 'center'
        },
        {
          title: '用户名',
          key: 'loginName',
          minWidth: 150,
          align: 'center'
        },
        {
          title: '真实姓名',
          key: 'realName',
          minWidth: 250,
          align: 'center'
        },
        {
          title: '状态',
          slot: 'status',
          align: 'center'
        },
        {
          title: '是否领导',
          slot: 'departmentHead',
          align: 'center'
        },
        // {
        //   title: "默认部门",
        //   key: "orgType",
        //   align: "center",
        // },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          minWidth: 200
        }
      ],
      pageSize: 50,
      currentPage: 1,
      memberTotal: 0,
      memberId: '',
      isUnit: false,
      msg: '',
      keyWord: '',
      expandArr: [],
      actionId: '',
      actionItem: '',
      isUnitCrm: true,
      //单位客户经理等信息
      viewDepartCustomerDrawer: false,
      rowDepart: {
        nameOfAccountManager: '',
        customerManagerTelephone: '',
        customerAdministratorName: '',
        customerAdministratorTelephone: '',
        numberOfAccounts: '',
        projectContractPeriod: '',
        totalProjectAmount: '',
        groupNumber: ''
      },
      /* 选择正文编辑分屏范围弹窗控制 */
      isShowModel: false,
      /* 选择的分屏范围 */
      selectModelList: [],
      // 推送
      selectedRowIds: [],
      showOrgModal: false,
      // 推送应用
      showApplicationModal: false,
      showApplicationModalName: '',
      titleName: '授权',
      pushApplicationData: {},
      toBottomSign: false,
      preData: [],
      currentPageScroll: 1,
      pageSizeScroll: 10,
      dataCountScroll: 0,
      isAdmin: false,
      unitSynchroShow: false,
      currentData: {},
      unitTypeList: [
        { value: 0, label: '党委办' },
        { value: 1, label: '政府办' }
      ],
      orgLineList: [],
      orgClassifyList: [
        {
          value: '01',
          label: '学校'
        },
        {
          value: '02',
          label: '医院'
        }
      ],
      treeConfig: {
        transform: true,
        lazy: true,
        hasChildField: 'hasChild',
        // children: 'children',
        childrenField: 'children',
        rowField: 'id',
        parentField: 'parentId',
        // reserve: true,
        // iconOpen: 'vxe-icon-square-minus',
        // iconClose: 'vxe-icon-square-plus',
        expandRowKeys: []
      },
      rowDragConfig: {
        // isCrossDrag: true,
        // isSelfToChildDrag: true,
        // isToChildDrag: true,
        // showGuidesStatus: false,
        // trigger: 'cell',
        showDragTip: true,
        isPeerDrag: true,
        dragTip: '99988'
      },
      rowConfig: { drag: true },
      tableKey: 1,
      itemData: [],
      itemIndex: 0,
      orgCodePath: '',
      // 是否为虚拟机构
      isVirtualOrg: false,
      allOrdList: [],
      currentUploadRow: {},
      isAuthorized: false,
      isPush: false, // 是否推送
      isSubmitDisabled: false, // 是否禁用提交按钮
      appAlreadyPushList: [], // 已经推送的应用列表
      regionAdmin: false, // 是否区划管理员
      // 授权记录
      showHistoryModal: false,
      historyList: [],
      historyColumns: [
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h('span', params.index + (this.historgCurrentPage - 1) * this.historyPageSize + 1);
          }
        },
        {
          key: 'appName',
          title: '系统名称',
          align: 'center'
        },
        {
          key: 'appCode',
          title: '编号',
          align: 'center',
          width: 90
        },
        {
          key: 'strOperaType',
          title: '操作类型',
          align: 'center',
          slot: 'operateTypeSlot',
          width: 95
        },
        {
          key: 'inSystemFlag',
          title: '是否内部系统推送',
          align: 'center',
          slot: 'inSystemFlagSlot',
          width: 150
        },
        {
          key: 'strUnitName',
          title: '机构名称',
          align: 'center',
          width: 150
        },
        {
          key: 'strId',
          title: '机构strId',
          align: 'center',
          width: 150
        },
        {
          key: 'requestMode',
          title: '推送方式',
          align: 'center',
          slot: 'requestModeSlot',
          width: 150
        },
        {
          key: 'syncSuccess',
          title: '响应状态',
          align: 'center',
          slot: 'responseStatusSlot',
          width: 95
        },
        {
          key: 'syncMessage',
          title: '返回信息',
          align: 'center',
          tooltip: true,
          // slot: 'failedReasonSlot',
          width: 150
        },
        {
          key: 'sourceId',
          title: '数据来源Id',
          align: 'center',
          width: 150
        },
        {
          title: '推送时间',
          key: 'createTime',
          align: 'center',
          width: 165,
          sortable: true,
          render: (h, params) => {
            return h('span', {}, params.row.createTime.substring(0, 16));
          }
        },
      ],
      historyFilterArr: [
        {
          label: '授权时间',
          name: 'createTime',
          value: [],
          labelWidth: 100,
          type: 3 //1-输入框2-下拉菜单3-日期时间段
        }
      ],
      historyFilterForm: {},
      tableLoading: false,
      historyTotal: 0,
      historyPageSize: 10,
      historgCurrentPage: 1,
      moderatorFlag: false,
      isRefush: false,
    };
  },
  computed: {
    selectOrgByIdApiName() {
      return this.isRegion ? 'selectRegionOrgById' : 'selectOrgById';
    },
    // getOrgInfo() {
    // return JSON.parse(
    //   localStorage.getItem('orgInfo') || '{}'
    // );
    // }
  },
  watch: {
    // open(val) {
    //   if (val) {
    //     this.$nextTick(() => {
    //       this.$refs.tenantUnitSelect.getOrgList();
    //     });
    //   }
    // },
    open: {
      handler(val) {
        // this.filterForm = val;
        this.getOrgInfo = {
          ...JSON.parse(
            localStorage.getItem('orgInfo') || '{}'
          )
        }
      },
      deep: true
    },
  },
  watch: {
    open(val) {
      if (!val) {
        // 弹窗关闭
        this.creditCodeOrg = []
      }
    },
  },
  async created() {
    // historyFilterArr 里的创建时间默认一个月内 yy-mm-dd 格式
    const now = new Date(+new Date() + 8 * 3600 * 1000);
    const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    this.historyFilterArr.forEach(item => {
      if (item.name === 'createTime') {
        item.value = [
          oneMonthAgo.toISOString().substring(0, 10),
          now.toISOString().substring(0, 10)];
      }
    });
    this.historyFilterForm.createTime = [
      oneMonthAgo.toISOString().substring(0, 10),
      now.toISOString().substring(0, 10)
    ];
    let userInfo = this.$byStoreGet('userInfo');
    console.log(userInfo, 'userInfo');
    this.userInfo = JSON.parse(sessionStorage.getItem('setUserInfo'))
    // 版主用户也查所有机构
    this.isAdmin = userInfo.id == 1 || userInfo.id == 2 || userInfo.id == 3 || userInfo.id == 5 || userInfo.roleCodes.includes("platform_role");
    this.regionAdmin = this.userInfo.roleCodes.includes("region_role")
    this.isWw = await isWwBySysConfig();
    console.log(this.isWw, 'isWw');
    await this.getList(this.isRegion ? undefined : 0);
    this.getOrgLabelList()
    this.getDetailType();
    this.getOrgLevel();
    this.getSecurity();
    this.getOrgLineList();
    this.getIsPersonUser();
  },
  methods: {
    // 如果是版主并且是区划管理员 只能操作区划下的单位或者机构 其余只能推送查看
    isRegionAdminANDmoderator(row) {
      // 如果是版主但不是单位管理员不是区划管理员 直接返回false
      if (this.userInfo.moderatorAppIdList.length > 0 && !this.regionAdmin && !this.userInfo.roleCodes.includes("unit_role")) {
        return false
      }
      if (this.userInfo.moderatorAppIdList.length > 0 && this.regionAdmin) {
        const orgCode = this.userInfo.cscpOrgDTO?.orgCode || '';
        // 如果orgCode的第五和第六位是00则是市级区划机构 如果不是则是县级区划机构
        if (orgCode.length >= 6 && orgCode.substring(4, 6) === '00') {
          // 市级区划机构
          return row.orgCode.startsWith(orgCode.substring(0, 4));
        } else {
          // 县级区划机构
          return row.orgCode.startsWith(orgCode.substring(0, 6));
        }
      } else {
        // 非版主用户
        return true;
      }
    },
    // 如果是版主 并且是单位管理员 只能操作单位下的机构和部门 其余只能推送查看
    isUnitAdminANDmoderator(row) {
      // 如果是版主但不是单位管理员不是区划管理员 直接返回false
      if (this.userInfo.moderatorAppIdList.length > 0 && !this.regionAdmin && !this.userInfo.roleCodes.includes("unit_role")) {
        return false
      }
      if (this.userInfo.moderatorAppIdList.length > 0 && this.userInfo.roleCodes.includes("unit_role")) {
        // 如果是单位管理员
        return row.orgCode.startsWith(this.userInfo.cscpOrgDTO?.orgCode || '');
      } else {
        // 非单位管理员
        return true;
      }
    },
    creditCodeSearch() {
      const { creditCode } = JSON.parse(JSON.stringify(this.formCreditCode));
      // 社会信用代码搜索
      if (creditCode === '') {
        this.$Message.warning('请输入社会信用代码');
        return;
      }
      this.$refs.formCreditCodeRef.validate((valid) => {
        if (valid) {
          let msg = this.$Message.loading({
            content: '正在查询中...',
            duration: 0
          });
          this.$api.org.selectOrgByCreditCode(creditCode).then((res) => {
            console.log(res, 'res');
            let data = res.data.resultData.map((item) => {
              return {
                ...item,
                title: item.orgName,
                orgCode: item.orgCode || '',
                creditCode: item.creditCode || '',
                hasChild: false,
                nodeKey: item.id
              };
            });
            this.creditCodeData = data.length ? data : [
              {
                title: '暂未查询到机构，信用代码可用于新增机构',
                orgCode: '',
                creditCode: '',
                hasChild: false,
                nodeKey: ''
              }
            ];
            console.log(this.creditCodeData, 'creditCodeData');
          }).finally(() => {
            msg();
          });
        }
      });
    },
    onCancel() {
      this.creditCodeShow = false;
      this.formCreditCode = { creditCode: '' }
      this.creditCodeData = [];
      this.$refs.formCreditCodeRef.resetFields();
    },
    //获取应用列表
    async getApplicationList() {
      const params = {
        currentPage: 1,
        pageSize: 1000,
        // autoPush: 1,
        status: 1
      }
      const { data } = await this.$api.applicationManager.queryTSyncAppSystemManagePage(params);
      // 自动推送的应用
      this.appList = data.resultData.data;
      this.appAutoPushList = data.resultData.data.filter(item => item.autoPush == 1);
    },
    openApplicationModal() {
      if (this.disabled) return;
      this.showApplicationModal = true;
    },
    onPushApplicationOk() {
      if (this.isAuthorized) {
        // 授权弹窗 提交调用接口
        let pushAppCode = this.$refs.pushApp.selectedRowData.map(app => app.appCode)
          .filter(appCode => !!appCode)
          .join(',');
        if (!pushAppCode) {
          this.$Message.warning('请选择要授权的应用');
          return;
        }
        this.isSubmitDisabled = true;
        this.$api.applicationManager.autoOrgApp({
          pushAppCode,
          id: this.tableOrgInfo.id,
        }).then(res => {
          if (res.data.resultCode == 20000) {
            this.$Message.success('授权成功');
            this.showApplicationModal = false;
            this.isSubmitDisabled = false;
            // 刷新应用列表
            // this.getApplicationList();
            this.refushOrgItem()
          } else {
            this.$Message.error(res.data.resultMessage || '授权失败');
            this.isSubmitDisabled = false;
          }
        }).catch(() => {
          this.isSubmitDisabled = false;
        });
      } else {
        // 新增/编辑
        // 拿到已经选中的应用
        this.form.authorizeAppList = this.$refs.pushApp.selectedRowData || [];
        this.showApplicationModal = false;
      }
    },
    // 获取用户类型
    getIsPersonUser() {
      const userType = this.$byStoreGet('userInfo').userType;
      // 判断是否普通用户
      this.isPersonUser = userType === 'PERSON_USER';
    },
    // 当前选择的数据
    selectChangeEvent() {
      // console.log(this.$refs.tableRef.getCheckboxRecords(true))
      // handleSelectRow(this.$refs.tableRef.getCheckboxRecords(true))
    },
    selectChangeEventExpand({ row }) {
      // if (this.treeConfig.transform === false && this.treeConfig.lazy === false && this.filterForm.name != "" && row.hasChild) {
      //   // 点击才是查询这个接口
      //   this.handleLoadData(row)
      //   // console.log(e, '看里面有啥');
      // }
    },
    highLightTextFun(title, keyWord, color) {
      return highLightText(title, keyWord, color);
    },
    securityChange(e) {
      this.form.securityLevelCodeName = this.securityType.filter(v => v.code == e)[0].name;
    },
    // 获取数据字典中的密级
    getSecurity() {
      this.$api.dictionary
        .getDictRecordByDictCode({ code: 'securityClassified' })
        .then(({ data }) => {
          const { resultCode, resultData = [] } = data;
          if (resultCode == 20000) {
            this.securityType = resultData;
          }
        });
    },
    // 获取系统条线字典
    getOrgLineList() {
      this.$api.dictionary
        .getDictRecordByDictCode({ code: 'org_line' })
        .then(({ data }) => {
          const { resultCode, resultData = [] } = data;
          if (resultCode == 20000) {
            this.orgLineList = resultData.map(n => {
              return {
                label: n.name,
                value: n.code
              };
            });
          }
        });
    },
    orgLabelClick() {
      this.personShow = true
      this.selectLabelList = [...this.form.orgLabelList || []];
      // 回显
      this.orgLabelList.forEach(n => {
        n.checked = this.selectLabelList.some(m => m.id == n.id)
      })
    },
    // 获取数据字典里的机构标签
    getOrgLabelList() {
      this.$api.dictionary
        .getDictRecordByDictCode({ code: 'org_label' })
        .then(({ data }) => {
          const { resultCode, resultData = [] } = data;
          if (resultCode == 20000) {
            const list = resultData.map(n => {
              return {
                title: n.name,
                id: n.code,
                checked: false
              };
            });
            this.orgLabelList = list;
            console.log(this.orgLabelList, 'orgLabelList');
          }
        });
    },
    changePersonTree(arr, obj) {
      console.log(arr, obj, 'changePersonTree');
      if (obj && obj.checked) {
        this.selectLabelList.push(obj)
      } else {
        this.selectLabelList = this.selectLabelList.filter(v => v.id != obj.id)
      }
    },
    handlePersonOk() {
      if (this.selectLabelList.length > 0) {
        // const list = this.uniqueArray([
        //   ...(this.form.orgLabelList || []),
        //   ...this.selectLabelList
        // ])
        this.form.orgLabelList = [...this.selectLabelList];
        this.personShow = false;
      }
    },
    uniqueArray(array) {
      return array.reduce((acc, current) => {
        const x = acc.find(item => item.id === current.id);
        if (!x) {
          acc.push(current);
        }
        return acc;
      }, []);
    },
    handlerSms(val) {
      this.$nextTick(() => {
        this.$refs['form'].validateField(val);
      });
    },
    //获取全宗号
    handleDossierNumber() {
      this.$api.archives
        .getdossierNumber()
        .then(res => {
          if (res && res.data.resultCode == 20000) {
            this.form.dossierNumber = res.data.resultData || '';
          }
        })
        .finally(() => { });
    },
    deleteBanner(index) {
      if (this.mList.length == 5) {
        return;
      }
      this.mList = this.mList.filter((v, i) => i != index);
    },
    addBanner() {
      this.mList.push({
        title: 'APP轮播图',
        // app轮播图
        labTitle: 'APP轮播图',
        code: 'APP_BANNER',
        tenantId: '',
        companyId: '',
        remarks: '',
        sort: this.mList.length + 1,
        imageTip: '建议尺寸128px*128px，大小不超过2MB'
      });
    },
    setBannerList(i, data) {
      this.mList[i].remarks = data.remarks;
      this.mList[i].title = data.title;
      this.mList[i].sort = data.sort;
      if (data.picList.length > 0) {
        this.mList[i].extName = data.picList[0].extName;
        this.mList[i].fileName = data.picList[0].fileName;
        this.mList[i].fileSize = data.picList[0].fileSize;
        this.mList[i].imgPath = data.picList[0].imgPath;
        this.mList[i].imgUrl = data.picList[0].imgUrl;
      } else {
        this.mList[i].extName = '';
        this.mList[i].fileName = '';
        this.mList[i].fileSize = '';
        this.mList[i].imgPath = '';
        this.mList[i].imgUrl = '';
      }
    },
    // 添加一个辅助方法来扁平化树结构
    flattenTree(tree) {
      let result = [];
      const flatten = nodes => {
        nodes.forEach(node => {
          result.push(node);
          if (node.children && node.children.length > 0) {
            flatten(node.children);
          }
        });
      };
      flatten(tree);
      return result;
    },
    testSearch(params) {
      this.filterForm = JSON.parse(JSON.stringify(params));
      if (this.filterForm.name !== '') {
        this.treeConfig.transform = false;
        // this.treeConfig.lazy = false;
        // if (!this.canPush || this.isAdmin) {
        let msg = this.$Message.loading({
          content: '正在更新数据',
          duration: 0
        });
        let apiParams = null
        if (this.isRegion || (this.regionAdmin && this.canPush)) {
          apiParams = {
            orgName: this.filterForm.name,
          }
        } else {
          apiParams = this.filterForm.name
        }
        let api = (this.isRegion || (this.regionAdmin && this.canPush)) ? this.$api.org.selectDivisionOrgByIdName : this.$api.org
          .getOrgHierarchyByNameNewest;
        api(apiParams)
          .then(response => {
            // 处理返回的树形结构数据
            const processTreeData = data => {
              return data.map(item => {
                const node = {
                  id: item.id,
                  parentId: item.parentId,
                  creditCode: item.creditCode,
                  title: item.title,
                  name: item.name,
                  orgCode: item.orgCode,
                  dataIsIntegrity: item.dataIsIntegrity == '1' ? '不完整' : '完整',
                  type: item.type,
                  detailsData: item,
                  children: item.children || [],
                  hasChild: (item.children && item.children.length > 0) || item.isValuable == 1,
                  _loading: false,
                  orderBy: item.orderBy,
                  isDistrictOrStreet: this.isDistrictOrStreet(item.orgCode) // 判断是否为区县或街道
                };
                // 如果有子节点，递归处理
                if (node.children && node.children.length > 0) {
                  node.children = processTreeData(node.children);
                }
                if (item.children && item.children.length > 0) {
                  this.$nextTick(() => {
                    if (this.$refs.tableRef) {
                      setTimeout(() => {
                        const $table = this.$refs.tableRef
                        if ($table) {
                          $table.setTreeExpand(item, true)
                        }
                      }, 0);
                    }
                  });
                } else if (item.isValuable) {
                  // 如果没有子节点但有isValuable字段，设置hasChild为true
                  node.hasChild = true
                  this.$set(node, 'children', [])
                }
                console.log(node.hasChild, 'node', node);
                return node;
              });
            };

            // 处理返回的数据
            const processedData = processTreeData(response.data.resultData);

            this.tempDeptList = processedData
            this.search(this.tempDeptList, this.filterForm.name, 'title');

            // 使用 nextTick 确保 DOM 更新后再展开节点
            // this.$nextTick(() => {
            //   if (this.$refs.tableRef) {
            //     setTimeout(() => {
            //       const $table = this.$refs.tableRef
            //       if ($table) {
            //         $table.setAllTreeExpand(true)
            //       }
            //     }, 0);
            //   }
            // });
          })
          .finally(() => {
            this.treeConfig.transform = true;
            // this.treeConfig.lazy = true;
            msg();
          });
        // } else {
        //   let cacheList = JSON.parse(JSON.stringify(this.preData));
        //   this.search(cacheList, this.filterForm.name, 'title');
        //   this.tempDeptList = cacheList;
        //   this.tempDeptFlatList = cacheList;
        //   this.$refs.tableBox.scrollTop = 0;
        // }
      } else {
        this.treeConfig.transform = true;
        this.treeConfig.lazy = true;
        this.keyWord = '';
        this.getList(this.isRegion ? undefined : 0);
      }
    },
    // ======================上传相关方法开始==========================
    handleMaxSize(file) {
      this.$Message.error('文件' + file.name + '超出指定大小限制');
    },
    handleBeforeUpload(file) {
      this.msg = this.$Message.loading({
        content: '正在上传',
        duration: 0
      });
    },
    isAdmins() {
      const userType = this.$byStoreGet('userInfo').userType;
      let roleCodes = JSON.parse(sessionStorage.getItem('setUserInfo')).roleCodes || [];
      // console.log(roleCodes, 'roleCodes');
      // 判断是否为admin 或者 区划管理员账号
      return userType === 'SYSTEM_USER' || roleCodes.includes('region_role');
    },
    handleFormatError(file, fileList) {
      this.$Message.error('文件格式错误');
    },
    handleSuccess(response, file, fileList) {

      this.currentUploadRow.hasChild = true; // 确保显示展开按钮
      this.$refs.tableRef.reloadTreeExpand(this.currentUploadRow); // 清除加载状态
      if (response.resultCode == 20000) {
        this.$Message.success({
          // content: `导入成功，可前往“系统设置” - “导入结果页面”查看结果`,
          duration: 5,
          render: h => {
            return h('span', [
              '导入成功，可前往“系统设置” - “',
              h(
                'a',
                {
                  on: {
                    click: () => {
                      this.viewResult();
                    }
                  }
                },
                '导入结果'
              ),
              '"页面查看结果'
            ]);
          }
        });
        this.msg();
        this.$emit('setIsEdit');
        // this.$Message.destroy()
        // this.getList(this.isRegion ? undefined : 0);
      } else {
        this.$Message.error('导入失败');
      }
    },

    handleSuccessOrg(response, file, fileList) {
      if (response.resultCode == 20000) {
        this.$Message.success({
          // content: `导入成功，可前往“系统设置” - “导入结果页面”查看结果`,
          duration: 5,
          render: h => {
            return h('span', [
              '导入成功，可前往“系统设置” - “',
              h(
                'a',
                {
                  on: {
                    click: () => {
                      this.viewResult();
                    }
                  }
                },
                '导入结果'
              ),
              '"页面查看结果'
            ]);
          }
        });
        this.msg();
        this.$emit('setIsEdit');
        this.getList(this.isRegion ? undefined : 0);
      } else {
        this.$Message.error('导入失败');
      }
    },
    handleError(error, file, fileList) {

      this.msg();
    },
    viewResult() {
      this.$router.push({ name: 'importResult' });
    },
    // ======================上传相关方法结束==========================
    // 下载导入模板
    handleTemplate(name) {
      const templateName = `${name}.xlsx`;
      downloadTemplate(this, templateName);
    },

    /**
     * @description: 导入部门 ｜ 单位
     * @param {*} row 当前行
     * @param {*} type 导入类型：department(部门) | company(单位)
     * @return {*}
     */
    handleImport(row, type = 'department') {
      this.action =
        this.$util.getAjaxUrl('workflow') +
        `/api/orgImportAndExport/${type === 'department' ? 'uploadDepartment' : 'uploadCompany'}/${row.id
        }`;
    },
    handleImportDept(row, type = 'department') {
      this.currentUploadRow = row;
      this.action =
        this.$util.getAjaxUrl('workflow') +
        `/api/orgImportAndExport/${type === 'department' ? 'uploadDepartment' : 'uploadCompany'}/${row.id
        }`;
      // 获取 Upload 组件的根元素，并查找内部的 input
      const uploadElement = this.$refs.uploadRef.$el;
      const fileInput = uploadElement.querySelector('input[type="file"]');

      // 触发文件选择
      if (fileInput) {
        fileInput.click();
      }
    },
    handleUploadLevelAll(row) {
      this.currentUploadRow = row;
      this.action =
        this.$util.getAjaxUrl('workflow') + `/api/orgImportAndExport/uploadLevelAll/${row.id}`;
      // 获取 Upload 组件的根元素，并查找内部的 input
      const uploadElement = this.$refs.uploadRef.$el;
      const fileInput = uploadElement.querySelector('input[type="file"]');

      // 触发文件选择
      if (fileInput) {
        fileInput.click();
      }
    },
    uploadLevelCompany() {
      this.actions =
        this.$util.getAjaxUrl('workflow') + `/api/orgImportAndExport/uploadLevelCompany`;
    },
    // 导出部门
    handleExport(row) {
      let msg = this.$Message.loading({
        content: '正在导出内设机构',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      axios({
        method: 'get',
        url: baseUrl + `/api/orgImportAndExport/exportDepartment/${row.id}`,
        responseType: 'blob',
        headers: {
          Authorization: sessionStorage.token
        }
      })
        .then(res => {
          const link = document.createElement('a');
          let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = `${row.title}内设机构表.xlsx`; //下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          if (res.status == 200) {
            this.$Message.success('导出成功');
          } else {
            this.$Message.error('导出失败');
          }
        })
        .finally(() => {
          msg();
        });
    },
    handleExportAsync(row) {
      let msg = this.$Message.loading({
        content: '正在导出内设机构',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      axios({
        method: 'get',
        url: baseUrl + `/api/orgImportAndExport/exportOrgById/${row.id}`,
        // responseType: 'blob',
        headers: {
          Authorization: sessionStorage.token
        }
      })
        .then(res => {
          if (res.data.resultCode == 20000) {
            this.$Message.success('导出成功');
          } else {
            this.$Message.error('导出失败');
          }
          this.exportShow = true
          this.exportUrl = res.data.resultData
          // const link = document.createElement('a');
          // let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
          // link.style.display = 'none';
          // link.href = URL.createObjectURL(blob);
          // link.download = `${row.title}内设机构表.xlsx`; //下载的文件名
          // document.body.appendChild(link);
          // link.click();
          // document.body.removeChild(link);
          // if (res.status == 200) {
          //   this.$Message.success('导出成功');
          // } else {
          //   this.$Message.error('导出失败');
          // }
        })
        .finally(() => {
          msg();
        });
    },
    copyText(text) {
      const input = document.createElement('input');
      input.setAttribute('value', text);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$Message.success('复制成功');
    },
    // 导出单位
    handleExportCompany(row) {
      let msg = this.$Message.loading({
        content: '正在导出单位',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      axios({

      })
    },
    // 选择所属部门
    handleSelectUser() {
      if (this.form.authorityDepartment) {
        this.$refs.department.selectedId = JSON.parse(
          JSON.stringify(this.form.authorityDepartment)
        );
      }
      this.$refs.department.init();
    },
    // 获取所选部门
    getDepartment(depart) {
      if (depart.length > 0) {
        this.form.authorityDepartment = depart[0].id;
        // this.numberManageAddForm.belongingDepartmentName = depart[0].title;
      }
    },
    // 获取机构详细类型
    getDetailType() {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'orgDetailType' }).then(res => {
        if (res.data.resultCode == 20000) {
          this.detailType = res.data.resultData;
        }
      });
    },
    // 获取单位级别
    getOrgLevel() {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'orgLevel' }).then(res => {
        if (res.data.resultCode == 20000) {
          this.orgLevel = res.data.resultData;
        }
      });
    },
    // 选择分管领导
    selectBranchLeaderId() {
      if (!!this.form.branchLeaderId) {
        this.$refs.branchLeaderIdSelect.userList = [
          JSON.parse(JSON.stringify(this.form.branchLeaderId))
        ];
      }
      this.$refs.branchLeaderIdSelect.userSelectModal = true;
    },
    getBranchLeaderId(params) {
      params = JSON.parse(JSON.stringify(params));
      this.$set(this.form, 'branchLeaderId', params[0] ? params[0] : '');
    },
    // 选择部门领导
    openRoleModel() {
      if (!!this.form.departmentHeadIdList) {
        this.$refs.departmentHeadIdListSelect.workGroupList = this.form.departmentHeadIdList;
      }
      this.$refs.departmentHeadIdListSelect.init(this.departmentId);
    },
    getDepartmentHeadIdList(params) {
      params.map(n => {
        return JSON.parse(JSON.stringify(n));
      });
      this.$set(this.form, 'departmentHeadIdList', params);
    },
    // 删除部门领导/分管领导
    handleCloseLeaderId() {
      this.form.branchLeaderId = '';
    },
    handleCloseLeader(item, index) {
      this.form.departmentHeadIdList.splice(index, 1);
    },
    typeChange(item) {
      console.log(item, 'item');
      this.form.creditCode = '';
      if (item != 3) {
        this.form.branchLeaderId = '';
        this.form.departmentHeadIdList = [];
      }
      // 如果item不为2 即不是单位类型时，清空
      // if (item != 2) {
      //   this.form.orgClassify = '';
      //   this.form.unitType = '';
      //   this.form.dutyPhone = '';
      //   this.form.faxPhone = '';
      // }
    },
    /** 查询部门列表 */
    async getList(id) {
      let msg = this.$Message.loading({
        content: '正在更新数据',
        duration: 0
      });
      // if (!this.canPush || this.isAdmin) {
      this.$api.org[this.selectOrgByIdApiName]({ id: id })
        .then(response => {
          this.tempDeptList = response.data?.resultData.map(n => {
            return n.isValuable
              ? {
                id: n.id,
                title: n.title,
                name: n.title,
                creditCode: n.creditCode,
                orgCode: n.orgCode,
                type: n.type,
                dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
                detailsData: n,
                hasChild: true,
                parentId: n.parentId,
                children: [],
                _loading: false,
                orderBy: n.orderBy,
                isDistrictOrStreet: this.isDistrictOrStreet(n.orgCode) // 判断是否为区县或街道
              }
              : {
                id: n.id,
                title: n.title,
                name: n.title,
                creditCode: n.creditCode,
                orgCode: n.orgCode,
                type: n.type,
                dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
                detailsData: n,
                parentId: n.parentId,
                orderBy: n.orderBy,
                isDistrictOrStreet: this.isDistrictOrStreet(n.orgCode) // 判断是否为区县或街道
              };
          });
          // console.log(this.tempDeptList, 'this.tempDeptList');

          this.tempDeptFlatList = this.tempDeptList;
          let obj = [...this.tempDeptList];
          let flag = obj.some(v => v.detailsData.crmTenantType);
          if (flag) {
            this.isUnitCrm = false;
          }
        })
        .finally(() => {
          this.loading = false;
          msg();
        });
      // }
      // else {
      // 不是admin和单位管理员账号时，即区划管理员
      // this.$api.org
      //   .selectOrgList({
      //     parentId: '0'
      //   })
      //   .then(response => {
      //     this.preData = response.data.resultData;
      //     this.dataCountScroll = this.preData.length;
      //     this.currentPageScroll = 1;
      //     this.tempDeptList = this.expandTreeData(this.preData.slice(0, this.pageSizeScroll));
      //     this.tempDeptFlatList = this.tempDeptList;
      //     let flag = this.preData.some(v => v.detailsData.crmTenantType);
      //     if (flag) {
      //       this.isUnitCrm = false;
      //     }
      //   })
      //   .finally(() => {
      //     this.loading = false;
      //     msg();
      //   });
      // }

      const newCols = await initColumnsByCipherMachine(
        this.isUnitCrm ? this.orgColumnsCrm : this.orgColumns
      );
      this.orgColumnsCrm = newCols;
      if (!this.isUnitCrm) {
        this.orgColumns = newCols;
      }
    },
    handleTableScroll(e) {
      if (this.tempDeptList.length && !this.filterForm.name && this.canPush) {
        if (e.target.scrollTop + e.target.offsetHeight + 6 > e.target.scrollHeight) {
          // 视为触底
          if (!this.toBottomSign) {
            this.toBottomSign = true;
            if (this.currentPageScroll * this.pageSizeScroll < this.dataCountScroll) {
              this.currentPageScroll = this.currentPageScroll + 1;
              const startIndex = (this.currentPageScroll - 1) * this.pageSizeScroll;
              this.tempDeptList.push(
                ...this.expandTreeData(
                  this.preData.slice(startIndex, startIndex + this.pageSizeScroll)
                )
              );
              this.tempDeptFlatList = this.tempDeptList;
            }
          }
        } else {
          this.toBottomSign = false;
        }
      }
    },
    // 展开数据
    expandTreeData(data) {
      let list = [];
      data.forEach(item => {
        list.push({
          ...item,
          children:
            item.children && item.children.length
              ? this.expandTreeData(item.children)
              : item.children,
          _showChildren: true
        });
      });
      return list;
    },
    rowDragendEvent({ newRow, oldRow, dragPos }) {


      let orgList = []
      const treeParent = this.$refs.tableRef.getTreeParentRow(newRow.id)
      if (treeParent && treeParent.id) {
        orgList = treeParent.children
      } else {
        const { fullData, visibleData, tableData, footerData } = this.$refs.tableRef.getTableData()
        orgList = fullData
      }
      if (orgList && orgList.length) {
        orgList.forEach((item, index) => {
          item.orderBy = index + 1
        })

        let params = orgList.map((item, index) => {
          return {
            id: item.id,
            orderBy: index + 1
          }
        })

        orgList.forEach((item, index) => {
          if (this.allOrdList.length == 0 || !this.allOrdList.find(r => r.id == item.id)) {
            this.allOrdList.push(item)
          }
        })

        this.$api.org.updateOrgOrderBy(params).then(res => {
          if (res.data.resultCode == 20000) {
            this.$Message.success('排序成功');
          } else {
            this.$Message.error('排序失败');
          }
        })
      }

      console.log(`拖拽完成，被拖拽行：${oldRow.name} 目标行：${newRow.name} 目标位置：${dragPos}`)
    },
    async handleLoadData(item) {
      console.log(item, 'item');
      // 如果已经加载过了，直接返回
      if (item.children && item.children.length > 0 && !this.isRefush) {
        return item.children;
      } else {
        this.isRefush = false;
      }

      // return new Promise((resolve, reject) => {
      //   this.$api.org[this.selectOrgByIdApiName]({ id: item.id }).then(response => {

      //     const dataSource = response.data.resultData.map(n => {
      //       return n.isValuable
      //         ? {
      //           id: n.id,
      //           title: n.title,
      //           orgCode: n.orgCode,
      //           type: n.type,
      //           dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
      //           detailsData: n,
      //           creditCode: n.creditCode,
      //           hasChild: true,
      //           children: [],
      //           _loading: false
      //         }
      //         : {
      //           id: n.id,
      //           title: n.title,
      //           orgCode: n.orgCode,
      //           type: n.type,
      //           creditCode: n.creditCode,
      //           dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
      //           detailsData: n
      //         };
      //     });
      //     // this.tempDeptFlatList = [...this.tempDeptFlatList, ...dataSource];
      //     // this.$set(item, 'children', dataSource);
      //     console.log('---dataSource----');
      //     this.$nextTick(() => {
      //       this.$refs.tableRef.refreshRow(item);
      //     });

      //     resolve(dataSource);
      //   });

      // })

      const { data } = await this.$api.org[this.selectOrgByIdApiName]({ id: item.id });

      const dataSource = data.resultData.map(n => {
        return n.isValuable
          ? {
            id: n.id,
            title: n.title,
            orgCode: n.orgCode,
            type: n.type,
            name: n.title,
            dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
            detailsData: n,
            creditCode: n.creditCode,
            hasChild: true,
            children: [],
            _loading: false,
            parentId: n.parentId,
            orderBy: n.orderBy,
            isDistrictOrStreet: this.isDistrictOrStreet(n.orgCode) // 判断是否为区县或街道
          }
          : {
            id: n.id,
            title: n.title,
            orgCode: n.orgCode,
            type: n.type,
            name: n.title,
            creditCode: n.creditCode,
            dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
            detailsData: n,
            parentId: n.parentId,
            orderBy: n.orderBy,
            isDistrictOrStreet: this.isDistrictOrStreet(n.orgCode) // 判断是否为区县或街道
          };
      });
      this.tempDeptFlatList = [...this.tempDeptFlatList, ...dataSource];
      item.children = Object.freeze(dataSource)
      // this.$set(item, 'children', Object.freeze(dataSource));

      return dataSource;
    },
    // 搜索事件
    search(tree, keyWord, name) {
      this.keyWord = keyWord ? JSON.parse(JSON.stringify(keyWord)) : '';
      // searchTree(tree, keyWord, name);
      // this.tempDeptFlatList = this.tempDeptFlatList.filter((item) => item.title.indexOf(keyWord) > -1);
      // 展开全部

      // this.expandAllNodes();
      this.loading = false;
    },
    expandAllNodes() {
      const table = this.$refs.tableRef;
      this.$nextTick(() => {
        table.setAllTreeExpand(true);
        //   const nodesToExpand = this.tempDeptList.filter(node => {
        //     return node?.children?.length>0
        //   });
        //   let currentIndex = 0;
        //   const expandNextNode = () => {
        //     if (currentIndex < nodesToExpand.length) {
        //       const node = nodesToExpand[currentIndex];
        //       this.$nextTick(() => {
        //       table.setTreeExpand(node, true);
        //   })
        //       currentIndex++;
        //       requestAnimationFrame(expandNextNode);
        //     }
        //   };
        //   expandNextNode();
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = this.$options.data().form;
      this.$refs.form.resetFields();
      this.banner = 3;
    },
    /** */
    /**
     * @description: 新增 / 编辑按钮操作
     * @param {*} row 新增/编辑的当前对象
     * @param {*} status 0 编辑｜ 1 新增
     * @param {*} code /按钮权限码   有值：是开放给用户的新增或编辑
     * @return {*}
     */
    async handleAdd(row, status, code) {
      await this.getApplicationList().catch(err => {
        this.$Message.error('获取应用列表失败，请稍后重试');
      });
      this.currentUploadRow = row;
      let orderBy = 0;
      if (row && row.id) {
        const org = this.allOrdList.find(r => r.id == row.id)
        if (org) {
          orderBy = org.orderBy
        } else {
          orderBy = row.detailsData.orderBy
        }
      }
      console.log(row, 'row');
      let data = { ...row.detailsData }
      if (row) {
        // row有值 表示在已有的机构下编辑和新增机构
        this.orgCodePath = data.orgCodePath
        const { detailsData } = row;
        // detailsData.type 为1 表明为类型为虚拟机构 创建的子机构不能为内设机构
        if (detailsData.type == 1 && status == 1) {
          this.isVirtualOrg = true;
        } else {
          this.isVirtualOrg = false;
        }
        // 如果是区划机构管理编辑，并且localStorage里的orgInfo里的id和当前操作人strid相同才覆盖
        if (this.isRegion && this.getOrgInfo.id == this.userInfo.strId && status == 0) {
          this.orgIdOptions = this.getOrgInfo.orgIdOptions;
        }
        this.companyId = detailsData.companyId;
        this.departmentId = detailsData.id;
        this.actionId = status ? row.id : detailsData.parentId;
        this.actionItem = row;
        if (
          detailsData.splitview &&
          detailsData.modelDataTypeList &&
          detailsData.modelDataTypeList.length > 0
        ) {
          this.selectModelList = detailsData.modelDataTypeList.map((item, index) => {
            return {
              modelDataType: item,
              modelName: detailsData.modelNameList[index]
            };
          });
        }
      } else {
        this.actionId = 0;
        this.actionItem = '';
      }
      this.mList = [...this.baseList];
      // 弹窗信息
      this.isEdit = status ? false : true;
      this.title = status ? '添加机构' : '修改机构';
      // 表单默认值
      if (status) {
        this.appAlreadyPushList = [];
        // const res = await this.$api.org.getCscpOrgs(row.id);
        // console.log(res, 'res');
        this.selectModelList = [];
        const form = this.$options.data().form;
        const obj = {
          parentId: row ? row.id : '0',
          parentType: row ? row.type : '',
          title: row ? row.title : '',
          type: row ? '' : 2,
          parentName: ''
        };
        this.form = {
          ...form,
          ...obj,
          orderBy: orderBy
        };
        // 新增机构 已授权应用初始化为 自动推送应用
        this.form.authorizeAppList = [...this.appAutoPushList]

        if (code) {
          //按钮权限码   有值：是开放给用户的新增或编辑
          this.code = code;
          this.$set(this.form, 'type', 3);
        } else {
          this.code = '';
        }
        this.open = true;
      } else {
        // 编辑机构
        this.isEdit = true;
        row = row.detailsData;
        this.appAlreadyPushList = this.appList.filter((item) => {
          return row.pushAppCode && row.pushAppCode.split(',').includes(item.appCode);
        })
        row.checkUp = row.checkUp ? 1 : 0;
        row.title = '';
        row.orderBy = orderBy;
        this.$set(this, 'form', JSON.parse(JSON.stringify(row)));
        this.form.authorizeAppList = [...this.appAlreadyPushList]
        this.form.orgLine = this.currentUploadRow.detailsData.orgLineList?.map(item => item.lineName) || []
        this.open = true;
      }

      // 获取父级机构 row.detailsData.parentId为0时，表示没有父级机构
      if (data && data?.parentId != 0 && data.parentId) {
        this.$api.org.getCscpOrgs(data.parentId).then(res => {
          if (res.data.resultCode == 20000) {
            // this.parentOrg = res.data.resultData;
            this.parentName = res.data.resultData.orgName;
            // this.$nextTick(() => {
            //   this.$set(this.form, 'parentName', res.data.resultData.orgName);
            // });
          }
        });
      } else {
        // this.parentOrg = [];
        this.parentName = '';
      }
      const codes = data.orgLabel ? data.orgLabel.split(',') : [];
      console.log(codes, 'codes');
      this.form.orgLabelList = this.orgLabelList.filter(item => codes.includes(item.id));
      console.log(this.form.orgLabelList, 'form.orgLabelList');
      console.log(this.currentUploadRow, 'this.currentUploadRow');
    },
    // 成员分页
    changeMember(page) {
      if (page) {
        this.currentPage = page;
      }
      let data = {
        ...this.modalFilterForm,
        pageSize: this.pageSize,
        currentPage: this.currentPage
      };
      this.$api.org.pageSelectUsers(this.memberId, data).then(res => {
        this.memberData = res.data.resultData.data;
        this.memberTotal = res.data.resultData.recordsTotal;
      });
    },
    // 查看成员
    handleView(row) {
      this.isUnit = row.detailsData.type === 2 ? true : false;
      this.memberId = row.id;
      this.$refs.modalFilter.reset();
      this.viewMemberDrawer = true;
    },
    // 添加成员
    handleAddMember() {
      this.$refs.viewMemberAdd.userSelectModal = true;
    },
    // 查看成员 - 新增选人
    getViewMemberAdd(params) {
      let users = JSON.parse(JSON.stringify(params)).map(n => {
        return {
          orgId: this.memberId,
          userId: n.userId,
          userName: n.userName
        };
      });
      this.$api.user.saveUserOrg(users).then(res => {
        if (res.data.resultCode == 20000) {
          this.$Message.success('新增成功');
          this.changeMember(1);
        }
      });
    },
    modalSearch(params) {
      this.modalFilterForm = JSON.parse(JSON.stringify(params));
      this.changeMember(1);
    },
    // 0 设置领导 / 1 取消领导 / 2 移除成员
    handleAction(row, status) {
      let data = {
        orgId: this.memberId,
        userId: row.id
      },
        message = !status
          ? `确定设置 ${row.realName} 为部门领导吗？`
          : status == 1
            ? `确定取消 ${row.realName} 部门领导吗？`
            : `确定从该部门中移除成员 ${row.realName} 吗？`;

      this.$Modal.confirm({
        title: '提示',
        content: message,
        loading: true,
        onOk: () => {
          let http = !status
            ? this.$api.org.saveDepartmentHead(data)
            : status == 1
              ? this.$api.org.deleteDepartmentHead(data)
              : this.$api.org.deleteUserOrg(data);
          http
            .then(res => {
              if (res.data.resultCode == 20000) {
                this.$Message.success('操作成功');
                this.changeMember(1);
              } else {
              }
            })
            .finally(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    getTreeData(data) {
      // 循环遍历json数据
      for (var i = 0; i < data.length; i++) {
        data[i] = {
          value: data[i].addrCode,
          label: data[i].addrName,
          children: data[i].sysOrgAddrList
        };
        // data[i].children = data[i].sysOrgAddrList
        if (data[i].children && data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          // data[i].sysOrgAddrList = undefined;
          data[i].children = [];
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    getExpandId(arr) {
      // expandArr
      for (let i = 0; i < arr.length; i++) {
        if (arr[i]._showChildren) {
          this.expandArr.push(arr[i].id);
          // if (arr[i].children && arr[i].children.length > 0) {
          //   this.getExpandId(arr[i].children);
          // }
        }
      }
    },
    async getExpandTree(arr) {
      this.$nextTick(() => {
        this.expandArr.forEach(node => {
          this.$refs.tableRef.setTreeExpand(node, true);
        });
      });

      // 如果是展开项，则需要请求数据
      // for (let i = 0; i < arr.length; i++) {
      //   let isExpand = this.expandArr.find(m => {
      //     return m.id == arr[i].id;
      //   });
      //   if (isExpand || arr[i].id == this.actionId) {
      //     arr[i]._showChildren = true;
      //     await this.$api.org[this.selectOrgByIdApiName]({ id: arr[i].id }).then(async response => {
      //       if (response && response.data.resultData.length > 0) {
      //         arr[i].children = response.data.resultData.map(j => {
      //           return j.isValuable
      //             ? {
      //                 id: j.id,
      //                 title: j.title,
      //                 orgCode: j.orgCode,
      //                 type: j.type,
      //                 detailsData: j,
      //                 children: [],
      //                 hasChild:true,
      //                 _loading: false
      //               }
      //             : {
      //                 id: j.id,
      //                 title: j.title,
      //                 orgCode: j.orgCode,
      //                 type: j.type,
      //                 detailsData: j
      //               };
      //         });
      //         await this.getExpandTree(arr[i].children);
      //       }
      //     });
      //   }
      // }
    },
    refushOrgItem(isDel = 0) {
      this.isRefush = true;
      const treeExpandRecords = this.$refs.tableRef.getTreeExpandRecords();
      const treeParent = this.$refs.tableRef.getTreeParentRow(this.currentUploadRow.id)
      if (!this.isEdit && isDel == 0 && !this.isAuthorized) {
        // 新增
        this.currentUploadRow.hasChild = true;
        this.$refs.tableRef.reloadTreeExpand(this.currentUploadRow);
        if (this.currentUploadRow.children && this.currentUploadRow.children.length > 0) {
          this.treeOrgExpand(this.currentUploadRow.children, treeExpandRecords);
        }

      } else {
        // 编辑/授权
        if (!treeParent) {
          this.getList(this.isRegion ? undefined : 0);
          return
        }
        if (this.currentUploadRow.hasChild) {

          // this.$refs.tableRef.reloadTreeExpand(this.currentUploadRow);
          // this.treeOrgExpand(this.currentUploadRow.children, treeExpandRecords);
          this.$refs.tableRef.reloadTreeExpand(treeParent);
          this.treeOrgExpand(treeParent.children, treeExpandRecords);
        } else {
          this.$refs.tableRef.reloadTreeExpand(treeParent);
          this.treeOrgExpand(treeParent.children, treeExpandRecords);
        }
      }

    },
    treeOrgExpand(list, treeExpandRecords) {
      list.forEach(item => {
        if (item.hasChild) {
          item._showChildren = true
          if (treeExpandRecords && treeExpandRecords.find(n => n.id == item.id)) {
            this.$refs.tableRef.setTreeExpand(item, false);
          }
          this.treeOrgExpand(item.children, treeExpandRecords)
        }
      })
    },
    saveSuccess() {
      // 获取原本已展开的数据
      this.expandArr = [];
      // this.getExpandId(this.tempDeptList);
      this.expandArr = this.$refs.tableRef.getTreeExpandRecords();

      if (this.filterForm.name) {
        this.testSearch({ name: this.filterForm.name });
        return;
      }

      // 获取需要刷新的数据
      let msg = this.$Message.loading({
        content: '正在更新数据',
        duration: 0
      });

      this.loading = true;
      this.$api.org[this.selectOrgByIdApiName]({ id: this.isRegion ? undefined : 0 })
        .then(async response => {
          let arr = response.data.resultData.map(n => {
            return n.isValuable
              ? {
                id: n.id,
                title: n.title,
                orgCode: n.orgCode,
                creditCode: n.creditCode,
                dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
                type: n.type,
                detailsData: n,
                hasChild: true,
                children: [],
                _loading: false,
                parentId: n.parentId,
                orderBy: item.orderBy
              }
              : {
                id: n.id,
                title: n.title,
                orgCode: n.orgCode,
                creditCode: n.creditCode,
                dataIsIntegrity: n.dataIsIntegrity == '1' ? '不完整' : '完整',
                type: n.type,
                detailsData: n,
                parentId: n.parentId,
                orderBy: item.orderBy
              };
          });
          this.$nextTick(async () => {
            this.tempDeptList = JSON.parse(JSON.stringify(arr));

            this.tempDeptFlatList = this.tempDeptList;
            if (this.filterForm.name !== '') {
              this.search(this.tempDeptList, this.filterForm.name, 'title');
            } else {
              this.keyWord = '';
            }
            msg();
          });
          this.$nextTick(async () => {
            for (const n of this.tempDeptList) {
              if (this.expandArr.map(item => item.id).includes(n.id)) {
                await this.handleLoadData(n);
                await this.$refs.tableRef.toggleTreeExpand(n);
                if (n.children && n.children.length > 0) {
                  await this.expandNodes(n.children);
                }
              }
            }
          });
        })
        .finally(() => {
          this.loading = false;
          this.tableKey += 1;
          msg();
        });
    },
    async expandNodes(nodes) {
      this.$nextTick(async () => {
        for (const node of nodes) {
          if (this.expandArr.map(n => n.id).includes(node.id)) {
            try {
              await this.handleLoadData(node);
              this.$refs.tableRef.toggleTreeExpand(node);
              if (node.children && node.children.length > 0) {
                await this.expandNodes(node.children);
              }
            } catch (error) {
              console.error(`加载节点 ${node.id} 的子节点数据失败:`, error);
            }
          }
        }
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      console.log(this.form, 'this.form.orgClassify');
      this.form.unitType = this.form.unitType
        ? this.form.unitType
        : this.form.unitType === 0
          ? 0
          : null;
      // this.form.departmentHeadIdList = JSON.parse(JSON.stringify(this.form.departmentHeadIdList))
      // 转换成字符串 , 分割
      this.form.orgLabel = this.form.orgLabelList
        ? this.form.orgLabelList.map(n => n.id).join(',')
        : '';
      delete this.form.orgLabelList;
      const data = JSON.parse(JSON.stringify(this.form));
      // 如果默认不分屏择置为undefined
      if (!data.splitview) {
        data.modelDataTypeList = undefined;
        data.modelNameList = undefined;
      }
      if (data.type == 3) {
        data.branchLeaderId =
          data.branchLeaderId && data.branchLeaderId.userId ? data.branchLeaderId.userId : '';
        data.departmentHeadIdList = data.departmentHeadIdList
          ? data.departmentHeadIdList.map(n => {
            return n.userId;
          })
          : [];
      } else {
        data.branchLeaderId = undefined;
        data.branchLeaderDTO = undefined;
        data.departmentHeadDTOList = undefined;
        data.departmentHeadIdList = undefined;
      }
      // 处理授权应用 pushAppCode 应用编码字符串 用,分割
      data.pushAppCode = data.authorizeAppList
        .map(app => app.appCode)
        .filter(appCode => !!appCode)
        .join(',');
      // 删除 data 里的 authorizeAppList
      delete data.authorizeAppList;
      // 处理系统条线
      if (data?.orgLine.length > 0) {
        data.orgLineList = data.orgLine.map(n => {
          return { lineName: n }
        });
      } else {
        data.orgLineList = [];
      }
      delete data.orgLine;
      this.$refs['form'].validate(async (valid) => {
        if (!valid) return;
        this.submitLoading = true;
        if (this.form.creditCode && this.form.type == 2) {
          const params = {
            creditCode: this.form.creditCode,
            id: this.form.id ? this.form.id : null
          }
          // 如果机构类型为单位类型时， 并且有统一社会信用代码时需要通过调用接口来判断是否存在重复的统一社会信用代码，如果存在提示用户有哪些机构，是否继续新增/更新
          const res = await this.$api.org.selectOrgByCreditCode(this.form.creditCode).catch(() => { })
          if (res.data && res.data.resultCode == 20000) {
            // 如果是更新编辑 把自己从结果中过滤掉
            const org = this.isEdit ? res.data.resultData.filter(item => item.id !== this.form.id) : res.data.resultData;
            if (org && org.length > 0) {
              this.$Modal.confirm({
                title: '提示',
                content: `该统一社会信用代码已存在于机构 ${org.map(item => item.orgName).join('、')}，是否继续更新？`,
                loading: true,
                onOk: () => {
                  this.submitFormRequest(data);
                  this.$Modal.remove();
                },
                onCancel: () => {
                  this.submitLoading = false;
                }
              });
            } else {
              this.submitFormRequest(data);
            }
          }
        } else {
          // 如果没有统一社会信用代码 或者 机构类型不是单位类型时，直接提交表单
          this.submitFormRequest(data);
        }
      });
    },
    submitFormRequest(data) {
      let msg = this.$Message.loading({
        content: this.isEdit ? '正在更新机构' : '正在创建机构',
        duration: 1
      });
      let http =
        this.form.id == null
          ? this.$api.org.cscpOrgsSave(data)
          : this.$api.org.updateCscpOrg(data);
      http
        .then(res => {
          if (res && res.data.resultCode == 20000) {
            this.$Message.success(this.isEdit ? '修改成功！' : '新增成功！');
            this.open = false;
            this.refushOrgItem()
          }
        })
        .finally(() => {
          msg();
          this.submitLoading = false;
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // 如果row.isValuable为true || row.children.length > 0 表示有下级机构 不能删除
      if (row.detailsData.isValuable || (row.children && row.children.length > 0)) {
        this.$Message.error('该机构存在下级单位，不能删除！');
        return;
      }
      this.currentUploadRow = row;
      this.$Modal.confirm({
        title: '提示',
        content: `确定删除机构 ${row.title} ？`,
        loading: true,
        onOk: () => {
          this.actionId = row.detailsData.parentId;
          this.actionItem = '';
          this.$api.org
            .deleteCscpOrgs(row.id)
            .then(data => {
              //删除失败：该机构存在下级单位！
              if (data.data.resultCode == '20000') {
                this.$Message.success('删除成功!');
                // this.saveSuccess();
                this.refushOrgItem(1)
                this.pushOrgAuto(row.id, 'delete');
              }
            })
            .finally(error => {
              this.$Modal.remove();
            });
        }
      });
    },
    navigateToExport(copyText) {
      // this.copyText(copyText)
      this.$router.push({ path: '/authority/exportFileManage' });
    },
    handleApplayDelete(row) {
      this.formDelete.auditExplain = ''
      this.deleteRow = row;
      this.deleteShow = true;
    },
    handleDeleteOk() {
      this.$refs.formDeleted.validate(valid => {
        if (valid) {
          this.actionId = this.deleteRow.detailsData.parentId;
          this.actionItem = '';
          this.$api.org
            .deleteCscpOrgsRemove({ orgId: this.deleteRow.id, ...this.formDelete })
            .then(data => {
              //删除失败：该机构存在下级单位！
              if (data.data.resultCode == '20000') {
                this.$Message.success('申请删除成功!');
                this.deleteShow = false;
                this.saveSuccess(); // 重新请求列表
                // this.pushOrgAuto(this.deleteRow.id, 'delete');
              }
            })
            .finally(error => {
              this.$Modal.remove();
            });
          // this.$Modal.confirm({
          //     title: '提示',
          //     content: `确定申请删除机构 ${this.deleteRow.title} ？`,
          //     loading: true,
          //     onOk: () => {

          //     }
          // });
        }
      })

    },
    pushOrgAuto(orgId, flag) {
      this.$api.org.syncOrgBusiness({ orgId, flag });
    },
    /** 查看单位详情操作 */
    handleViewDepart(row) {
      this.rowDepart = row.detailsData;
      this.$nextTick(() => {
        this.viewDepartCustomerDrawer = true;
      });
    },
    checkModel() {
      this.isShowModel = true;
    },
    onSelectModel(arr) {
      let modelNameList = [];
      let modelDataTypeList = [];
      this.selectModelList = arr.map(item => {
        modelNameList.push(item.modelName);
        modelDataTypeList.push(item.modelDataType);
        return {
          modelDataType: item.modelDataType,
          modelName: item.modelName
        };
      });
      this.form.modelNameList = modelNameList;
      this.form.modelDataTypeList = modelDataTypeList;
    },
    hanlderEmpty() {
      this.form.modelNameList = [];
      this.form.modelDataTypeList = [];
      this.selectModelList = [];
    },
    // 推送
    async handleSelectRow(selectdata) {
      this.selectedRowIds = [];
      for (const data of selectdata) {
        await this.processNode(data);
        // this.selectedRowIds.push(data.id);
      }
    },
    async processNode(node) {
      if (node.hasChild && node.children?.length === 0) {
        try {
          // 异步加载子节点数据
          const children = await this.handleLoadData(node);
          node.children = children;
        } catch (error) {
          console.error('加载子节点数据时出错:', error);
          return;
        }
      }
      // 将当前节点 ID 添加到选中列表中（如果还未添加）
      if (!this.selectedRowIds.includes(node.id)) {
        this.selectedRowIds.push(node.id);
      }
      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          await this.processNode(child);
        }
      }
    },
    // 获取树中某个节点的所有祖先节点和子孙节点
    findAncestorsAndDescendants(node, callback) {
      // 递归遍历所有祖先节点
      const traverseAncestors = (currentNode, callback) => {
        if (currentNode.detailsData.parentId && currentNode.detailsData.parentId !== '0') {
          traverseAncestors(
            this.tempDeptFlatList.find(item => item.id === currentNode.detailsData.parentId),
            callback
          );
        }
        node.id !== currentNode.id && callback(currentNode);
      };

      // 递归遍历所有子孙节点
      const traverseDescendants = (currentNode, callback) => {
        node.id !== currentNode.id && callback(currentNode);
        if (currentNode.children && currentNode.children.length) {
          currentNode.children.forEach(child => traverseDescendants(child, callback));
        }
      };

      // 从当前节点开始，遍历所有祖先节点
      // traverseAncestors(node, callback);
      // 从当前节点开始，遍历所有子孙节点
      traverseDescendants(node, callback);
    },

    handlePush({ id }, name = '') {
      this.$Modal.confirm({
        title: '确定推送',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[
            this.pushData.inSystemFlag == 0
              ? 'syncOrg'
              : name == 'mq'
                ? 'sendOrgMessage'
                : 'syncOrgInSystem'
          ]({
            appId: this.pushData.id,
            orgId: id
          })
            .then(res => {
              if (res?.data?.resultCode === 20000) {
                this.$Message.success('推送成功');
              }
            })
            .finally(() => {
              this.$Modal.remove();
            });
        }
      });
    },

    async handleBatchPush(name = '') {
      const data = this.$refs.tableRef.getCheckboxRecords(true);
      console.log(data, 'data');
      // for (let i = 0; i < data.length; i++) {
      //   if (data[i].orgCode.substring(0, 4) != '4300'&& data.length > 1) {
      //       this.$Message.info('市州区县数据只允许推送一个市州区县')
      //       return

      //    }
      // }
      if (data.length > 100) {
        this.$Message.info('最多只能选择100条数据');
        return;
      }
      await this.handleSelectRow(data);
      console.log(this.selectedRowIds, 'this.selectedRowIds');
      if (!this.selectedRowIds.length) {
        return this.$Message.warning('请先勾选');
      }
      this.$Modal.confirm({
        title: '确定批量推送',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[
            this.pushData.inSystemFlag == 0
              ? 'syncOrgBatch'
              : name == 'mq'
                ? `sendOrgBatchMessage`
                : 'syncOrgBatchInSystem'
          ]({
            appId: this.pushData.id,
            orgIds: this.selectedRowIds
          })
            .then(res => {
              if (res?.data?.resultCode === 20000) {
                this.selectedRowIds = [];
                this.$Message.success('推送成功');
              }
            })
            .finally(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    handleWestoneAllPush() {
      this.$Modal.confirm({
        title: '确定一键批量推送所有',
        loading: true,
        onOk: () => {
          this.$api.applicationManager[
            this.pushData.appCode == 'westone_uas_system'
              ? 'syncWestoneOrgBatch'
              : 'syncWestoneOrgBatch'
          ]({
            appId: this.pushData.id
          })
            .then(res => {
              if (res?.data?.resultCode === 20000) {
                this.selectedRowIds = [];
                this.$Message.success('推送成功，请稍后查看推送结果');
              }
            })
            .finally(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    handleBatchPushUser() {
      if (!this.selectedMember || !this.selectedMember.length) {
        return this.$Message.warning('请先勾选成员');
      }
      this.$Modal.confirm({
        title: '确定批量推送成员',
        loading: true,
        onOk: () => {
          this.$api.user.autoSyncUserBusiness(this.selectedMember.map(n => n.id))
            .then(res => {
              if (res?.data?.resultCode === 20000) {
                this.selectedMember = [];
                this.$Message.success('推送成功');
              }
            })
            .finally(() => {
              this.$Modal.remove();
            });
        }
      });
    },
    handleMemberSelectionChange(selection) {
      this.selectedMember = selection;
    },
    showSelectOrgModel() {
      this.showOrgModal = true;
    },
    clearOrg() {
      this.$Modal.confirm({
        title: '提示',
        content: `是否清空机构？`,
        loading: true,
        onOk: () => {
          this.$Modal.remove();
          this.form = {
            ...this.form,
            strId: undefined,
            strParentId: undefined,
            orgName: undefined,
            orgAbbreviation: undefined,
            description: undefined,
            orderBy: 1,
            strTrustNo: undefined,
            strParentTrustNo: undefined
          };
        },
        onCancel: () => { }
      });
    },
    getOrgList(params) {
      console.log(params, 'params');
      const data = params[0];
      const {
        strId,
        strParentId,
        strUnitName,
        strEasyName,
        strDescription,
        intSort,
        strTrustNo,
        strParentTrustNo,
        title
      } = data;
      this.form = {
        ...this.form,
        strId,
        strParentId,
        orgName: strUnitName,
        orgAbbreviation: strEasyName,
        description: strDescription,
        orderBy: intSort || 1,
        strTrustNo,
        strParentTrustNo
      };
      this.showOrgModal = false;
      this.form.orgAbbreviation = title;
    },
    // 推送应用
    handlePushApplication(data, moderatorFlag = false) {
      this.isPush = true;
      this.titleName = '推送'
      this.moderatorFlag = moderatorFlag; // 是否版主推送应用
      // this.showApplicationModalName = name;
      this.showApplicationModal = true;
      this.pushApplicationData = data;
    },
    // 授权应用
    async handleAppAuthorization(data) {
      await this.getApplicationList().catch(err => {
        this.$Message.error('获取应用列表失败，请稍后重试');
      });
      this.currentUploadRow = data
      this.titleName = '授权';
      this.isAuthorized = true;
      this.tableOrgInfo = data
      console.log(data.detailsData.pushAppCode, 'pushAppCode');
      this.appAlreadyPushList = this.appList.filter(item => {
        return data.detailsData.pushAppCode ? data.detailsData.pushAppCode.split(',').includes(item.appCode) : false;
      });
      this.form.authorizeAppList = [...this.appAlreadyPushList];
      console.log(this.appAlreadyPushList, 'appAlreadyPushList');
      this.showApplicationModal = true;
    },
    onPushApplicationCancel() {
      this.titleName = '授权';
      this.showApplicationModal = false;
      this.isPush = false;
      this.isAuthorized = false;
      this.pushApplicationData = {};
    },
    synchro(row) {
      this.unitSynchroShow = true;
      this.currentData = row;
    },
    openOrgModel(Boolean = false) {
      this.treeShow = true;
      this.isUpdatehHierarchy = Boolean; // 是否更新机构层级
      // 当前操作人是区划机构管理员 && localStorage里有orgInfo && orgInfo.id和当前操作人strId相同
      console.log(this.isRegion && this.getOrgInfo.id && this.getOrgInfo.id == this.userInfo.strId, 'this.isRegion && this.getOrgInfo.id && this.getOrgInfo.id == this.userInfo.strId');
      if (this.isRegion && this.getOrgInfo.id && this.getOrgInfo.id == this.userInfo.strId) {
      } else {
        this.initOrgTree()
      }
    },
    // 机构名称搜索
    unitSearch(params) {
      let filterForm = JSON.parse(JSON.stringify(params));
      if (filterForm.orgName !== '') {
        if (this.isAdmin) {
          let msg = this.$Message.loading({
            content: '正在更新数据',
            duration: 0
          });
          let api = this.isRegion
            ? this.$api.org.selectDivisionOrgByIdName
            : this.$api.org.getOrgHierarchyByNameNewest;
          let apiParams = null
          if (this.isRegion) {
            apiParams = {
              orgName: filterForm.orgName,
            }
          } else {
            apiParams = filterForm.orgName
          }
          api(apiParams)
            .then(response => {
              this.orgIdOptions = response.data.resultData.map((item) => {
                const transformChildren = (node) => {
                  if (node.childOrgs) {
                    node.children = node.childOrgs.map(transformChildren); // 递归处理子节点
                    delete node.childOrgs; // 删除原来的 childOrgs 属性
                  }
                  return node;
                };
                return transformChildren(item);
              });

              // 递归设置所有节点的展开状态
              const expandAllNodes = (nodes) => {
                nodes.forEach(node => {
                  this.$set(node, 'expand', true);
                  if (node.children && node.children.length > 0) {
                    expandAllNodes(node.children);
                  }
                });
              };

              expandAllNodes(this.orgIdOptions);
            })
            .finally(() => {
              msg();
            });
        }
      } else {
        this.keyWord = '';
        this.initOrgTree()
      }
    },
    unitReset() {
      this.initOrgTree()
    },
    initOrgTree(checkedList = []) {
      this.getOrgData(0, checkedList).then(res => {
        console.log('initOrgTree', res);
        this.orgIdOptions = res;
      });
    },
    getOrgData(id, checkedList = []) {
      return new Promise((resolve, reject) => {
        this.orgLoading = true;
        let result = null
        if (this.isRegion && !id) {
          result = {
            orgCodePath: this.orgCodePath
          }
        } else {
          result = {
            id,
            orgCodePath: this.orgCodePath
          }
        }
        console.log(result, 'result');
        let api = this.isRegion
          ? this.$api.org.selectRegionOrgById
          : this.$api.org.selectOrgById;
        api(result)
          .then(res => {
            this.orgLoading = false;
            resolve(
              res.data.resultData.map(n => {
                return n.isValuable
                  ? {
                    id: n.id,
                    title: n.title,
                    // disabled: n.type === 1 ? true : false,
                    render: (h, { root, node, data }) => {
                      return h(
                        'span',
                        {
                          class: n.type === 1 && 'disbaled_node'
                        },
                        data.title
                      );
                    },
                    checked: checkedList.includes(n.id),
                    children: [],
                    loading: false
                  }
                  : {
                    id: n.id,
                    title: n.title,
                    // disabled: n.type === 1 ? true : false,
                    render: (h, { root, node, data }) => {
                      return h(
                        'span',
                        {
                          class: n.type === 1 && 'disbaled_node'
                        },
                        data.title
                      );
                    },
                    checked: checkedList.includes(n.id)
                  };
              })
            );
          })
          .catch(err => {
            reject(err);
            this.orgLoading = false;
          });
      });
    },
    changeTree(arr, obj) {
      console.log(obj, 'obj');
      this.parentOrgData = {
        ...obj
      }
    },
    handleOrgOk() {
      // 如果是区划机构操作 缓存父级机构组织树 和 当前操作人id
      if (this.isRegion) {
        const orgInfo = {
          id: this.userInfo.strId,
          orgIdOptions: this.orgIdOptions
        }
        localStorage.setItem('orgInfo', JSON.stringify(orgInfo));
      }
      console.log(localStorage.getItem('orgInfo'), 'orgInfo');
      console.log(this.currentUploadRow, 'this.currentUploadRow');
      // 如果是更新机构层级
      if (this.isUpdatehHierarchy) {
        this.$api.org.updateParentOrg({
          id: this.currentUploadRow.id,
          parentId: this.parentOrgData.id
        }).then(res => {
          if (res.data.resultCode == 20000) {
            this.$Message.success('更新机构层级成功');
            this.treeShow = false;
            this.parentName = this.parentOrgData.title;
            this.form.parentId = this.parentOrgData.id;
            this.refushOrgItem();
          }
        });
      } else {
        this.form.parentId = this.parentOrgData.id;
        this.parentName = this.parentOrgData.title;
        this.treeShow = false;
      }
    },
    loadData(item, callback) {
      this.getOrgData(item.id).then(res => {
        callback(res);
      });
    },
    orgNameBlur() {
      this.form.orgAbbreviation = this.form.orgName
    },
    // 交换位置的排序版本
    onDragDrop(index1, index2) {
      console.log(index1, index2, 'index1, index2');
      index1 = parseInt(index1);
      index2 = parseInt(index2);
      // 互换 orderBy
      let oldData = {
        ...this.memberData[index1],
        orderBy: this.memberData[index2].orderBy
      };
      let newData = {
        ...this.memberData[index2],
        orderBy: this.memberData[index1].orderBy
      };
      this.memberData.splice(index1, 1, newData);
      this.memberData.splice(index2, 1, oldData);
      console.log([oldData, newData], 'oldData, this.memberData[index2]');
      // return
      this.$api.org.userOrderBy([oldData, newData]).then(res => {
        if (res.data.resultCode == 20000) {
          this.$Message.success('排序成功');
        }
      });
    },
    // 是否为区县和街道
    // 区县判断条件: 不是4300开头，并且org_code长度是12位
    // 街道判断条件: 不是4300开头，并且org_code长度是12位，且第7位到第9位不是000
    handleCopyUserName(row) {
      const input = document.createElement('input');
      input.setAttribute('value', row.loginName || '');
      document.body.appendChild(input);
      input.select();
      const result = document.execCommand('copy');
      document.body.removeChild(input);
      if (result) {
        this.$Message.success('复制成功');
      } else {
        this.$Message.error('复制失败');
      }
    },
    isDistrictOrStreet(orgCode) {
      if (orgCode && orgCode.length === 12 && !orgCode.startsWith('4300')) {
        return true
      }
      return false;
    },
    // 下拉清除事件
    onClear(key) {
      this.form[key] = null
    },

    // 授权历史记录相关操作
    handlePageHistory(data) {
      this.showHistoryModal = true;
      this.tableOrgInfo = data;
      this.historgCurrentPage = 1;
      this.historyPageSize = 10;
      this.handleHistoryPageChange(1);
    },
    handleHistorySearch(params) {
      this.historyFilterForm = JSON.parse(JSON.stringify(params));
      this.handleHistoryPageChange(1);
    },
    handleHistorgPageChange(index) {
      this.handleHistoryPageChange(index);
    },
    // 授权记录分页
    handleHistoryPageChange(index = 1) {
      this.tableLoading = true;
      this.historgCurrentPage = index;
      const { appName, appCode, status, createTime } = this.historyFilterForm;
      console.log(this.historyFilterForm, 'historyFilterForm');
      this.$api.applicationManager.queryListPageByRole({
        orgId: this.tableOrgInfo.id,
        createTimeStart: createTime && createTime.length > 0 && createTime[0] ? `${createTime[0]} 00:00:00` : '',
        createTimeEnd: createTime && createTime.length > 0 && createTime[1] ? `${createTime[1]} 23:59:59` : '',
        currentPage: this.historgCurrentPage,
        pageSize: this.historyPageSize
      }).then(res => {
        if (res && res.data.resultCode == 20000) {
          this.historyList = res.data.resultData.data;
          this.historyTotal = res.data.resultData.recordsTotal;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    // 是否有权限
    hasPermission(code) {
      if (code) {
        return this.$store.state.app.btnLimitedCodes.includes(code)
      } else {
        return true
      }
    },
    // 统一社会信用代码 是否唯一
    creditBlur() {
      console.log(this.form.creditCode, 'form.creditCode');
      if (this.form.creditCode && this.form.creditCode.length == 18) {
        // 如果是新增
        if (!this.isEdit) {
          this.$api.org.selectOrgByCreditCode(this.form.creditCode)
            .then(res => {
              if (res.data.resultCode == 20000) {
                console.log(res.data.resultData, 'res.data.resultData');
                this.creditCodeOrg = res.data.resultData;
              }
            });
        } else {
          // 如果是编辑 需要把自身筛选出去
          this.$api.org.selectOrgByCreditCode(this.form.creditCode)
            .then(res => {
              if (res.data.resultCode == 20000) {
                this.creditCodeOrg = res.data.resultData.filter(item => item.id !== this.form.id);
              }
            });
        }
      }
    }
  },
};
</script>
<style lang="less">
.organization {
  .ivu-select-dropdown ul {
    max-width: 152px;
  }

  .ivu-cascader .ivu-cascader-menu-item {
    height: 35px;
  }
}
</style>
<style lang="less" scoped>
.add-banner {
  margin-top: 10px;
  padding: 10px 0;
  margin-bottom: 20px;
  color: #95a5a6;
  text-align: center;
  width: 100%;
  border: 1px solid #bccbcc;
  border-radius: 5px;
  cursor: pointer;

  &:hover {
    color: #fff;
    background-color: #95a5a6;
  }
}

.organization {
  display: flex;
  flex-direction: column;

  &.can_push {
    height: calc(70vh - 32px);
  }

  .form-item-div {
    border: 1px solid #dcdee2;
    min-height: 32px;
    border-radius: 4px;
    padding: 0 7px;
  }

  .view-member {
    /deep/ .ivu-page {
      padding-bottom: 10px !important;
    }

    &-filter {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    // &-table {}

    .task-row-col {
      border: 1px solid #dcdee2;

      .task-detail-name {
        height: 45px;
        background-color: #f8f8f9;
        font-size: 14px;
        font-weight: bold;
        border-right: 1px solid #dcdee2;
        border-top: 1px solid #dcdee2;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .task-detail-value {
        height: 45px;
        border-right: 1px solid #dcdee2;
        border-top: 1px solid #dcdee2;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.ivue-items-input {
  width: 204px;
}

.ivue-items-btn {
  text-align: center;
}

.table-bth {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;

  &:first-child {
    margin-right: 10px;
  }
}

.select-org-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 9;
}

/deep/.vxe-header--row .vxe-cell--checkbox {
  display: none;
}


.i-blue {
  color: #409eff;
  font-style: normal;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;


  &:hover {
    text-decoration: underline;
  }

}

.i-red {
  font-style: normal;
  color: #f56c6c;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;

  &:hover {
    text-decoration: underline;
  }
}

/deep/ .vxe-tree-cell {
  display: flex !important;
}

/deep/ .vxe-cell {
  height: auto !important;
  min-height: 48px !important;
}

.credit-code-item {
  font-weight: bold;
  margin-right: 5px;
}
</style>
