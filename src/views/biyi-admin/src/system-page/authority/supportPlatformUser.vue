<!-- 统一支撑平台用户 -->
<template>
  <div class="workArrange-search components">
    <div class="workArrange-search-wrapper">
      <Row :gutter="20">
        <Col span="4" class="col-left">
        <Input v-model="filterText" suffix="ios-search" placeholder="输入单位名称"
          style="width: auto; margin: 10px 5px 0px 5px" />
        <el-tree class="scrollbar" ref="tree" expand-node @node-click="onNodeClick" node-key="id" :data="treeData"
          :filter-node-method="filterNode" :default-expand-all="true">
          <span @click.stop="onNodeClick(data)" class="custom-tree-node" slot-scope="{ data }">
            <span>{{ data.title }}</span>
          </span>
        </el-tree>

        <!-- <Tree :data="treeData" @on-select-change="onNodeClick"></Tree> -->
        </Col>
        <Col span="20">
        <div class="unit-top">
          <p>{{ currentUnitName }}</p>
        </div>
        <CommonFilterBar style="margin-top: 12px" ref="commonFilterBar" :filterArr="filterArr" @search="testSearch">
          <Button type="primary" class="mb10 mr10" style="float: right" @click="handleExport">导出用户</Button>
        </CommonFilterBar>
        <Table :columns="columns" :data="tableData" :loading="tableLoading"> </Table>
        <Page :total="total" show-elevator show-total class="table-page" v-show="total >= 10" :current="currentPage"
          @on-change="changeCurrentPage" :transfer="true" :page-size="10" />
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
import CommonFilterBar from '@/views/hnjcoa/components/commonFilterBar.vue';
import axios from 'axios';

export default {
  name: 'supportPlatformUser',

  components: {
    CommonFilterBar
  },
  data() {
    return {
      currentUnitName: '',
      search: '',
      filterText: '',
      filterArr: [
        {
          label: '用户姓名',
          name: 'strCname',
          value: '',
          labelWidth: 70,
          type: 1
        }
      ],
      unitContent(h, { root, node, data }) {
        return h(
          'span',
          {
            style: {
              display: 'inline-block',
              width: '100%',
              cursor: 'pointer'
            },
            on: {
              click: () => {
                //onchange(data);
              }
            }
          },
          [
            h('span', [
              h('Icon', {
                props: {
                  // type: 'md-home'
                },
                style: {
                  fontSize: '20px',
                  marginRight: '8px',
                  marginTop: '-3px'
                }
              }),
              h('span', data.title)
            ])
          ]
        );
      },
      // filterForm: {},
      tableLoading: false,
      tableData: [],
      columns: [
        {
          title: '用户姓名',
          key: 'strCname',
          align: 'left'
        },
        {
          title: '用户名',
          key: 'strUserId',
          align: 'left'
        },
        {
          key: 'strId',
          title: '用户唯一信任号',
          align: 'left',
        },
        {
          title: '密级',
          key: 'strMobile',
          align: 'left',
          render: (h, params) => {
            // strClassified 用户密级（10:普通　20:秘密　30:机密）
            const strClassified = params.row.strClassified;
            return h(
              'span',
              strClassified == '10'
                ? '普通'
                : strClassified == 20
                  ? '秘密'
                  : strClassified == 30
                    ? '机密'
                    : ''
            );
          }
        },
        {
          title: '性别',
          key: 'strSex',
          align: 'left'
        },
        {
          title: '邮箱',
          key: 'strEmail',
          align: 'left'
        },

        {
          title: '主用手机号码',
          key: 'strMobile',
          align: 'left'
        },
        {
          title: '备用手机号码',
          key: 'strMobileOther',
          align: 'left'
        },
        {
          title: '办公电话',
          key: 'strHPhone',
          align: 'left'
        },
        {
          title: '职位',
          key: 'strDuty',
          align: 'left'
        },

        {
          title: '岗位',
          key: 'strPost',
          align: 'left'
        }
      ],
      total: 0,
      currentPage: 1,
      pageSize: 10,

      workArrangeType: [],
      filterForm: {
        orgName: ''
      },
      treeData: [],
      userType: ''
    };
  },
  watch: {
    filterText: function (newVal) {
      this.$refs.tree.filter(newVal);
    }
  },
  computed: {
    /* 角色类型 */
    roleType() {
      return this.$byStoreGet('userRoleType');
    }
  },
  async created() {
    this.unit();
    this.changeCurrentPage(1);
  },
  mounted() {
    setTimeout(() => {
      console.log(this.userType, 'sdhjsdhsjdh');
    }, 500);
  },
  activated() {
    this.changeCurrentPage();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      console.log(data, 'data');
      if (!value) return true;
      return data.title.indexOf(value) !== -1;
    },
    onNodeClick(val) {
      console.log(val);
      this.onchange([val]);
    },
    toggleSearch(id) {
      this.status = id;
      if (this.search) {
        let params = {
          orgName: this.search,
          parentId: id
        };
        this.$api.distributionGroup.getTenantCompanyList(params).then(res => {
          this.tenantTree = (res.data.resultData.tenantTree || []).map(el => {
            return {
              ...el,
              expand: true
            };
          });
          if (res.data.resultData.orgTree) {
            this.treeData = res.data.resultData.orgTree;
          } else {
            this.treeData = [];
          }
        });
      } else {
        this.unit();
      }
    },
    testSearch(params) {
      this.filterForm = JSON.parse(JSON.stringify(params));

      this.changeCurrentPage(1);
    },
    // 翻页
    changeCurrentPage(page) {
      if (page) {
        this.currentPage = page;
      }
      const data = {
        strId: this.companys,
        ...this.filterForm
      }
      const params = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      };
      this.getTableData(data, params);
    },
    onchange(val) {
      this.companys = val[0].strId;
      this.currentUnitName = val[0].title;
      this.changeCurrentPage();
    },
    // 请求
    getTableData(data, params) {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'userType' }).then(res => {
        this.userType = res.data.resultData;
      });
      this.tableLoading = true;
      this.$api.user.querySxUserByTreeId(data, params).then(res => {
        this.tableLoading = false;
        res.data.resultData.data.map(s => {
          this.userType.map(m => {
            m.code == s.userType ? (s.userType = m.name) : m;
          });
        });
        this.tableData = res.data.resultData.data;
        this.total = res.data.resultData.recordsTotal;
      });
      console.log(this.userType, 'this.userType');
    },
    unit() {
      let msg = this.$Message.loading('正在获取单位列表');
      let paramsq = {
        strId: '0'
      };
      this.$api.user.querySxOrgTreeNew(paramsq).then(res => {
        this.treeData = res.data.resultData;
        this.changeData();
      }).finally(() => {
        msg();
      });
    },
    changeData() {
      transfer(this.treeData);

      function transfer(list) {
        list.forEach(res => {
          res.title = res.strUnitName;
          res.expand = true;
          res.children = JSON.parse(JSON.stringify(res.childList));
          res.childList = null;
          // this.$delete(res, 'childList');
          if (res.children && res.children.length > 0) {
            transfer(res.children);
          }
        });
      }
      if (this.treeData.length > 0) {
        this.onchange(this.treeData);
      } else {
        this.companys = '';
        this.currentUnitName = '';
      }
    },

    // 导出用户
    handleExport() {
      const params = {
        strId: this.companys,
        ...this.filterForm
      };
      let msg = this.$Message.loading({
        content: '正在导出用户',
        duration: 0
      });
      let baseUrl = this.$util.baseUrl;
      console.log(baseUrl, 'baseUrl');
      axios({
        url: baseUrl + '/api/sxSyncUserOrgData/querySxUserByTreeId/export',
        method: 'post',
        responseType: 'blob',
        data: params,
        headers: {
          Authorization: sessionStorage.token
        }
      }).then(res => {
        if (res.status === 200) {
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', '用户信息.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } else {
          this.$Message.error('导出失败');
        }
      }).finally(() => {
        msg();
      });
    },

    // 查看详情
    handleView(row) {
      this.$router.push({
        path: 'points-rule-detail',
        query: {
          ruleId: row.ruleId
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .ivu-table-cell>.hoverBlue {
  color: rgb(237, 64, 20) !important;
  cursor: pointer;
}

::v-deep .ivu-tree-title {
  overflow: hidden;
  width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.col-left {
  border-right: 1px solid #ccc;
  max-height: 600px !important;
  // overflow: hidden;
  // height: calc(100vh - 260px);
}

.scrollbar {
  overflow: scroll;
  height: 100%;
}

&::-webkit-scrollbar {
  width: 4px;
  background-color: #fff !important;
}

&:hover ::-webkit-scrollbar-track-piece {
  /*鼠标移动上去再显示滚动条*/
  background-color: #fff;
  /* 滚动条的背景颜色 */
  border-radius: 6px;
  /* 滚动条的圆角宽度 */
}

&:hover::-webkit-scrollbar-thumb:hover {
  background-color: #c1c1c1;
}

&:hover::-webkit-scrollbar-thumb:vertical {
  background-color: #c1c1c1;
  border-radius: 6px;
  outline: 2px solid #c1c1c1;
  outline-offset: -2px;
  border: 2px solid #c1c1c1;
}

.unit-top {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  padding-top: 10px;
  font-size: 18px;
}

.custom-tree-node {
  flex: 1 !important;
}
</style>
