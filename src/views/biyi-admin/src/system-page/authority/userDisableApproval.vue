<template>
  <div class="import-result components">
    <div class="import-result-wrapper">
      <CommonFilterBar :filterArr="filterArr" @search="testSearch">
      </CommonFilterBar>
      <Tabs :value="tabValue" @on-click="tabsClick">
        <TabPane label="审核中" name="0"></TabPane>
        <TabPane label="审核通过" name="1"></TabPane>
        <TabPane label="审核驳回" name="2"></TabPane>
      </Tabs>
      <Table ref="table" row-key="id" stripe border :columns="columns" :data="tableData" :loading="tableLoading"
        @on-selection-change="handleSelectionChange">
        <template slot-scope="{ row }" slot="action">
          <Button oaType="oaEdit" @click="handleAudit(row, true)">通过</Button>
          <Button oaType="oaDelete" @click="handleAudit(row, false)">驳回</Button>
        </template>
      </Table>
      <Page :total="total" show-elevator show-total class="margin-top-10" style="float: right" v-show="total >= 10"
        :current="currentPage" @on-change="changeCurrentPage" :transfer="true" :page-size="pageSize" />
    </div>

    <!-- 审核驳回弹窗 -->
    <Modal v-model="isModal" title="审核驳回" width="600">
      <Form ref="form" :model="modelForm" :rules="rules" label-width="120">
        <!-- 审核拒绝原因 -->
        <FormItem label="审核拒绝原因" prop="auditRefuseExplain">
          <Input v-model="modelForm.auditRefuseExplain" type="textarea" :rows="4" placeholder="请输入审核拒绝原因"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="isModal = false">取消</Button>
        <Button type="primary" :loading="tableLoading" @click="handleModalOk">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import CommonFilterBar from "@/views/hnjcoa/components/commonFilterBar.vue";
import { downloadTemplate } from "@/utils/demoWeb.js";
export default {
  name: "import-result",
  components: {
    CommonFilterBar,
  },
  data() {
    return {
      tabValue: '0', // 当前选中的标签页
      modelForm: {
        auditRefuseExplain: "", // 审核拒绝原因
      },
      rules: {
        auditRefuseExplain: [
          { required: true, message: "请输入审核拒绝原因", trigger: "blur" },
          { min: 5, max: 4000, message: "审核拒绝原因长度在5到4000个字符之间", trigger: "blur" },
        ],
      },
      filterArr: [
        {
          label: "申请人姓名",
          name: "applyRealName",
          labelWidth: 120,
          value: "",
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
        {
          label: "申请人手机",
          name: "applyMobile",
          labelWidth: 120,
          value: "",
          type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        },
        // 消费失败原因
        // {
        //   label: "消费失败原因",
        //   name: "failReason",
        //   labelWidth: 120,
        //   value: "",
        //   type: 1, // 1-输入框   2-下拉菜单   3-日期时间段
        // },
        // // 创建时间
        // {
        //   label: "创建时间",
        //   name: "startEndTime",
        //   labelWidth: 80,
        //   value: "",
        //   type: 3, // 1-输入框   2-下拉菜单   3-日期时间段
        // },
      ],
      filterForm: {
        auditStatus: 0, // 审核状态，0-审核中，1-审核通过，2-审核驳回
      },
      loading: false,
      tableLoading: false,
      tableData: [],
      columnsReviewing: [
        // {
        //   type: "selection",
        //   width: 60,
        //   align: "center",
        // },
        {
          title: "序号",
          width: 70,
          align: "center",
          render: (h, params) => {
            return h(
              "span",
              params.index + (this.currentPage - 1) * this.pageSize + 1
            );
          },
        },
        {
          title: "禁用原因",
          key: "applyDeleteExplain",
          align: "center",
          width: 200,
          render: (h, params) => {
            // 超出一定长度时显示省略号，并且鼠标悬停时显示完整内容
            const content = params.row.applyDeleteExplain || '无';
            return h(
              "span",
              {
                style: {
                  display: 'inline-block',
                  maxWidth: '180px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
                attrs: {
                  title: content, // 鼠标悬停时显示完整内容
                },
              },
              content
            );
          },
        },
        {
          title: "申请人姓名",
          key: "applyRealName",
          align: "center",
        },
        {
          title: "申请人手机",
          key: "applyMobile",
          align: "center",
        },
        {
          title: "申请时间",
          key: "applyDeleteTime",
          align: "center",
        },
        {
          title: '禁用用户名',
          key: 'deleteLoginName',
          align: 'center',
        },
        {
          title: "禁用用户姓名",
          key: "deleteRealName",
          align: "center",
        },
        {
          key: "deleteMobile",
          title: "禁用用户手机号",
          align: "center",
        },
        {
          key: 'deleteUserId',
          title: '禁用用户信任号',
          align: 'center',
        },
        {
          key: 'deleteIdCardNo',
          title: '禁用用户身份证号',
          align: 'center',
        },
        {
          key: 'deleteUserCompanyName',
          title: '禁用用户所在单位',
          align: 'center',
        },
        {
          key: 'deleteUserDepartmentName',
          title: '禁用用户所在部门',
          align: 'center',
        },
        // {
        //   title: "申请类型",
        //   key: "applyType",
        //   align: "center",
        //   render: (h, params) => {
        //     return h(
        //       "span",
        //       params.row.applyType == 0 ? "省直、市区划管理员审批" : "上级区划管理员审批"
        //     );
        //   },
        // },
        // {
        //   title: "审批状态",
        //   key: "auditStatus",
        //   align: "center",
        //   render: (h, params) => {
        //     return h(
        //       "span",
        //       params.row.auditStatus == 0 ? "审批中" : (params.row.auditStatus == 1 ? "审批通过" : "审批驳回")
        //     );
        //   },
        // },
        {
          title: "操作",
          fixed: 'right',
          slot: "action",
          align: "center",
        },
      ],
      columnsSuccessful: [
        {
          title: "审批时间",
          key: "auditTime",
          align: "center",
        },
      ],
      columnsFailed: [
        {
          title: "审批时间",
          key: "auditTime",
          align: "center",
        },
        {
          title: "审批拒绝原因",
          key: "auditRefuseExplain",
          align: "center",
          width: 200,
          render: (h, params) => {
            // 超出一定长度时显示省略号，并且鼠标悬停时显示完整内容
            const content = params.row.auditRefuseExplain || '无';
            return h(
              "span",
              {
                style: {
                  display: 'inline-block',
                  maxWidth: '180px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
                attrs: {
                  title: content, // 鼠标悬停时显示完整内容
                },
              },
              content
            );
          },
        }

      ],
      columns: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      isModal: false,
      isEdit: false,
      selectedRows: [], // 选中的行数据
    };
  },
  created() {
    this.columns = [...this.columnsReviewing]
  },
  mounted() {
    this.changeCurrentPage(1);
  },
  methods: {
    testSearch(params) {
      this.filterForm = JSON.parse(JSON.stringify(params));
      this.changeCurrentPage(1);
    },
    // 翻页
    changeCurrentPage(page) {
      if (page) {
        this.currentPage = page;
      }
      let { applyMobile, applyRealName, failReason, startEndTime } = this.filterForm || {};
      const params = {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        applyMobile: applyMobile || null,
        applyRealName: applyRealName || null,
        auditStatus: this.tabValue, // 当前选中的标签页
        // createTimeStart: startEndTime && startEndTime.length ? `${startEndTime[0]} 00:00:00` : null,
        // createTimeEnd: startEndTime && startEndTime.length ? `${startEndTime[1]} 23:59:59` : null,
      };
      this.getTableData(params);
    },
    // 请求列表
    getTableData(params) {
      this.tableLoading = true;
      this.$api.user.getDivisionOrgAuditList(params).then((res) => {
        this.tableLoading = false;
        this.total = res.data.resultData.recordsTotal;
        this.tableData = res.data.resultData.data;
        console.log(res, 'res');
      });
    },
    handleSelectionChange(selectedRows) {
      this.selectedRows = selectedRows;
    },
    // resetForm() {
    //   this.filterForm = {

    //   };
    //   this.changeCurrentPage(1);
    // },
    tabsClick(name) {
      console.log(name, 'name');
      this.tabValue = name; // 更新当前选中的标签页
      console.log(this.tabValue, 'this.tabValue');
      // 根据选中的标签页更新表格列
      if (name == '0') {
        this.columns = [...this.columnsReviewing];
      } else if (name == '1') {
        this.columns = [...this.columnsReviewing, ...this.columnsSuccessful];
        // 移除操作
        this.columns = this.columns.filter(col => col.slot !== 'action'); // 移除操作列
      } else if (name == '2') {
        this.columns = [...this.columnsReviewing, ...this.columnsFailed];
        this.columns = this.columns.filter(col => col.slot !== 'action'); // 移除操作列
      }
      this.changeCurrentPage(1); // 切换标签页时刷新列表
    },
    handleAudit(row, auditType) {
      // 审核通过或驳回
      if (auditType) {
        const params = {
          deleteUserIds: [row.deleteUserId],
          auditStatus: 1,
        }
        this.$Modal.confirm({
          title: "提示",
          content: `确定审核通过该条数据吗？`,
          loading: true,
          onOk: () => {
            this.$api.user.updateAuditStatus(params).then((res) => {
              if (res.data.resultCode === 20000) {
                this.$Message.success(`审核通过`);
                this.changeCurrentPage(this.currentPage);
              } else {
                this.$Message.error(res.data.resultMsg || '操作失败');
              }
            }).finally(() => {
              this.$Modal.remove();
            });
          },
        });
      } else {
        this.isModal = true;
        this.modelForm.auditRefuseExplain = ""; // 清空审核拒绝原因
        this.$nextTick(() => {
          this.$refs.form.resetFields(); // 重置表单
        });
        this.isEdit = true; // 设置为编辑状态
        this.modelForm.deleteUserId = row.deleteUserId; // 设置禁用用户ID
      }
    },
    handleModalOk() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.tableLoading = true; // 显示加载状态
          const params = {
            deleteUserIds: [this.modelForm.deleteUserId],
            auditStatus: 2, // 审核驳回
            auditRefuseExplain: this.modelForm.auditRefuseExplain,
          };
          this.$api.user.updateAuditStatus(params).then((res) => {
            if (res.data.resultCode === 20000) {
              this.$Message.success(`审核驳回成功`);
              this.isModal = false; // 关闭弹窗
              this.changeCurrentPage(this.currentPage); // 刷新列表
            } else {
              this.$Message.error(res.data.resultMsg || '操作失败');
            }
          }).finally(() => {
            this.tableLoading = false; // 隐藏加载状态
            this.$Modal.remove();
          });
        } else {
          this.$Message.error('请填写完整的审核拒绝原因');
        }
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
