<template>
    <div class="async-routes-config">
        <!-- 统一菜单 -->
        <Alert type="warning" show-icon closable>
            本页操作提示：
            <template slot="desc">
                <p>1. 点击【新增】则为给相应菜单添加子节点</p>
                <p>2. 按钮不能添加子节点</p>
                <p>3. 有子节点的父节点不能删除</p>
                <p>4. 新增/编辑/拖拽节点后，刷新页面即可</p>
                <p>5. 非一级节点不允许拖拽成为一级节点。</p>
                <p>6. 一级节点（【所有菜单】下一级节点）只允许平级拖拽移动。</p>
            </template>
        </Alert>
        <!-- <Row class="query-div">
      <Col span="8">
      <Input v-model="query" search placeholder="请输入菜单名称" @on-search="handleSearch" />
      </Col>
    </Row> -->

        <div class="tabsSystemMenu">
            <!-- <el-radio-group v-model="menuType" @change="handleMenuTypeChange" size="small">
                <el-radio-button label="上海"></el-radio-button>
                <el-radio-button label="北京" disabled></el-radio-button>
                <el-radio-button label="广州"></el-radio-button>
                <el-radio-button label="深圳"></el-radio-button>
            </el-radio-group> -->
            <el-tabs v-model="menuType" type="card" @tab-click="handleMenuTypeChange">
                <el-tab-pane :label="item.label" :name="item.value" v-for="item in platformList" :key="item.id">
                    <Tree :data="treeData" :render="renderContent" :load-data="loadNode" style="line-height: 32px">
                    </Tree>
                </el-tab-pane>
                <!-- <el-tab-pane label="三服务" name="second">

                    <Tree :data="treeData" :render="renderContent" :load-data="loadNode" style="line-height: 32px">
                    </Tree>
                </el-tab-pane>
                <el-tab-pane label="会议排座" name="third">

                    <Tree :data="treeData" :render="renderContent" :load-data="loadNode" style="line-height: 32px">
                    </Tree>
                </el-tab-pane>
                <el-tab-pane label="接待系统" name="fourth">

                    <Tree :data="treeData" :render="renderContent" :load-data="loadNode" style="line-height: 32px">
                    </Tree>
                </el-tab-pane> -->
            </el-tabs>
        </div>
        <Modal v-model="isNodeEdited" class-name="add-route-modal" footer-hide title="节点信息编辑">
            <Form ref="asyncRoute" :model="asyncRoute" :rules="ruleAsyncRoute" :label-width="180">
                <FormItem prop="type" label="类型（type）">
                    <Select v-model="asyncRoute.type">
                        <Option value="menu" label="一级菜单"></Option>
                        <Option value="menu2" label="二级菜单（不含三级菜单），三级菜单"></Option>
                        <Option value="menu2-sp" label="二级菜单（含三级菜单）"></Option>
                        <Option value="non-menu" label="非菜单页面"></Option>
                        <Option value="button" label="按钮"></Option>
                    </Select>
                </FormItem>
                <FormItem prop="title" label="标题（title）">
                    <Input type="text" v-model="asyncRoute.title" placeholder="路由标题">
                    <Icon type="md-create" slot="prepend"></Icon>
                    </Input>
                </FormItem>
                <template v-if="asyncRoute.type !== 'button'">
                    <FormItem prop="icon" label="菜单图标名（icon）">
                        <Input type="text" v-model="asyncRoute.icon" placeholder="菜单图标名，参考iView的图标库">
                        <Icon type="md-create" slot="prepend"></Icon>
                        </Input>
                    </FormItem>
                    <FormItem prop="name" label="路由名（name）">
                        <Input type="text" v-model="asyncRoute.name" placeholder="路由名">
                        <Icon type="md-create" slot="prepend"></Icon>
                        </Input>
                    </FormItem>
                    <template v-if="asyncRoute.type !== 'menu2-sp'">
                        <FormItem prop="url" label="路由访问路径（path）">
                            <Input type="text" v-model="asyncRoute.url"
                                placeholder="一级菜单路径必须以 ' / '开头，二级及其他路径可不以 ' / '开头，区别详见前端readme文档 ">
                            <Icon type="md-create" slot="prepend"></Icon>
                            </Input>
                        </FormItem>
                        <FormItem prop="component" label="vue组件（component）">
                            <Input type="text" v-model="asyncRoute.component"
                                placeholder="组件位于src下路径，例如：views/main/main.vue">
                            <Icon type="md-create" slot="prepend"></Icon>
                            </Input>
                        </FormItem>
                    </template>
                </template>
                <FormItem prop="permissionCode" label="权限码">
                    <Input type="text" v-model="asyncRoute.permissionCode" placeholder="权限码，例如：cscp.user.query">
                    <Icon type="md-create" slot="prepend"></Icon>
                    </Input>
                </FormItem>
                <div v-if="asyncRoute.type == 'non-menu' || asyncRoute.type == 'menu2' || asyncRoute.type == 'menu'">
                    <FormItem prop="homeDisplay" label="首页显示">
                        <RadioGroup v-model="asyncRoute.homeDisplay" @on-change="homeDisplayChange">
                            <Radio label="1">是</Radio>
                            <Radio label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="homeArea" label="显示区域" v-if="asyncRoute.homeDisplay == '1'">
                        <RadioGroup v-model="asyncRoute.homeArea" type="button">
                            <Radio v-for="(item, index) in displayArea" :key="index" :label="item.code">{{ item.name }}
                            </Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="homeCountUrl" label="角标获取路径" v-if="asyncRoute.homeDisplay == '1'">
                        <Input type="text" v-model="asyncRoute.homeCountUrl"
                            placeholder="角标获取路径，例如：/api/tSysDictRecord/getDictRecordByDictCode">
                        <Icon type="md-create" slot="prepend"></Icon>
                        </Input>
                    </FormItem>
                    <!-- <FormItem prop="homeIcon" label="图标" v-if="asyncRoute.homeDisplay == '1'">
              <Input type="text" v-model="asyncRoute.homeIcon" placeholder="图标，参考iview图标库">
              <Icon type="md-create" slot="prepend"></Icon>
              </Input>
            </FormItem> -->
                </div>
                <!--        外系统跳转用 extranetLinkUrl-->
                <FormItem label="外系统链接">
                    例：http://134.178.223.50:31583/other-login?
                    <Input type="text" v-model="asyncRoute.extranetLinkUrl">
                    </Input>
                </FormItem>
                <!--        报表专用-->
                <div v-if="asyncRoute.type == 'non-menu' || asyncRoute.type == 'menu2'">
                    <FormItem prop="homeIframeDisplay" label="是否加载外链地址">
                        <RadioGroup v-model="asyncRoute.homeIframeDisplay" @on-change="handleIframeDisplayChange">
                            <Radio label="1">是</Radio>
                            <Radio label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="iframeUrl" label="外链路径" v-if="asyncRoute.homeIframeDisplay == '1'">
                        <Input type="text" v-model="asyncRoute.iframeUrl"
                            placeholder="iframe路径，例如：http://192.168.120.82:8080/webroot/decision/view/report">
                        <Icon type="md-create" slot="prepend"></Icon>
                        </Input>
                    </FormItem>
                </div>
                <FormItem prop="permissionCode" label="使用范围" v-if="asyncRoute.type == 'button'">
                    <Select v-model="asyncRoute.platformAdminUse" transfer>
                        <Option :value="1">无限制</Option>
                        <Option :value="2">系统平台管理员专用</Option>
                        <Option :value="3">租户专用</Option>
                        <Option :value="4">平台与租户使用</Option>
                    </Select>
                </FormItem>
                <FormItem prop="orderBy" label="排序">
                    <InputNumber style="background: #fff" v-model="asyncRoute.orderBy"></InputNumber>
                </FormItem>
            </Form>
            <Row type="flex" justify="center">
                <Button type="primary" class="mr10" @click="handleSubmit('asyncRoute')" :loading="loading">保存</Button>
                <Button type="default" @click="cancel">取消</Button>
            </Row>
        </Modal>
        <!-- 数据权限设置 -->
        <Drawer :title="dataPerTitle" :closable="false" v-model="dataPerDra" width="1000">
            <Row :gutter="16" type="flex" justify="start" class="def_row">
                <Col>默认规则：</Col>
                <Col>
                <!-- <CheckboxGroup v-model="def_button" class="def_radio"> -->
                <Checkbox @on-change="changeDef($event, item)" v-model="item._checked"
                    v-for="(item, index) in def_rule_list" :key="index" :label="item.ruleColumn">{{ item.ruleName }}
                </Checkbox>
                <!-- </CheckboxGroup> -->
                </Col>
            </Row>
            <Button type="primary" class="ivue-items-btn" icon="md-add" @click="showAddrule">新增</Button>
            <Row>
                <Col span="24">
                <Table stripe :columns="dataPerColumns" :data="dataPerList"></Table>
                </Col>
            </Row>
        </Drawer>
        <Modal v-model="addRuleShow" :title="addRuleTitle" width="60%" @on-ok="addRuleOk">
            <Form ref="addRuleForm" :model="addRuleForm" :rules="ruleaddForm" :label-width="180">
                <FormItem prop="ruleName" label="规则名称">
                    <Input type="text" v-model="addRuleForm.ruleName" placeholder="请输入规则名称"></Input>
                </FormItem>
                <FormItem label="规则字段">
                    <Input type="text" v-model="addRuleForm.ruleColumn" placeholder="请输入规则字段"></Input>
                </FormItem>
                <FormItem prop="ruleConditions" label="条件规则">
                    <Select ref="roleSelect" v-model="addRuleForm.ruleConditions">
                        <Option v-for="option in conditions" :value="Object.keys(option)[0]"
                            :key="Object.keys(option)[0]" :label="conditionsMap[Object.keys(option)[0]]"></Option>
                    </Select>
                </FormItem>
                <FormItem prop="ruleValue" label="规则值">
                    <Input type="text" v-model="addRuleForm.ruleValue" placeholder="请输入规则值"></Input>
                </FormItem>
                <FormItem prop="status" label="状态">
                    <RadioGroup v-model="addRuleForm.status" type="button" button-style="solid">
                        <Radio label="1">有效</Radio>
                        <Radio label="0">无效</Radio>
                    </RadioGroup>
                </FormItem>
            </Form>
        </Modal>
    </div>
</template>

<script>
import dynamicParam from '@/api/dynamicParam';
import { platform } from 'os';
export default {
    data() {
        return {
            loading: false,
            def_button: [],
            def_rule_list: [], //默认规则列表
            def_rule: {
                ruleColumn: "age",
                ruleConditions: "4",
                ruleName: "默认规则",
                ruleValue: "1",
                status: 1,
            },
            menuType: 'first',
            menuId: "", //选中编辑数据规则的菜单id
            addRuleTitle: "", //数据规则编辑弹窗标题
            conditions: [], //条件规则列表
            conditionsMap: [],
            ruleaddForm: {
                ruleName: [
                    { required: true, message: "规则名称不能为空！", trugger: "blur" },
                ],
                ruleConditions: [
                    { required: true, message: "条件规则不能为空！", trigger: "blur" },
                ],
                ruleValue: [
                    { required: true, message: "规则值不能为空！", trigger: "blur" },
                ],
            },
            addRuleForm: {
                status: "1",
            },
            addRuleShow: false,
            dataPerList: [],
            dataPerColumns: [
                {
                    key: "ruleName",
                    title: "规则名称",
                    align: "center",
                },
                {
                    key: "ruleColumn",
                    title: "规则字段",
                    align: "center",
                },
                {
                    key: "ruleConditions",
                    title: "规则条件",
                    align: "center",
                    render: (h, params) => {
                        return h("span", [
                            h("strong", this.conditionsMap[params.row.ruleConditions]),
                        ]);
                    },
                },
                {
                    key: "ruleValue",
                    title: "规则值",
                    align: "center",
                },
                {
                    title: "操作",
                    align: "center",
                    key: "handle",
                    render: (h, params) => {
                        return h("div", [
                            params.row.isSys == 2
                                ? h(
                                    "Button",
                                    {
                                        style: {
                                            margin: "0 5px",
                                            color: "#2085F7",
                                        },
                                        props: {
                                            type: "text",
                                        },
                                        on: {
                                            click: () => {
                                                this.showAddrule(params.row);
                                            },
                                        },
                                    },
                                    "编辑"
                                )
                                : "",
                            params.row.isSys == 2
                                ? h(
                                    "Poptip",
                                    {
                                        props: {
                                            confirm: true,
                                            title: "确定删除这条规则吗？",
                                            transfer: true,
                                        },
                                        on: {
                                            "on-ok": () => {
                                                this.deleteRule(params.row.id);
                                            },
                                        },
                                    },
                                    [
                                        h(
                                            "Button",
                                            {
                                                style: {
                                                    color: "#ff3300",
                                                },
                                                props: {
                                                    code: "",
                                                    type: "text",
                                                    placement: "top",
                                                },
                                            },
                                            "删除"
                                        ),
                                    ]
                                )
                                : "",
                        ]);
                    },
                },
            ],
            dataPerDra: false,
            dataPerTitle: "",
            props: {
                label: "title",
                children: "children",
            },
            treeData: [],
            currentNode: null,
            isNodeEdited: false,
            asyncRoute: null,
            ruleAsyncRoute: {
                title: [
                    {
                        required: true,
                        message: "请填写路由标题",
                        trigger: "blur",
                    },
                ],
                name: [
                    {
                        required: true,
                        message: "请填写路由名",
                        trigger: "blur",
                    },
                    {
                        validator: (rule, value, callback) => {
                            let reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
                            if (!reg.test(value)) {
                                callback();
                            } else {
                                callback(new Error("请输入合法的路由名，不能包含中文。"));
                            }
                        },
                        trigger: "blur",
                    },
                ],
                url: [
                    {
                        required: true,
                        message: "请填写路由访问路径",
                        trigger: "blur",
                    },
                    {
                        validator: (rule, value, callback) => {
                            let reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
                            if (!reg.test(value)) {
                                callback();
                            } else {
                                callback(new Error("请输入合法的路由路径，不能包含中文。"));
                            }
                        },
                        trigger: "blur",
                    },
                ],
                component: [
                    {
                        required: true,
                        message: "请填写路由对应的Vue组件",
                        trigger: "blur",
                    },
                    {
                        validator: (rule, value, callback) => {
                            let reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
                            if (!reg.test(value)) {
                                callback();
                            } else {
                                callback(new Error("请输入合法的组件路径，不能包含中文。"));
                            }
                        },
                        trigger: "blur",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择该节点的数据类型",
                        trigger: "blur",
                    },
                ],
                orderBy: [
                    {
                        required: true,
                        message: "排序号不能为空",
                        trigger: "blur",
                        type: "number",
                    },
                ],
                iframeUrl: [
                    {
                        required: true,
                        message: "地址不不能为空",
                        trigger: "blur",
                    },
                ],
            },
            dragstartNode: "",
            dragstartData: "",
            displayArea: [],
            query: "",
            keyWord: "",
            platformList: []
        };
    },
    methods: {
        async getDictionary() {
            await dynamicParam
                .queryTSysConfigPage({
                    currentPage: 1,
                    pageSize: 99999,
                    code: 'sync:rbac:'
                })
                .then(res => {
                    const arr = res.data.resultData?.data || [];
                    this.platformList = arr.map(item => {
                        return { label: item.remark, value: item.code };
                    });
                    this.menuType = arr[0]?.code;
                });
        },
        handleSearch() {
            this.getNodes("0");
        },
        // 获取数据字典中的显示区域
        getDisplayArea() {
            this.$api.dictionary
                .getDictRecordByDictCode({ code: "displayArea" })
                .then((res) => {
                    if (res.data.resultCode == 20000) {
                        this.displayArea = res.data.resultData;
                    }
                });
        },
        permissionCodeChange(value) {
            this.asyncRoute.platformAdminUse = value;
        },
        homeDisplayChange(value) {
            if (value == 1) {
                this.$set(this.asyncRoute, "homeArea", "1");
            }
        },
        handleIframeDisplayChange(val) {
            if (val === "0") {
                this.asyncRoute.iframeUrl = "";
            }
        },
        changeDef(e, item) {
            if (!e) {
                this.deleteRule(item.id);
            } else {
                item.menuId = this.menuId;
                item.isSys = 1;
                item.serviceKey = this.menuType;
                this.$api.dataUnitaryRule.create(item).then((res) => {
                    if (res.data.resultData) {
                        item.id = res.data.resultData.id;
                        this.$Message.success("新增规则成功");
                        this.queryCscpDataRulePage(this.menuId);
                    }
                });
            }
        },
        /**获取默认规则列表
         * api/cscpDataRule/getDataFilter
         */
        getDataFilter() {
            this.$api.dataUnitaryRule.getDataFilter({ menuId: this.menuId, serviceKey: this.menuType }).then((response) => {
                this.def_rule_list = response.data.resultData;
                response.data.resultData.forEach((e) => {
                    if (e._checked) {
                        this.def_button.push(e.ruleColumn);
                    }
                });
            });
        },
        deleteRule(id) {
            this.$api.dataUnitaryRule.delete({ id: id, serviceKey: this.menuType }).then(() => {
                this.$Message.success("删除成功！");
                this.queryCscpDataRulePage(this.menuId);
            });
        },
        /**查询该菜单已有数据规则列表 */
        queryCscpDataRulePage(id) {
            this.$api.dataUnitaryRule
                .queryCscpDataRulePage(
                    { menuId: id, serviceKey: this.menuType },
                    { currentPage: 1, pageSize: 100000 }
                )
                .then((response) => {
                    this.dataPerList = response.data.resultData.data;
                });
        },
        /**查询规则条件列表 */
        queryrule() {
            let that = this;
            this.$api.dataUnitaryRule.queryrule({ serviceKey: this.menuType }).then((response) => {
                this.conditions = response.data.resultData;
                this.conditions.map((e) => {
                    for (let [k, v] of Object.entries(e)) {
                        that.conditionsMap[k] = v;
                    }
                });
            });
        },
        /**打开新增数据规则弹窗 */
        showAddrule(obj) {
            this.addRuleShow = true;
            if (obj) {
                this.addRuleForm = JSON.parse(JSON.stringify(obj));
                this.addRuleForm.status = String(this.addRuleForm.status);
                this.addRuleTitle = "编辑";
            } else {
                this.addRuleForm = {
                    menuId: this.menuId,
                    status: "1",
                };
                this.addRuleTitle = "新增";
            }
        },
        /**新增规则 */
        // /api/cscpDataRule/update/{id}
        addRuleOk() {
            const msg = this.$Message.loading({
                content: this.addRuleForm.id ? "正在修改规则" : "正在新增规则",
                duration: 0,
            });
            this.addRuleForm.isSys = 2;
            let params = this.addRuleForm;
            let http = params.id
                ? this.$api.dataUnitaryRule.update({ ...params, serviceKey: this.menuType })
                : this.$api.dataUnitaryRule.create({ ...params, serviceKey: this.menuType });
            http
                .then((response) => {
                    if (response) {
                        this.$Message.success(params.id ? "修改规则成功" : "新增规则成功");
                        this.queryCscpDataRulePage(this.menuId);
                    }
                })
                .finally(() => {
                    msg();
                });
        },
        /**初始化新增路由弹窗 */
        initAsyncRoute() {
            this.asyncRoute = {
                type: "",
                name: "",
                icon: "ios-folder-outline",
                title: "",
                url: "",
                component: "",
                permissionCode: "",
                parentId: null,
                id: null,
                orderBy: 1,
                platformAdminUse: 1,
                homeDisplay: "0",
                homeArea: "1",
                homeCountUrl: "",
                homeIcon: "",
                homeIframeDisplay: "0",
                iframeUrl: "",
            };
        },
        /**加载路由子节点 */
        loadNode(item, callback) {
            this.getNodes(item.id, true).then((value) => {
                if (value.length == 0) {
                    delete item.children;
                    delete item.loading;
                    this.$set(
                        this,
                        "treeData",
                        JSON.parse(JSON.stringify(this.treeData))
                    );
                } else {
                    return callback(value);
                }
            });
        },
        /**自定义路由树显示规则 */
        renderContent(h, { root, node, data }) {
            return h(
                "span",
                {
                    style: {
                        display: "inline-block",
                        width: "100%",
                    },
                    attrs: {
                        draggable: "true",
                    },
                    on: {
                        dragstart: () => this.handleDragStart(root, node, data.detailsData),
                        dragover: () => this.handleDragOver(root, node, data.detailsData),
                        dragend: () => this.handleDragEnd(root, node, data.detailsData),
                        drop: () => this.handleDrop(root, node, data.detailsData),
                    },
                },
                [
                    h(
                        "span",
                        {
                            style: {
                                marginRight: "16px",
                            },
                        },
                        [h("span", data.title)]
                    ),
                    h(
                        "span",
                        {
                            style: {
                                display: "inline-block",
                                float: "right",
                            },
                        },
                        [
                            data.detailsData.type != "button"
                                ? h(
                                    "Button",
                                    {
                                        props: Object.assign({}, this.buttonProps, {
                                            oaType: "oaEdit",
                                        }),
                                        style: {
                                            textAlign: "center",
                                            display: data.type == "button" ? "none" : "",
                                        },
                                        on: {
                                            click: () => {
                                                this.editNode(node, data, "append");
                                            },
                                        },
                                    },
                                    data.detailsData.type != "root" ? "新增" : "新增一级菜单"
                                )
                                : "",
                            data.detailsData.type != "root"
                                ? h(
                                    "Button",
                                    {
                                        props: Object.assign({}, this.buttonProps, {
                                            oaType: "oaEdit",
                                        }),
                                        style: {
                                            textAlign: "center",
                                        },
                                        on: {
                                            click: () => {
                                                this.editNode(node, data, "update");
                                            },
                                        },
                                    },
                                    "编辑"
                                )
                                : "",
                            data.detailsData.type != "root"
                                ? h(
                                    "Button",
                                    {
                                        props: Object.assign({}, this.buttonProps, {
                                            oaType: "oaDelete",
                                        }),
                                        style: {
                                            textAlign: "center",
                                        },
                                        on: {
                                            click: () => {
                                                this.removeNodeConfirmed(node, data);
                                            },
                                        },
                                    },
                                    "删除"
                                )
                                : "",
                            data.detailsData.type != "root" &&
                                data.detailsData.type != "button"
                                ? h(
                                    "Button",
                                    {
                                        props: Object.assign({}, this.buttonProps, {
                                            oaType: "oaEdit",
                                        }),
                                        style: {
                                            textAlign: "center",
                                        },
                                        on: {
                                            click: () => {
                                                this.openDataPerDra(data);
                                            },
                                        },
                                    },
                                    "数据权限"
                                )
                                : "",
                        ]
                    ),
                ]
            );
        },
        /**打开数据规则侧边栏 */
        openDataPerDra(data) {
            this.dataPerTitle = data.title + "：数据权限规则";
            this.dataPerDra = true;
            this.addRuleForm.menuId = data.id;
            this.menuId = data.id;
            this.queryCscpDataRulePage(data.id);
            this.getDataFilter();
        },
        /**获取路有树 */
        getNodes(parentId, load) {
            let arr = [];
            if (parentId == "0") {
                arr[0] = {
                    id: 0,
                    title: "菜单管理",
                    parentId: null,
                    loading: false,
                    expand: true,
                    detailsData: {
                        type: "root",
                    },
                };
            }
            return new Promise((resolve, reject) => {
                const msg = this.$Message.loading({
                    content: "正在获取数据",
                    duration: 0,
                });
                this.$api.menuUnitary
                    .getMenu({ parentId, level: 1, serviceKey: this.menuType })
                    .then((response) => {
                        if (parentId == "0") {
                            arr[0].children = response.data.resultData.map((e) => {
                                return {
                                    ...e,
                                    loading: false,
                                    children: [],
                                };
                            });
                        } else {
                            arr = response.data.resultData.map((e) => {
                                return {
                                    ...e,
                                    loading: false,
                                    children: [],
                                };
                            });
                        }
                        if (!load) {
                            [...this.treeData] = arr;
                        } else {
                            resolve(arr);
                        }
                    })
                    .finally(() => {
                        msg();
                    });
            });
        },
        /**编辑/新增 菜单按钮*/
        editNode(node, data, act) {
            console.log("node: ", node);
            this.currentNode = node;
            if (act === "append") {
                this.initAsyncRoute();
                this.asyncRoute.parentId = data.parentId === null ? 0 : data.id;
            } else if (act === "update") {
                data.detailsData.homeDisplay = data.detailsData.homeDisplay.toString();
                this.asyncRoute = JSON.parse(JSON.stringify(data.detailsData));
                this.asyncRoute.homeIframeDisplay = (
                    data.detailsData.homeIframeDisplay || 0
                ).toString();
                if (data.detailsData.type == "button") {
                    this.asyncRoute.type = "button";
                }
            }
            this.isNodeEdited = true;
        },
        /**删除 菜单按钮*/
        removeNodeConfirmed(node, data) {
            this.$Modal.confirm({
                title: "警告",
                content: "确定删除该节点（包括子节点）数据？",
                loading: true,
                onOk: () => {
                    this.deleteNode(node, data);
                },
            });
        },
        /**确定删除 菜单按钮 */
        deleteNode(node, data) {
            this.$api.menuUnitary
                .deleteMenu({ id: data.id, serviceKey: this.menuType })
                .then((res) => {
                    if (res && res.data.resultCode == 20000) {
                        this.$Message.success("删除成功！");
                        this.getNodes("0");
                    }
                })
                .finally(() => {
                    this.$Modal.remove();
                });
        },
        /**提交编辑/新增 菜单 */
        handleSubmit(name) {
            let _this = this;
            this.$refs[name].validate((valid) => {
                if (valid) {
                    _this.loading = true;
                    const isAppendNode = _this.asyncRoute.id === null;
                    const msg = _this.$Message.loading({
                        content: isAppendNode ? "正在添加新节点" : "正在更新节点",
                        duration: 0,
                    });
                    _this.asyncRoute.serviceKey = _this.menuType;
                    if (
                        _this.asyncRoute.type != "non-menu" &&
                        _this.asyncRoute.type != "menu2" &&
                        _this.asyncRoute.type != "menu"
                    ) {
                        _this.asyncRoute.homeDisplay = "0";
                    }
                    const data = isAppendNode ? _this.asyncRoute : [_this.asyncRoute];
                    const http = isAppendNode
                        ? this.$api.menuUnitary.addMenu(data)
                        : this.$api.menuUnitary.updateMenu(data);
                    http
                        .then((response) => {
                            // if (isAppendNode) {
                            _this.asyncRoute.id = Number(response.data.id);
                            this.getNodes("0");
                            // }
                            response &&
                                response.data.resultCode == 20000 &&
                                _this.$Message.success(
                                    isAppendNode ? "添加节点成功！" : "更新节点成功！"
                                );
                            _this.isNodeEdited = false;
                        })
                        .finally(() => {
                            _this.loading = false;
                            msg();
                        });
                }
            });
        },
        cancel() {
            this.initAsyncRoute();
            this.isNodeEdited = false;
        },
        handleDragEnd(draggingNode, dropNode, dropType, ev) { },
        handleDragStart(root, node, data) {
            this.dragstartNode = node;
            this.dragstartData = data;
        },
        handleDragOver(root, node, data) {
            const event = window.event || arguments[0];
            event.preventDefault();
        },
        async handleDrop(root, node, data) {
            if (node.nodeKey == 0 && this.dragstartData.parentId != 0) {
                this.$Message.error("非一级节点不允许拖拽成为一级节点");
                return false;
            } else if (
                data.id != this.dragstartData.parentId &&
                data.parentId != this.dragstartData.parentId
            ) {
                this.$Message.error("不允许更换父节点");
                return false;
            } else if (data.parentId == this.dragstartData.parentId) {
                //同级换顺序
                await this.$api.menuUnitary
                    .updateDrag({
                        drag: this.dragstartData,
                        target: data,
                    })
                    .then((response) => {
                        if (response.data.resultCode == "20000")
                            this.$Message.success("更新成功！");
                    });
            }
            this.getNodes("0");
        },
        handleMenuTypeChange(val) {
            this.initAsyncRoute();
            this.getNodes("0");
            this.queryrule();
            this.getDisplayArea();
        },
    },
    async created() {
        this.initAsyncRoute();
        await this.getDictionary()
        this.getNodes("0");
        this.queryrule();
        this.getDisplayArea();
    },
};
</script>

<style lang="less">
.def_row {
    padding: 16px 0 32px 0;
    border-bottom: 1px solid #e8eaec;
    margin-bottom: 16px;
}

.def_radio .ivu-radio-wrapper {
    margin-right: 32px;
}

.ivue-items-btn {
    margin-bottom: 16px;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;

    .ivu-btn {
        margin-left: 8px;
    }
}

.add-root-btn {
    width: 89px !important;
}

.modal-mask {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;

    &-form {
        width: 50%;

        span {
            font-size: 16px;
        }

        .ivu-btn {
            margin: 0 8px;
        }
    }
}

.async-routes-config {
    .query-div {
        padding: 16px 0 0 16px;
        background: #fff;
    }

    .ivu-tree {
        background: #fff;
        padding: 16px;
    }

    .ivu-tree-title {
        width: 100%;
    }
}

.add-route-modal {
    form {
        max-height: calc(100vh - 300px);
        overflow-x: auto;
        padding-bottom: 10px;
    }
}

.tabsSystemMenu {
    background: #fff;
}
</style>
