<template>
  <div class="userEdit other_main">
    <Row :gutter="16" type="flex" justify="center" align="top">
      <Col span="18" style="border: #fff 1px solid; margin: 5px">
      <Form ref="userDetailForm" :model="userDetail" :label-width="140" :rules="ruleValidate">
        <Row type="flex" justify="start" :gutter="16">
          <Col span="12" style="position: relative">
          <FormItem prop="realName" label="真实姓名">
            <Input type="text" :maxlength="12" :show-word-limit="true" v-model="userDetail.realName" placeholder="真实姓名"
              :disabled="disabled || !isAdmin" @on-blur="getUserName">
            <Icon type="md-person" size="14" slot="prepend"></Icon>
            </Input>
            <div class="select-user-btn">
              <!-- <Button type="primary" @click="showSelectUserModel" :loading="isSubmitDisabled" :disabled="disabled"
                size="small">从商信选择</Button> -->
              <!-- <Button type="primary" style="margin-left: 5px" @click="showSelectUserNewModel"
                :loading="isSubmitDisabled" :disabled="disabled" size="small">从人社厅选择</Button> -->
              <Button @click="clearUser" size="small" :disabled="disabled"
                style="margin-left: 5px; margin-right: 5px">清空</Button>
            </div>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem prop="loginName" label="用户名">
            <Input type="text" v-model="userDetail.loginName" placeholder="输入真实姓名后自动生成" :disabled="!!userId">
            <Icon type="md-person" size="14" slot="prepend"></Icon>
            </Input>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem prop="mobile" label="手机">
            <Input type="text" :maxlength="11" :show-word-limit="true" v-model="userDetail.mobile" placeholder="手机"
              :disabled="disabled">
            <Icon type="md-tablet-portrait" size="14" slot="prepend"></Icon>
            </Input>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem prop="backupMobile" label="备用手机号">
            <Input type="text" v-model="userDetail.backupMobile" placeholder="多个备用手机号请用逗号(,)隔开" :disabled="disabled">
            <Icon type="md-tablet-portrait" size="14" slot="prepend"></Icon>
            </Input>
          </FormItem>
          </Col>
          <!-- <Col span="12" v-if="!code">
              <FormItem prop="email" label="电子邮件">
                <Input
                  type="text"
                  :disabled="disabled"
                  v-model="userDetail.email"
                  placeholder="电子邮件"
                >
                  <Icon type="md-mail" size="14" slot="prepend"></Icon>
                </Input>
              </FormItem>
            </Col> -->
          <Col span="12" v-if="!code">
          <FormItem prop="idCardNo" label="身份证号码">
            <Input type="text" :disabled="disabled" v-model="userDetail.idCardNo" maxlength="20" placeholder="身份证号码">
            </Input>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="办公电话">
            <Input type="text" v-model="userDetail.officePhone" placeholder="办公电话" :disabled="disabled">
            <Icon type="md-call" size="14" slot="prepend"></Icon>
            </Input>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="职务">
            <span v-if="orgSortData.length <= 1">
              <Input type="text" v-model="userDetail.post" :disabled="disabled" placeholder="职务">
              <Icon type="ios-briefcase" size="14" slot="prepend"></Icon></Input>
            </span>
            <a @click="openSetOrgSort(0)" v-else>设置职务</a>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="职级">
            <!-- <span v-if="orgSortData.length <= 1">
                  <Input
                    type="text"
                    v-model="userDetail.rank"
                    :disabled="disabled"
                    placeholder="职级"
                  >
                    <Icon type="ios-briefcase" size="14" slot="prepend"></Icon
                  ></Input>
                </span>
                <a @click="openSetOrgSort(0)" v-else>设置职级</a> -->

            <Select v-model="userDetail.rank" :disabled="disabled" filterable transfer clearable>
              <Option v-for="item in currentRankList" :key="item.code" :value="item.code">{{
                item.name
              }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="12" v-if="code">
          </Col>
          <Col span="12">
          <FormItem label="秘书姓名">
            <Input type="text" v-model="userDetail.secretaryName" placeholder="秘书姓名" :disabled="disabled">
            <Icon type="md-call" size="14" slot="prepend"></Icon>
            </Input>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="秘书手机号">
            <Input type="text" v-model="userDetail.secretaryPhone" placeholder="秘书手机号" :disabled="disabled">
            <Icon type="md-call" size="14" slot="prepend"></Icon>
            </Input>
          </FormItem>
          </Col>
          <Col span="24" v-if="isRoleEdit">
          <FormItem prop="roleNames" label="角色">
            <div style="background: #fff" class="border" @click="openRoleModel">
              <Tag v-for="(item, index) in userDetail.roleNames" :key="index" closable
                @on-close="handleCloseRole(item, index)">{{ item.name }}</Tag>
            </div>
          </FormItem>
          </Col>
          <!--    <Col span="24">

              <FormItem label="移动端角色">
                <div style="background: #fff" class="border" @click="openAppRoleModel">
                  <Tag
                    v-for="(item, index) in userDetail.appRoleNames"
                    :key="index"
                    closable
                    @on-close="handleCloseAppRole(item, index)"
                    >{{ item.name }}</Tag
                  >
                </div>
              </FormItem>
            </Col> -->
          <Col span="24">
          <FormItem label="用户唯一信任号" prop="strId">
            <Input type="text" v-model="userDetail.strId" placeholder="" :disabled="true">
            </Input>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem prop="orgNameList" label="机构">
            <div style="background: #fff" class="border" @click="openTree()">
              <Tag v-for="(item, index) in userDetail.orgNameList" :key="index" :closable="!disabled"
                @on-close="handleCloseOrg(item, index)">{{ item.title }}</Tag>
            </div>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="默认机构" prop="defaultDepart">
            <Select v-model="userDetail.defaultDepart" placeholder="请选择默认机构">
              <Option v-for="item in userDetail.orgNameList" :value="item.id" :key="item.id">
                {{ item.title }}
              </Option>
            </Select>
          </FormItem>
          </Col>
          <Col v-show="userDetail.defaultDepart" span="24">
          <FormItem prop="orgManagementList" label="管理机构">
            <div style="background: #fff" class="border" @click="openTree('manage')">
              <Tag v-for="(item, index) in userDetail.orgManagementList || [].map(n => n.name)" :key="index"
                :closable="!disabled" @on-close="handleCloseOrg(item, index, 'manage')">{{
                  item.title }}</Tag>
            </div>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem label="已授权应用" prop="authorizeAppList">
            <div style="background: #fff" class="border" @click="openApplicationModal">
              <Tag v-for="(item, index) in userDetail.authorizeAppList" :key="index">{{
                item.appName }}</Tag>
            </div>
          </FormItem>
          </Col>
          <!-- 人员类型 -->
          <Col span="12">
          <FormItem label="人员类型" prop="userType">
            <Select v-model="userDetail.userType" :disabled="disabled" filterable transfer multiple clearable>
              <Option v-for="item in userTypeList" :key="item.value" :value="item.value">{{ item.label }}
              </Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="12" v-if="dynamicTheme === 'red'">
          <FormItem label="涉密类别" prop="securityClassificationCode" required>
            <Select @on-change="strClassifiedChange" v-model="userDetail.securityClassificationCode"
              :disabled="disabled" label-in-value filterable transfer clearable>
              <Option v-for="(item) in securityClassList" :key="item.value" :value="item.value">{{
                item.label }}
              </Option>
            </Select>
          </FormItem>
          </Col>

          <Col span="12">
          <FormItem label="是否部门领导" prop="departmentHead">
            <RadioGroup v-if="orgSortData.length <= 1" v-model="userDetail.departmentHead" type="button">
              <Radio :label="1" :disabled="disabled">是</Radio>
              <Radio :label="0" :disabled="disabled">否</Radio>
            </RadioGroup>
            <a @click="openSetOrgSort(2)" v-else>设置部门领导</a>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="是否展示" prop="isDisplay">
            <RadioGroup v-model="userDetail.isDisplay" type="button">
              <Radio :label="1">是</Radio>
              <Radio :label="0">否</Radio>
            </RadioGroup>
          </FormItem>
          </Col>
          <!-- 进入单位时间 -->
          <Col span="12">
          <FormItem label="进入单位时间" prop="entryTime">
            <DatePicker :value="userDetail.entryTime" type="date" :disabled="disabled" placeholder="选择日期"
              format="yyyy-MM-dd" @on-change="(date) => setTime(date, 'entryTime')"></DatePicker>
          </FormItem>
          </Col>
          <!-- 开始工作时间 -->
          <Col span="12">
          <FormItem label="开始工作时间" prop="offerTime">
            <DatePicker :value="userDetail.offerTime" type="date" :disabled="disabled" placeholder="选择日期"
              format="yyyy-MM-dd" @on-change="(data) => setTime(data, 'offerTime')"></DatePicker>
          </FormItem>
          </Col>
          <Col span="24">
          <FormItem prop="personLabelList" label="用户标签">
            <div style="background: #fff" class="border" @click="personShow = true">
              <Tag v-for="(item, index) in userDetail.personLabelList" :key="index" :closable="!disabled"
                @on-close="handleClosePersonOrg(item, index)">{{ item.title }}</Tag>
            </div>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="性别" prop="sex">
            <RadioGroup v-model="userDetail.sex">
              <Radio :label="0" :disabled="disabled">男</Radio>
              <Radio :label="1" :disabled="disabled">女</Radio>
            </RadioGroup>
          </FormItem>
          </Col>
          <Col span="12" v-if="!code">
          <FormItem label="系统全局排序">
            <InputNumber :disabled="disabled" :min="1" v-model="userDetail.orderBy"></InputNumber>
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="部门排序号" prop="sort">
            <InputNumber v-if="orgSortData.length <= 1" :min="1" v-model="userDetail.sort" style="margin-right: 10px"
              :disabled="disabled" @on-change="sortChange"></InputNumber>
            <a @click="openSetOrgSort(1)" v-else>设置部门排序</a>
          </FormItem>
          </Col>
          <!--    <Col span="12">
              <FormItem prop="display" label="通讯录是否显示">
                <RadioGroup v-model="userDetail.display" type="button">
                  <Radio :label="1" :disabled="disabled">是</Radio>
                  <Radio :label="0" :disabled="disabled">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          <!--     <Col span="12">
              <FormItem prop="whetherShow" label="手机号是否显示">
                <RadioGroup v-model="userDetail.whetherShow" type="button">
                  <Radio :label="1" :disabled="disabled">是</Radio>
                  <Radio :label="0" :disabled="disabled">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          <!-- <Col span="12">
              <FormItem prop="isWriteSign" label="是否手写签批">
                <RadioGroup v-model="userDetail.isWriteSign" type="button">
                  <Radio :label="1" :disabled="disabled">是</Radio>
                  <Radio :label="0" :disabled="disabled">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          <!-- <Col span="12">
              <FormItem prop="withdrawCondition" label="撤回签批条件" :disabled="disabled">
                <RadioGroup v-model="userDetail.withdrawCondition">
                  <Radio :label="0" :disabled="disabled">下一步处理人未阅</Radio>
                  <Radio :label="1" :disabled="disabled">无</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          <!--     <Col span="12">
              <FormItem prop="auditSms" label="审批后短信提醒">
                <RadioGroup v-model="userDetail.auditSms" type="button">
                  <Radio :label="1" :disabled="disabled">是</Radio>
                  <Radio :label="0" :disabled="disabled">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          <!--   <Col span="12">
              <FormItem prop="isAddressUnit" label="通讯录本单位是否显示">
                <RadioGroup v-model="userDetail.isAddressUnit" type="button">
                  <Radio :label="1" :disabled="disabled">是</Radio>
                  <Radio :label="0" :disabled="disabled">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          <!-- <Col span="12">
              <FormItem label="用户手写签名">
                <SiginUpload
                  ref="SiginUpload"
                  :disabled="disabled"
                  :picList.sync="picList"
                  tipText="建议尺寸30*80px，大小不超过2MB"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="盖章图片地址">
                <SiginUpload
                  ref="stampUpload"
                  :disabled="disabled"
                  :picList.sync="stampPicList"
                  tipText="建议尺寸30*80px，大小不超过2MB"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="起始号">
                <InputNumber
                  :min="1"
                  v-model="userDetail.startNo"
                  style="margin-right: 10px"
                  :disabled="disabled"
                ></InputNumber>
              </FormItem>
            </Col> -->
          <!-- <Col span="12">
          <FormItem
            prop="statistics"
            label="是否统计"
          >
            <RadioGroup
              v-model="userDetail.statistics"
              type="button"
            >
              <Radio :label="1">是</Radio>
              <Radio :label="0">否</Radio>
            </RadioGroup>
          </FormItem>
          </Col> -->
        </Row>
        <Row type="flex" justify="center" style="margin-top: 16px">
          <Button type="primary" @click="handleSubmit('userDetailForm')" :disabled="isSubmitDisabled">提交</Button>
          <Button type="default" @click="handleReset" style="margin: 0 16px">重置</Button>
          <Button type="default" @click="closeMe()">返回</Button>
        </Row>
      </Form>
      </Col>
    </Row>
    <Modal title="设置部门" v-model="treeShow" :mask-closable="false" width="30%" :transfer="false">
      <div class="set-org" v-loading="orgLoading">
        <CommonFilterBar :filterArr="unitRoleFilterArr" ref="roleFilter" @search="unitSearch" @reset="unitReset">
        </CommonFilterBar>
        <Tree :data="orgIdOptions" :check-strictly="true" show-checkbox expand :load-data="loadData"
          @on-check-change="changeTree"></Tree>
      </div>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handleOrgOk">确定</Button>
          <Button @click="treeShow = false">取消</Button>
        </Row>
      </div>
    </Modal>
    <Modal title="选择用户标签" v-model="personShow" :mask-closable="false" width="30%" :transfer="false">
      <div class="set-org" v-loading="orgLoading">
        <Tree :data="personLabelList" :check-strictly="true" show-checkbox expand @on-check-change="changePersonTree">
        </Tree>
      </div>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handlePersonOk">确定</Button>
          <Button @click="personShow = false">取消</Button>
        </Row>
      </div>
    </Modal>

    <Modal :title="isSetSort ? '设置部门排序' : '设置职务'" v-model="orgSortModal" :mask-closable="false" width="60%"
      :transfer="false">
      <div class="set-org">
        <Table stripe border :columns="orgSortColumn" :data="orgSortData">
          <template slot-scope="{ index }" slot="post">
            <Input v-if="!isSetSort" type="text" v-model="orgSortData[index].post" style="width: 100px"
              placeholder="请输入职务"></Input>
            <span v-else>{{ orgSortData[index].post }}</span>
          </template>
          <!-- <template slot-scope="{ index }" slot="rank">
            <Input v-if="!isSetSort" type="text" v-model="orgSortData[index].rank" style="width: 100px"
              placeholder="请输入职级"></Input>
            <span v-else>{{ orgSortData[index].rank }}</span>
          </template> -->
          <template slot-scope="{ index }" slot="departmentHead">
            <RadioGroup v-if="isSetSort == 2" v-model="orgSortData[index].departmentHead" style="width: 100px"
              type="button">
              <Radio :label="1">是</Radio>
              <Radio :label="0">否</Radio>
            </RadioGroup>
            <span v-else>{{ orgSortData[index].departmentHead ? '是' : '否' }}</span>
          </template>
          <template slot-scope="{ row, index }" slot="userOrgSort">
            <InputNumber :min="1" :max="99999" v-if="isSetSort == 1" type="number"
              v-model="orgSortData[index].userOrgSort" style="width: 100px" placeholder="请输入排序号"
              @on-change="orgSortDataChange($event, index)">
            </InputNumber>
            <span v-else>{{ orgSortData[index].userOrgSort }}</span>
            <span v-show="row.isExist" style="margin-left: 10px; color: red">存在用户</span>
          </template>
        </Table>
      </div>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handleOrgSortOk">确定</Button>
          <Button @click="orgSortModal = false">取消</Button>
        </Row>
      </div>
    </Modal>
    <Modal title="设置角色" v-model="roleModal" :mask-closable="false" width="40%" :transfer="false">
      <CommonFilterBar :filterArr="roleFilterArr" ref="roleFilter" @search="roleSearch"></CommonFilterBar>

      <Table stripe border ref="roleTable" :columns="roleColumns" :data="roleList" @on-select="selectRole"
        @on-select-cancel="selectRoleCancel" @on-select-all="selectRoleAll" @on-select-all-cancel="selectRoleAllCancel">
      </Table>
      <Page class="table-page" v-if="roleListTatol > roleListPageSize" :total="roleListTatol"
        :page-size="roleListPageSize" @on-change="changeRolePage" show-elevator show-total
        :current="roleListCurrentPage"></Page>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handleRoleOk">确定</Button>
          <Button @click="roleModal = false">取消</Button>
        </Row>
      </div>
    </Modal>

    <!-- <Modal title="设置移动端角色" v-model="appRoleModal" :mask-closable="false" width="40%" :transfer="false">
      <CommonFilterBar :filterArr="appRoleFilterArr" ref="appRoleFilter" @search="appRoleSearch"></CommonFilterBar>

      <Row style="roleTable">
        <Col span="24">
        <Table stripe border ref="appRoleTable" :columns="appRoleColumns" :data="appRoleList" @on-select="selectAppRole"
          @on-select-cancel="selectAppRoleCancel" @on-select-all="selectAppRoleAll"
          @on-select-all-cancel="selectAppRoleAllCancel"></Table>
        </Col>
        <Col span="24" style="text-align: right;">
        <Page class="mt10" v-if="appRoleListTatol > appRoleListPageSize" :total="appRoleListTatol"
          :page-size="appRoleListPageSize" @on-change="changeAppRolePage" show-elevator show-total
          :current="appRoleListCurrentPage">
        </Page>
        </Col>
      </Row>
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="handleAppRoleOk">确定</Button>
          <Button @click="appRoleModal = false">取消</Button>
        </Row>
      </div>
    </Modal> -->

    <!-- 应用授权弹窗 -->
    <Modal v-if="showApplicationModal" v-model="showApplicationModal" :width="1400" :styles="{ minHeight: '90%' }"
      :mask-closable="false" :transfer="false" title="授权" @on-cancel="onPushApplicationCancel">
      <PushApplication ref="pushApp" pushType="User" :status="1" :authorizedAppList="userDetail.authorizeAppList"
        :authorizedAppListForEdit="appAutoPushList" :isEdit="isEditOrgSort" />
      <div slot="footer">
        <Row type="flex" justify="end">
          <Button type="primary" @click="onPushApplicationOk">确定</Button>
          <Button @click="onPushApplicationCancel">取消</Button>
        </Row>
      </div>
    </Modal>

    <user-select-modal ref="userSelect" :show-tab="[showTab]" other-unit-key="questionSurvey" @getUserList="getUserList"
      key="addQuestion" />
  </div>
</template>

<script>
import CommonFilterBar from '@/views/hnjcoa/components/commonFilterBar.vue';

// import UserSelectModal from '@/views/components/userSelectModal';
import SiginUpload from '@/views/biyi-admin/src/system-page/authority/components/siginUpload.vue';
import { title } from '@/libs/directive';

// 自定义校验
export function validateUserName(rule, value, callback) {
  if (!this.usernameEditable) {
    callback();
  } else {
    let reg = /^[a-zA-Z0-9_-]{4,16}$/;
    if (reg.test(value)) {
      // 正则验证通过,检查用户名是否存在
      this.$api.user
        .cscpUserExistByUsername({ userName: this.userDetail.loginName })
        .then(response => {
          if (response.data.resultData) {
            callback(new Error('当前账号已存在，请重新输入！'));
          } else {
            callback();
          }
        })
        .catch(() => {
          callback(new Error('异步校验出错！'));
        });
    } else {
      callback(new Error('请输入4-16位字母、数字、下划线'));
    }
  }
}
export function validateOrgIdList(rule, value, callback) {
  if (value.length == 0) {
    callback(new Error('机构不能为空！'));
    return;
  } else {
    callback();
  }
}

// 验证也授权应用
export function validateAuthorizeAppList(rule, value, callback) {
  if (value.length == 0) {
    callback(new Error('已授权应用不能为空！'));
    return;
  } else {
    callback();
  }
}
export function validateMobile(rule, value, callback) {
  let reg_tel = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  if (!reg_tel.test(value)) {
    callback(new Error('请正确填写您的手机号码！'));
  } else {
    if (
      this.usernameEditable ||
      (!this.usernameEditable && this.defaultMobile != this.userDetail.mobile)
    ) {
      this.$api.user.cscpUserExistByMobile({ mobile: this.userDetail.mobile }).then(res => {
        if (res && res.data.resultData) {
          callback(new Error('此手机号码已被使用！'));
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  }
}
export function validateMobile1(rule, value, callback) {
  let reg_tel = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  let newValue = value ? value.replace(/，/g, ',') : '';
  let backupMobileList = newValue ? newValue.split(',') : [];
  let status = false;
  if (backupMobileList && backupMobileList.length > 0) {
    status = backupMobileList.find(item => {
      return !reg_tel.test(item);
    });
  }
  if (status) {
    callback(new Error('请正确填写您的手机号码！'));
  } else {
    // if (backupMobileList && backupMobileList.length > 0) {
    //   this.$api.user
    //     .existMobileByMobileList({ mobiles: newValue, userId: this.userId })
    //     .then(res => {
    //       if (res && res.data.resultData) {
    //         let userPhone = res.data.resultData.length > 0 ? res.data.resultData.join(',') : [];
    //         callback(new Error(`${userPhone}备用手机号码已被使用！`));
    //       } else {
    //         callback();
    //       }
    //     });
    // } else {
    //   callback();
    // }
    callback();
  }
}
export default {
  name: 'userDetailEdit',
  components: {
    CommonFilterBar,
    // UserSelectModal,
    // SiginUpload
    PushApplication: () => import('@/views/biyi-admin/src/system-page/authority/appAuthorization/index.vue')
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.fromRoute = from;
    });
  },
  computed: {
    isAdmin() {
      const { userType } = JSON.parse(sessionStorage.getItem('setUserInfo'));
      return userType === 'SYSTEM_USER' || userType === 'PERSON_USER';
    }
  },
  data() {
    const validateSort = (rule, value, callback) => {
      if (this.orgSortData.length <= 1 && !this.userDetail.sort) {
        callback(new Error('请输入部门排序号'));
      } else {
        callback();
      }
    };
    const validateDepartmentHead = (rule, value, callback) => {
      if (this.orgSortData.length <= 1 && this.userDetail.departmentHead == null) {
        callback(new Error('请选择是否部门领导'));
      } else {
        callback();
      }
    };
    return {
      showApplicationModal: false,
      userInfo: {},
      showTab: '',
      departmentId: '',
      dynamicTheme: localStorage.themeColor,
      isRoleEdit: false,
      disabled: false,
      roleFilterArr: [
        {
          label: '角色名称',
          name: 'name',
          value: '',
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      roleFilterForm: {},
      appRoleFilterArr: [
        {
          label: '角色名称',
          name: 'name',
          value: '',
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      unitRoleFilterArr: [
        {
          label: '机构名称',
          name: 'orgName',
          value: '',
          type: 1 // 1-输入框   2-下拉菜单   3-日期时间段
        }
      ],
      appRoleFilterForm: {},
      fromRoute: '',
      defaultOrderBy: 100000000,
      defaultSort: 2,
      defaultMobile: '',
      appQuery: '',
      selectTree: [],
      tempSelectTree: [],
      isTempSelectTree: false,
      selectManageTree: [],
      selectRoleList: [], //表格中已勾选的角色列表
      selectAppRoleList: [], //表格中已勾选的角色列表
      roleModal: false,
      appRoleModal: false,
      roleColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h(
              'span',
              params.index + (this.roleListCurrentPage - 1) * this.roleListPageSize + 1
            );
          }
        },
        {
          key: 'name',
          title: '角色名',
          align: 'center'
        }
      ],
      appRoleColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '序号',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h(
              'span',
              params.index + (this.appRoleListCurrentPage - 1) * this.appRoleListPageSize + 1
            );
          }
        },
        {
          key: 'name',
          title: '角色名',
          align: 'center'
        }
      ],
      roleList: [],
      roleListCurrentPage: 1,
      roleListTatol: 0,
      roleListPageSize: 10,
      appRoleList: [],
      appRoleListCurrentPage: 1,
      appRoleListTatol: 0,
      appRoleListPageSize: 10,
      passwordRule: '',
      currentPasswordError: false,
      userId: null,
      usernameEditable: true,
      isSubmitDisabled: false,
      userDetail: {},
      userTypeList: [],
      newPassword: '',
      orgIdOptions: [], //机构列表
      orgProps: {
        value: 'id',
        expandTrigger: 'hover',
        multiple: true,
        children: 'cscpOrgList',
        label: 'orgName'
      },
      ruleValidate: {
        loginName: [
          {
            required: true,
            message: '用户名不能为空！',
            trugger: 'blur',
            validator: validateUserName
          }
        ],
        realName: [{ required: true, message: '真实姓名不能为空！', trigger: 'blur' }],

        post: [{ required: true, message: '职务不能为空！', trigger: 'blur' }],
        departmentHead: [
          {
            required: true,
            validator: validateDepartmentHead,
            trigger: 'blur'
            // type: "number",
          }
        ],
        roleNames: [{ required: true, message: '角色不能为空！' }],
        appRoleNames: [{ required: true, message: '移动端角色不能为空！' }],
        mobile: [
          { required: true, message: '手机号码不能为空！', trugger: 'blur' },
          { validator: validateMobile.bind(this), trigger: 'blur' }
        ],
        backupMobile: [{ validator: validateMobile1.bind(this), trigger: 'blur' }],
        orgNameList: [
          { required: true, message: '机构不能为空！' },
          { validator: validateOrgIdList.bind(this), trigger: 'change' }
        ],
        defaultDepart: [{ required: true, message: '默认机构不能为空！', trigger: 'change' }],
        authorizeAppList: [
          { required: true, message: '已授权应用不能为空' },
          { type: 'array', min: 1, message: '已授权应用不能为空', trigger: 'change' },
          { validator: validateAuthorizeAppList.bind(this), trigger: 'change' }
        ],
        display: [{ required: true, message: '请选择通讯录是否显示！' }],
        whetherShow: [{ required: true, message: '请选择手机号是否显示！' }],
        isWriteSign: [{ required: true, message: '请选择是否手写签批!' }],
        withdrawCondition: [{ required: true, message: '请选择撤回签批条件!' }],
        statistics: [{ required: true, message: '请选择是否统计！' }],
        auditSms: [{ required: true, message: '请选择审批后是否短信提醒！' }],
        isAddressUnit: [{ required: true, message: '请选择通讯录单位是否显示！' }],
        isDisplay: [{ required: true, message: '请选择是否展示！' }],
        securityClassificationCode: [
          {
            validator: (rule, value, callback) => {
              if (!this.userDetail.securityClassificationCode) {
                callback(new Error('请选择涉密类别！'));
              } else {
                callback();
              }
            }
          }
        ],
        sort: [
          {
            required: true,
            validator: validateSort,
            message: '请输入部门排序号'
          }
        ],
        idCardNo: [
          {
            required: true,
            message: '身份证号不能为空！',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              // 允许18个0通过
              if (value === '000000000000000000') {
                callback();
                return;
              }
              // 正常身份证号校验
              const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}([0-9]|X)$/;
              if (!reg.test(value)) {
                callback(new Error('请输入正确的身份证号！'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
      },
      treeShow: false,
      orgType: '',
      orgSortModal: false,
      orgSortData: [],
      orgSortColumn: [
        {
          key: 'title',
          title: '机构名称',
          align: 'center'
        },
        {
          slot: 'post',
          title: '职务',
          align: 'center'
        },
        // {
        //   slot: 'rank',
        //   title: '职级',
        //   align: 'center'
        // },
        {
          slot: 'departmentHead',
          title: '部门领导',
          align: 'center'
        },
        {
          slot: 'userOrgSort',
          title: '机构排序',
          align: 'center'
        }
      ],
      isSetSort: 0,
      code: '', //按钮权限区分
      /* 签名图片列表 */
      picList: [],
      /* 盖章图片列表 */
      stampPicList: [],
      orgLoading: false,
      currentRankList: [],
      //涉密类别
      securityClassList: [],
      personLabelList: [],
      personShow: false,
      selectPersonTree: [],
      // 自动推送的应用
      appAutoPushList: [],
      // 编辑时已经推送的应用 不能修改
      appAlreadyPushList: [],
      appList: [],
      companyId: '',
    };
  },
  methods: {
    isAdmins() {
      const userType = this.$byStoreGet('userInfo').userType;
      // 判断是否普通用户
      return userType === 'SYSTEM_USER';
    },
    // 获取数据字典中的涉密类别
    getSecuritySource() {
      this.$api.dictionary
        .getDictRecordByDictCode({ code: 'securityClassification' })
        .then(({ data }) => {
          const { resultCode, resultData = [] } = data;
          if (resultCode == 20000) {
            const list = resultData.map(n => {
              return {
                label: n.name,
                value: n.code
              };
            });
            this.securityClassList = list;
          }
        });
    },
    getPersonLabelList() {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'person_label' }).then(({ data }) => {
        const { resultCode, resultData = [] } = data;
        if (resultCode == 20000) {
          const list = resultData.map(n => {
            return {
              title: n.name,
              id: n.code,
              checked: false
            };
          });
          this.personLabelList = list;
        }
      });
    },
    // 获取人员类型
    getUserTypeList() {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'user_type' }).then(({ data }) => {
        const { resultCode, resultData = [] } = data;
        if (resultCode == 20000) {
          this.userTypeList = resultData.map(item => ({
            label: item.name,
            value: item.code
          }));
          console.log(this.userTypeList, 'this.userTypeList');
        }
      });
    },
    strClassifiedChange(e) {
      this.userDetail.securityClassificationCode = e.value;
      this.userDetail.securityClassificationCodeName = e.label;
    },
    getCurrentRank() {
      this.$api.dictionary.getDictRecordByDictCode({ code: 'userRank' }).then(({ data }) => {
        const { resultCode, resultData = [] } = data;
        if (resultCode == 20000) {
          this.currentRankList = resultData;
        }
      });
    },
    roleSearch(params) {
      this.roleFilterForm = JSON.parse(JSON.stringify(params));
      this.roleListCurrentPage = 1;
      this.getRoles();
    },
    appRoleSearch(params) {
      this.appRoleFilterForm = JSON.parse(JSON.stringify(params));
      this.appRoleListCurrentPage = 1;
      this.getAppRoles();
    },
    // 机构名称搜索
    unitSearch(params) {
      console.log(this.userDetail.orgNameList, 'this.userDetail.orgNameList');
      let ids = [];
      if (this.orgType) {
        ids = this.userDetail.orgManagementList.map(item => item.id);
      } else {
        ids = this.userDetail.orgNameList.map(item => item.id);
      }
      console.log(params, 'params');
      let filterForm = JSON.parse(JSON.stringify(params));
      if (filterForm.orgName !== '') {
        // if (this.isAdmin) {
        let msg = this.$Message.loading({
          content: '正在更新数据',
          duration: 0
        });

        this.$api.org
          .selectDivisionOrgByIdName({ orgName: filterForm.orgName, id: this.orgType ? this.companyId : undefined })
          .then(response => {
            this.orgIdOptions = response.data.resultData.map((item) => {
              const transformChildren = (node) => {
                // type 不等于 3 全部禁用
                if (node.type != 3 && this.orgType) {
                  node.disabled = true;
                }
                if (node.children) {
                  node.children = node.children.map(transformChildren); // 递归处理子节点
                  delete node.childOrgs; // 删除原来的 childOrgs 属性
                }
                return node;
              };
              return transformChildren(item);
            });

            // 递归设置所有节点的展开状态
            const expandAllNodes = (nodes) => {
              nodes.forEach(node => {
                this.$set(node, 'expand', true);
                if (node.children && node.children.length > 0) {
                  expandAllNodes(node.children);
                }
              });
            };

            expandAllNodes(this.orgIdOptions);
            console.log(ids, 'ids');
            this.uncheckOrgById(this.orgIdOptions, ids, 'checked');
          })
          .finally(() => {
            msg();
          });
        // }
      } else {
        this.keyWord = '';
        const { checkedList, orgId, isDisabled } = {
          checkedList: [],
          orgId: this.orgType ? this.companyId : undefined,
          isDisabled: this.orgType ? true : false
        };
        this.initOrgTree(checkedList, orgId, isDisabled);
      }
    },
    unitReset() {
      const { checkedList, orgId, isDisabled } = {
        checkedList: [],
        orgId: this.orgType ? this.companyId : undefined,
        isDisabled: this.orgType ? true : false
      };
      this.initOrgTree(checkedList, orgId, isDisabled);
    },
    // 添加一个辅助方法来扁平化树结构
    flattenTree(tree) {
      let result = [];
      const flatten = nodes => {
        nodes.forEach(node => {
          result.push(node);
          if (node.children && node.children.length > 0) {
            flatten(node.children);
          }
        });
      };
      flatten(tree);
      return result;
    },
    // 真实姓名失去焦点时获取用户名
    getUserName() {
      console.log(this.userDetail.realName, 'this.userDetail.realName');
      if (this.usernameEditable && !!this.userDetail.realName) {
        this.$api.user.getUserNameByRealName({ realName: this.userDetail.realName }).then(res => {
          if (res && res.data.resultCode == 20000) {
            this.userDetail.loginName = res.data.resultData;
          }
        });
      }
    },
    openSetOrgSort(status) {
      if (this.orgSortData.length <= 0) {
        this.$Message.info('请先选择机构');
      } else {
        this.isSetSort = status;
        this.orgSortModal = true;
      }
    },
    sortChange(event, index) {
      if (!event) {
        this.$nextTick(() => {
          this.$set(this.userDetail, 'sort', 9999);
          // this.$refs.userDetailForm.validateField("sort");
        });
      }
    },
    orgSortDataChange(event, index) {
      if (!event) {
        this.$nextTick(() => {
          this.orgSortData[index].userOrgSort = 9999;
        });
      }
      this.$set(this.orgSortData[index], 'isEdit', true);
    },
    handleOrgSortOk() {
      let msg = this.$Message.loading({
        content: '正在检测排序号',
        duration: 0
      });
      let promiseArr = [],
        pass = true;
      let arr = JSON.parse(JSON.stringify(this.orgSortData));
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].isEdit) {
          promiseArr.push(
            this.$api.user
              .checkUserOrgSortExist({
                orgId: arr[i].id,
                sort: arr[i].userOrgSort,
                userId: this.userId ? this.userId : ''
              })
              .then(res => {
                if (res.data.resultData) {
                  pass = false;
                  this.$set(this.orgSortData[i], 'isExist', true);
                } else {
                  this.$set(this.orgSortData[i], 'isExist', false);
                }
              })
          );
        }
      }
      Promise.all(promiseArr).then(res => {
        msg();
        if (pass) {
          this.$Message.success('设置成功');
          this.orgSortModal = false;
          for (let i = 0; i < this.orgSortData.length; i++) {
            this.$set(this.orgSortData[i], 'isEdit', false);
            this.$set(this.orgSortData[i], 'isExist', false);
          }
        } else {
          // 排序号已存在
          this.$Modal.confirm({
            title: '提示',
            content: `部分排序号已存在用户，是否继续？`,
            loading: true,
            onOk: () => {
              this.$Modal.remove();
              this.$Message.success('设置成功');
              this.orgSortModal = false;
              for (let i = 0; i < this.orgSortData.length; i++) {
                this.$set(this.orgSortData[i], 'isEdit', false);
                this.$set(this.orgSortData[i], 'isExist', false);
              }
            },
            onCancel: () => { }
          });
        }
      });
    },
    changeTree(arr, obj) {
      if (this.orgType) {
        if (this.code) {
          arr.forEach(e => {
            e.checked = false;
          });
          obj.checked = true;
          this.selectManageTree = [obj];
        } else {
          this.selectManageTree = arr;
        }
        console.log('selectManageTree', this.selectManageTree);
        // 如果取消勾选了已经保存了的机构，则需要将该机构从已选机构中移除
        if (!obj.checked) {
          this.isTempSelectTree = true;
          this.tempSelectTree = this.tempSelectTree.filter(n => n.id !== obj.id);
          console.log('selectManageTree', this.selectManageTree);
        }
      } else {
        if (this.code) {
          arr.forEach(e => {
            e.checked = false;
          });
          obj.checked = true;
          this.selectTree = [obj];
        } else {
          this.selectTree = arr;
        }
        // 如果取消勾选了已经保存了的机构，则需要将该机构从已选机构中移除
        if (!obj.checked) {
          this.isTempSelectTree = true;
          this.tempSelectTree = this.tempSelectTree.filter(n => n.id !== obj.id);
          console.log('tempSelectTree', this.tempSelectTree);
        }
      }
    },
    changePersonTree(arr, obj) {
      if (this.code) {
        arr.forEach(e => {
          e.checked = false;
        });
        obj.checked = true;
        this.selectPersonTree = [obj];
      } else {
        this.selectPersonTree = arr;
      }
    },
    showSelectUserModel() {
      this.showTab = 'supportPlatformSelect';
      let selectedUserList = [];

      this.$refs.userSelect.showUserSelect(selectedUserList);
    },
    showSelectUserNewModel() {
      this.showTab = 'supportPlatformSelectNew';
      let selectedUserList = [];

      this.$refs.userSelect.showUserSelect(selectedUserList);
    },

    handleOrgOk() {
      if (this.orgType) {
        if (this.selectManageTree.length > 0) {
          let arr = this.isTempSelectTree ? this.tempSelectTree || [] : this.userDetail.orgManagementList || []
          const list = this.uniqueArray([
            // ...(this.userDetail.orgManagementList || []),
            ...arr,
            ...this.selectManageTree
          ]);
          this.userDetail.orgManagementList = list;
        }
      } else {
        if (this.selectTree.length > 0 || this.tempSelectTree.length > 0 || this.isTempSelectTree) {
          let arr = this.isTempSelectTree ? this.tempSelectTree || [] : this.userDetail.orgNameList || []
          const list = this.uniqueArray([
            // ...(this.userDetail.orgNameList || []),
            ...arr,
            ...this.selectTree
          ]);
          this.userDetail.orgNameList = list;
          // 如果this.userDetail.orgNameList找不到默认机构的数据就把默认机构设为空
          if (!this.userDetail.orgNameList.find(n => n.id === this.userDetail.defaultDepart)) {
            this.$set(this.userDetail, 'defaultDepart', null);
          }
          console.log(list, 'list');
          this.orgSortData = list.map(n => {
            return {
              id: n.id,
              title: n.title,
              userOrgSort: n.userOrgSort || 9999,
              post: n.post || '',
              rank: n.rank || '',
              departmentHead: 0
            };
          });
          // 如果只选择了一个机构，默认机构自动带入这个机构
          if (this.orgSortData.length == 1) {
            this.$set(this.userDetail, 'defaultDepart', this.orgSortData[0].id);
          }
        }
        this.$refs.userDetailForm.validateField('sort');
        this.$refs.userDetailForm.validateField('orgNameList');
      }
      // 新增时，缓存当前人的层级
      if (this.userDetail.id == null && !this.orgType) {
        const regionOrgIdOptionsInfo = {
          id: this.userInfo.strId,
          orgIdOptions: this.orgIdOptions,
          selectManageTree: this.selectManageTree,
          selectTree: this.selectTree
        }
        sessionStorage.setItem('regionOrgIdOptionsInfo', JSON.stringify(regionOrgIdOptionsInfo));
      }
      this.treeShow = false;
    },
    handlePersonOk() {
      if (this.selectPersonTree.length > 0) {
        const list = this.uniqueArray([
          ...(this.userDetail.personLabelList || []),
          ...this.selectPersonTree
        ]);
        this.userDetail.personLabelList = list;
      }

      this.personShow = false;
    },
    uniqueArray(array) {
      // 去重
      return array.reduce((acc, current) => {
        const x = acc.find(item => item.id === current.id);
        if (!x) {
          acc.push(current);
        }
        return acc;
      }, []);
    },
    // 递归查找并取消选中/勾选中
    uncheckOrgById(tree, ids, type = 'checked') {
      for (let node of tree) {
        if (ids.includes(node.id)) {
          this.$set(node, 'checked', type == 'checked' ? true : false);
          // return true;
        }
        if (node.children && node.children.length > 0) {
          if (this.uncheckOrgById(node.children, ids, type)) {
            // return true;
          }
        }
      }
      return false;
    },
    handleCloseOrg(item, idx, type) {
      if (type) {
        let id = item.id;
        for (let i = 0; i < this.orgIdOptions.length; i++) {
          if (this.orgIdOptions[i].id == id) {
            this.$set(this.orgIdOptions[i], 'checked', false);
          }
        }
        this.userDetail.orgManagementList.splice(idx, 1);
        // this.selectManageTree.splice(idx, 1);
        this.selectManageTree = this.selectManageTree.filter(n => n.id != id);
      } else {
        let id = item.id;
        for (let i = 0; i < this.orgIdOptions.length; i++) {
          if (this.orgIdOptions[i].id == id) {
            this.$set(this.orgIdOptions[i], 'checked', false);
          }
        }
        this.orgSortData.splice(idx, 1);
        this.userDetail.orgNameList.splice(idx, 1);
        if (this.orgSortData.length == 1) {
          this.$set(this.userDetail, 'sort', this.orgSortData[0].userOrgSort);
          this.$set(this.userDetail, 'departmentHead', this.orgSortData[0].departmentHead);
        }
        this.selectTree = this.selectTree.filter(n => n.id != id);
        if (id === this.userDetail.defaultDepart) {
          // 如果删除的部门是默认部门，则将默认部门设置为null
          this.$set(this.userDetail, 'defaultDepart', null);
        }
        // 新增用户时，缓存当前人的层级
        if (this.userDetail.id == null) {
          this.uncheckOrgById(this.orgIdOptions, [item.id], 'noChecked');

          const regionOrgIdOptionsInfo = {
            id: this.userInfo.strId,
            orgIdOptions: this.orgIdOptions,
            selectManageTree: this.selectManageTree,
            selectTree: this.selectTree
          }
          sessionStorage.setItem('regionOrgIdOptionsInfo', JSON.stringify(regionOrgIdOptionsInfo));
        }
      }
    },
    handleClosePersonOrg(item, idx, type) {
      let id = item.id;
      for (let i = 0; i < this.personLabelList.length; i++) {
        if (this.personLabelList[i].id == id) {
          this.$set(this.personLabelList[i], 'checked', false);
        }
      }
      this.userDetail.personLabelList.splice(idx, 1);
      this.selectPersonTree.splice(idx, 1);
    },
    handleCloseRole(item, idx) {
      let id = item.id;
      for (let i = 0; i < this.roleList.length; i++) {
        if (this.roleList[i].id == id) {
          this.$set(this.roleList[i], '_checked', false);
          this.$refs.roleTable.objData[i]._isChecked = false;
        }
      }
      this.userDetail.roleNames.splice(idx, 1);
    },
    handleCloseAppRole(item, idx) {
      let id = item.id;
      for (let i = 0; i < this.appRoleList.length; i++) {
        if (this.appRoleList[i].id == id) {
          this.$set(this.appRoleList[i], '_checked', false);
          this.$refs.appRoleTable.objData[i]._isChecked = false;
        }
      }
      this.userDetail.appRoleNames.splice(idx, 1);
    },
    getUserList(val) {
      console.log(val, this.userDetail, 1111, this.usernameEditable);
      let soure = {};
      const u = val[0];
      if (!this.usernameEditable) {
        if (this.userDetail.mobile) {
          u.mobile = this.userDetail.mobile;
        }
        if (this.userDetail.idCardNo) {
          u.idCardNo = this.userDetail.idCardNo;
        }
        if (this.userDetail.loginName) {
          u.loginName = this.userDetail.loginName;
        }
      }
      this.userDetail = Object.assign(this.userDetail, {
        realName: u.strUserId,
        loginName: this.usernameEditable ? u.strUserId : this.userDetail.loginName,
        mobile: u.mobile,
        ...u
      });
      this.getUserName();
      this.$forceUpdate();
    },
    clearUser() {
      this.$Modal.confirm({
        title: '提示',
        content: `是否清空用户？`,
        loading: true,
        onOk: () => {
          this.$Modal.remove();
          this.userDetail = {};
        },
        onCancel: () => { }
      });
    },
    async openTree(type) {
      if (this.disabled) return;
      this.isTempSelectTree = false
      this.orgType = type;
      let params = [];
      if (type) {
        // 管理机构打开获取单位id
        await this.$api.menu.getCompanyIdByOrgId(this.userDetail.defaultDepart).then(res => {
          this.companyId = res.data.resultData || this.userDetail.defaultDepart || '';
          console.log(this.companyId, 'this.companyId');
        }).catch(() => { });
        this.tempSelectTree = [...this.userDetail.orgManagementList]
        params = (this.userDetail.orgManagementList || []).map(n => {
          n = n.id;
          return n;
        });
      } else {
        this.tempSelectTree = [...this.userDetail.orgNameList]
        params = (this.userDetail.orgNameList || []).map(n => {
          n = n.id;
          return n;
        });
      }
      const regionOrgIdOptionsInfo = JSON.parse(sessionStorage.getItem('regionOrgIdOptionsInfo'));
      if (!this.userDetail.id && regionOrgIdOptionsInfo && regionOrgIdOptionsInfo.id == this.userInfo.strId && !type) {
        this.orgIdOptions = regionOrgIdOptionsInfo.orgIdOptions;
        this.selectManageTree = regionOrgIdOptionsInfo.selectManageTree;
        this.selectTree = regionOrgIdOptionsInfo.selectTree;
      } else {
        let { orgId, isDisabled } = type ? { orgId: this.companyId, isDisabled: true } : { orgId: 0, isDisabled: false };
        this.initOrgTree(params, orgId, isDisabled);
      }
      // this.initOrgTree(params);
      this.treeShow = true;
      // this.getOrgChang(params);
    },
    initOrgTree(checkedList = [], orgId = 0, isDisabled = false) {
      this.getOrgData(orgId, checkedList, isDisabled).then(res => {
        console.log('initOrgTree', res);

        this.orgIdOptions = res;
      });
    },
    loadData(item, callback) {
      let params = [];
      if (this.orgType) {
        params = (this.userDetail.orgManagementList || []).map(n => {
          n = n.id;
          return n;
        });
      } else {
        params = (this.userDetail.orgNameList || []).map(n => {
          n = n.id;
          return n;
        });
      }
      const isDisabled = this.orgType ? true : false;
      this.getOrgData(item.id, params, isDisabled).then(res => {
        callback(res);
      });
    },
    closeTree() {
      this.treeShow = false;
    },
    getOrgData(id, checkedList = [], isDisabled = false) {
      let params = {}
      if (id) {
        params = { id: id };
      }
      return new Promise((resolve, reject) => {
        this.orgLoading = true;
        this.$api.org
          .selectRegionOrgById(params)
          .then(res => {
            this.orgLoading = false;
            resolve(
              res.data.resultData.map(n => {
                return n.isValuable
                  ? {
                    id: n.id,
                    title: n.title,
                    disabled: isDisabled && n.type != 3,
                    render: (h, { root, node, data }) => {
                      return h(
                        'span',
                        {
                          class: n.type === 1 && 'disbaled_node'
                        },
                        data.title
                      );
                    },
                    checked: checkedList.includes(n.id),
                    children: [],
                    loading: false
                  }
                  : {
                    id: n.id,
                    title: n.title,
                    disabled: isDisabled && n.type != 3,
                    render: (h, { root, node, data }) => {
                      return h(
                        'span',
                        {
                          class: n.type === 1 && 'disbaled_node'
                        },
                        data.title
                      );
                    },
                    checked: checkedList.includes(n.id)
                  };
              })
            );
          })
          .catch(err => {
            reject(err);
            this.orgLoading = false;
          });
      });
    },
    /**
     * param：已选机构ID列表
     * 编辑：获取已选机构列表关系树
     * 接口名：listBatchQryParentOrgId
     */
    getOrgChang(arr) {
      let _this = this;
      this.$api.org
        .queryOrgListCheckedNode(arr || [], { parentId: '0' })
        .then(res => {
          _this.orgIdOptions = this.getDepartmentTree(res.data.resultData);
          // _this.orgIdOptions = res.data.resultData;
          //this.selectTree = this.getDepartmentTree(res.data.resultData);
          //console.log(this.selectTree)
        })
        .catch();
    },
    // 设置单位不可勾选
    getDepartmentTree(arr) {
      for (let i = 0; i < arr.length; i++) {
        arr[i].expand = true;
        if (arr[i].detailsData.type == 2) {
          arr[i].disabled = true;
        }
        if (arr[i].children.length > 0) {
          this.getDepartmentTree(arr[i].children);
        }
      }
      return arr;
    },
    init() {
      this.handleReset();
      // this.getRoles();
      // this.initOrgId();
      this.getCurrentRank();
      this.getPasswordRule();
      this.getSecuritySource();
    },
    // 判断用户在该部门排序号是否存在
    getSortExist(msg) {
      const userDetail = JSON.parse(JSON.stringify(this.userDetail));
      let params = {
        orgId: userDetail.orgNameList[0].id,
        sort: userDetail.sort ? userDetail.sort : 9999,
        userId: this.userId ? this.userId : ''
      };
      this.$api.user
        .checkUserOrgSortExist(params)
        .then(res => {
          if (res.data.resultData && this.defaultSort != this.userDetail.sort) {
            // 排序号已存在
            this.$Modal.confirm({
              title: '提示',
              content: `部门排序号 ${this.userDetail.sort} 已存在用户，是否继续？`,
              loading: true,
              onOk: () => {
                this.$Modal.remove();
                this.isSubmitDisabled = true;
                let data = null;
                data = userDetail;
                this.submitUserDetails(this.userId, data);
              },
              onCancel: () => {
                msg();
              }
            });
          } else {
            this.isSubmitDisabled = true;
            let data = null;
            data = userDetail;
            this.submitUserDetails(this.userId, data);
          }
        })
        .finally(error => {
          msg();
        });
    },
    handleSubmit: function (name) {
      this.$refs[name].validate(valid => {
        console.log(valid);
        if (valid) {
          let msg = this.$Message.loading({
            content: '正在检测排序号',
            duration: 0
          });
          if (this.orgSortData.length == 1) {
            this.orgSortData[0].userOrgSort = this.userDetail.sort ? this.userDetail.sort : 9999;
            this.orgSortData[0].post = this.userDetail.post;
            this.orgSortData[0].rank = this.userDetail.rank;
            this.orgSortData[0].departmentHead = this.userDetail.departmentHead;
          }
          const uploadList = this.$refs.SiginUpload && this.$refs.SiginUpload.uploadList;
          if (uploadList && uploadList.length > 0) {
            const item = uploadList[0];
            this.userDetail.signatureImageURL = item.imgPath || '';
          } else {
            this.userDetail.signatureImageURL = '';
          }
          const uploadListstamp = this.$refs.stampUpload && this.$refs.stampUpload.uploadList;
          if (uploadListstamp && uploadListstamp.length > 0) {
            const item = uploadListstamp[0];
            this.userDetail.stampUrl = item.imgPath || '';
          } else {
            this.userDetail.stampUrl = '';
          }
          this.userDetail.orderBy = this.userDetail.orderBy ? this.userDetail.orderBy : 9999;
          this.$api.user
            .checkUserSortExist({
              userId: this.userId ? this.userId : '',
              sort: this.userDetail.orderBy
            })
            .then(res => {
              if (res.data.resultData && this.defaultOrderBy != this.userDetail.orderBy) {
                // 排序号已存在
                this.$Modal.confirm({
                  title: '提示',
                  content: `系统全局排序 ${this.userDetail.orderBy} 已存在用户，是否继续？`,
                  loading: true,
                  onOk: () => {
                    if (this.orgSortData.length == 1) {
                      this.$Modal.remove();
                      setTimeout(() => {
                        this.getSortExist(msg);
                      }, 1000);
                    } else {
                      msg();
                      this.$Modal.remove();
                      this.isSubmitDisabled = true;
                      let data = JSON.parse(JSON.stringify(this.userDetail));
                      this.submitUserDetails(this.userId, data);
                    }
                  },
                  onCancel: () => {
                    msg();
                  }
                });
              } else {
                if (this.orgSortData.length == 1) {
                  this.getSortExist(msg);
                } else {
                  msg();
                  this.isSubmitDisabled = true;
                  let data = JSON.parse(JSON.stringify(this.userDetail));
                  this.submitUserDetails(this.userId, data);
                }
              }
            })
            .catch(error => {
              msg();
            });
        }
      });
    },
    submitUserDetails(userId, data) {
      console.log(data);
      data.orgManagementList = this.userDetail?.orgManagementList?.map(n => {
        return {
          userId: this.userId,
          deptId: n.id,
          deptName: n.title
        };
      });
      data.personLabelList = this.userDetail?.personLabelList?.map(n => {
        return n.id;
      });
      console.log(data.personLabelList);
      // 处理人员类型
      if (data?.userType.length > 0) {
        data.userTypeList = data.userType.map((item) => {
          return { userType: item }
        })
      } else {
        data.userTypeList = [];
      }
      delete data.userType;
      //备用手机号处理
      data.backupMobile = data.backupMobile ? data.backupMobile.replace(/，/g, ',') : '';

      let msg = this.$Message.loading({
        content: this.userId !== null ? '正在更新用户' : '正在创建新用户',
        duration: 0
      });
      this.currentPasswordError = false;
      let [...arr] = this.userDetail.orgNameList;
      let arr1 = [];
      let nameList = [];
      arr.forEach(e => {
        arr1.push(e.id);
        nameList.push({ id: e.id, title: e.title });
      });
      let roleIds = [];
      let appRoleIds = [];
      data.roleNames
        ? data.roleNames.map(e => {
          roleIds.push(e.id);
        })
        : [];
      data.appRoleNames
        ? data.appRoleNames.map(e => {
          appRoleIds.push(e.id);
        })
        : [];
      data.roleIds = roleIds;
      data.appRoleIds = appRoleIds;
      this.orgSortData.forEach(e => {
        if (e.id == this.departmentId) {
          e.rank = data.rank;
        }
      });

      data.orgNameList = this.orgSortData;
      // data.orgIdList = arr1;
      data.orgIdList = undefined;
      // data.defaultDepart = arr1[0];

      // 处理授权应用 pushAppCode 应用编码字符串 用,分割
      data.pushAppCode = data.authorizeAppList
        .map(app => app.appCode)
        .filter(appCode => !!appCode)
        .join(',');
      // 删除 data 里的 authorizeAppList

      delete data.authorizeAppList;
      if (userId !== null) {
        this.currentPasswordError = false;

        this.$api.user
          .updateCscpUser(data)
          .then(resposne => {
            if (resposne.data.resultData.id) {
              this.$Message.success({
                content: '更新用户成功！',
                onClose: () => {
                  this.closeMe();
                }
              });
              // this.pushUserAuto(resposne.data.resultData.id, 'update');
            } else {
              this.isSubmitDisabled = false;
            }
          })
          .catch(error => {
            this.isSubmitDisabled = false;
          })
          .finally(() => {
            msg();
          });
      } else {
        this.$api.user
          .createCscpUser(data)
          .then(resposne => {
            if (resposne.data.resultData.id) {
              this.$Message.success({
                content: '新建用户成功！',
                onClose: () => {
                  this.closeMe();
                }
              });
              //http请求
              // this.pushUserAuto(resposne.data.resultData.id, 'add');
            } else {
              this.isSubmitDisabled = false;
            }
          })
          .catch(error => {
            this.isSubmitDisabled = false;
          })
          .finally(() => {
            msg();
          });
      }
    },
    pushUserAuto(userId, flag) {
      this.$api.user.syncUserBusiness({ userId, flag });
    },
    handleReset() {
      this.code = '';
      if (this.$route.name === 'region-user-edit') {
        // 编辑用户
        this.usernameEditable = false;
        this.isEditOrgSort = true;
        this.userId = this.$byStoreGet('user-edit')?.row?.id;
        this.code = this.$byStoreGet('user-edit')?.row?.code;
        this.disabled = this.$byStoreGet('user-edit')?.row?.disabled;
        if (this.$byStoreGet('user-edit')?.row?.disabled) {
          this.isRoleEdit = true;
        } else {
          this.isRoleEdit = false;
        }
        this.departmentId = this.$byStoreGet('user-edit')?.row?.departmentId;
        this.getData(this.userId, this.departmentId);
      } else {
        // 新增用户
        this.code = this.$byStoreGet('user-add')?.row?.code;
        this.usernameEditable = true;
        this.userDetail = {
          id: null,
          loginName: null,
          realName: null,
          mobile: null,
          backupMobile: null,
          officePhone: null,
          post: null,
          rank: null,
          email: null,
          roleNames: [],
          appRoleNames: [],
          orgIdList: [],
          orgNameList: [],
          orgManagementList: [],
          display: 1,
          whetherShow: 1,
          statistics: 1,
          departmentHead: 0,
          orderBy: 9999,
          sex: 0,
          sort: 1,
          isWriteSign: 1,
          withdrawCondition: 0,
          auditSms: 0,
          isAddressUnit: 1,
          signatureImageURL: null,
          stampUrl: null,
          startNo: 1,
          strId: '',
          isDisplay: 1,
          authorizeAppList: [...this.appAutoPushList],
        };
        // this.getOrgChang();
        this.initOrgTree([]);
      }
    },
    getArr(id, arr) {
      const obg = {
        value: id
      };

      if (this.personLabelList.find(res => res.id == obg.value)) {
        this.personLabelList.find(res => res.id == obg.value).checked = true;
      }
      return arr.find(res => res.id == obg.value);
    },
    getData(userId, departmentId) {
      if (!userId) {
        return;
      }
      let msg = this.$Message.loading({
        content: '正在获取用户信息',
        duration: 0
      });
      this.$api.user
        .cscpUsers(userId)
        .then(response => {
          this.userDetail = { ...response.data.resultData };
          this.userDetail.display = this.userDetail.display ? 1 : 0;
          this.userDetail.statistics = this.userDetail.statistics ? 1 : 0;
          this.userDetail.auditSms = this.userDetail.auditSms ? 1 : 0;
          this.userDetail.isAddressUnit = this.userDetail.isAddressUnit ? 1 : 0;
          this.userDetail.securityClassificationCode = this.userDetail.securityClassificationCode;
          this.userDetail.securityClassificationCodeName =
            this.userDetail.securityClassificationCodeName;
          this.defaultOrderBy = response.data.resultData.orderBy;
          this.defaultMobile = response.data.resultData.mobile;
          // 获取已经推送的应用
          this.appAlreadyPushList = this.appList.filter(app => {
            return response.data.resultData.pushAppCode && response.data.resultData.pushAppCode.split(',').includes(app.appCode);
          });
          // 人员类型
          this.userDetail.userType = response.data.resultData.userTypeList
            ? response.data.resultData.userTypeList.map(item => item.userType)
            : [];
          this.userDetail.authorizeAppList = [...this.appAlreadyPushList]
          console.log(this.appAlreadyPushList, 'this.appAlreadyPushList');
          const personList = [];
          if (this.userDetail.personLabelList && this.userDetail.personLabelList.length > 0) {
            for (let i = 0; i < this.userDetail.personLabelList.length; i++) {
              let obj = this.getArr(this.userDetail.personLabelList[i], this.personLabelList);
              console.log(obj, 'obj');
              if (obj) {
                obj.checked = true;
                personList.push(obj);
              }
            }
            this.userDetail.personLabelList = personList;
          }

          console.log(personList, 'personList');

          this.orgSortData = JSON.parse(JSON.stringify(this.userDetail.orgNameList));

          this.userDetail.orgManagementList = this.userDetail.orgManagementList.map(n => {
            return {
              title: n.deptName,
              ...n,
              id: n.deptId
            };
          });

          this.orgManagementList = JSON.parse(JSON.stringify(this.userDetail.orgManagementList));

          if (this.orgSortData.length > 0) {
            // this.userDetail.rank = this.orgSortData.find(orgID => orgID.id === departmentId).rank;

            this.userDetail.sort = this.orgSortData[0].userOrgSort;
            this.userDetail.post = this.orgSortData[0].post;

            this.userDetail.departmentHead = this.orgSortData[0].departmentHead
              ? this.orgSortData[0].departmentHead
              : 0;
            this.defaultSort = this.orgSortData[0].userOrgSort;
          }
          // 初始化用户签批图片
          if (this.userDetail.signatureImageURL) {
            const obj = { imgPath: this.userDetail.signatureImageURL };
            this.picList.push(obj);
            this.$refs.SiginUpload && this.$refs.SiginUpload.handleImg();
          }
          // 初始化用户签批图片
          if (this.userDetail.stampUrl) {
            const obj = { imgPath: this.userDetail.stampUrl };
            this.stampPicList.push(obj);
            this.$refs.stampUpload && this.$refs.stampUpload.handleImg();
          }

          // this.getOrgChang(response.data.resultData.orgIdList);
          this.initOrgTree(response.data.resultData.orgIdList);
        })
        .finally(() => {
          msg();
        });
    },
    handleRoleOk() {
      this.userDetail.roleNames = this.selectRoleList;
      this.$refs.userDetailForm.validateField('roleNames');
      this.roleModal = false;
    },
    handleAppRoleOk() {
      this.userDetail.appRoleNames = this.selectAppRoleList;
      // this.$refs.userDetailForm.validateField("appRoleNames");
      this.appRoleModal = false;
    },
    // 对象数组取差集
    getDiffArr(allArr, partArr) {
      for (let i = allArr.length - 1; i >= 0; i--) {
        for (let j = 0; j < partArr.length; j++) {
          if (allArr[i].id === partArr[j].id) {
            allArr.splice(i, 1);
            break;
          }
        }
      }
      return allArr;
    },
    // =====================PC端角色开始=================
    // 单选
    selectRole(selection, row) {
      this.selectRoleList.push(row);
    },
    // 取消单选
    selectRoleCancel(selection, row) {
      let index = this.selectRoleList.findIndex(n => {
        return n.id == row.id;
      });
      this.selectRoleList.splice(index, 1);
    },
    // 全选
    selectRoleAll(selection) {
      let arr = this.getDiffArr(selection, this.selectRoleList);
      this.selectRoleList.push(...arr);
    },
    // 取消全选
    selectRoleAllCancel(selection) {
      this.roleList.forEach(n => {
        this.selectRoleCancel('', n);
      });
    },
    // =====================PC端角色结束=================
    // =====================移动端角色开始=================
    // 单选
    selectAppRole(selection, row) {
      this.selectAppRoleList.push(row);
    },
    // 取消单选
    selectAppRoleCancel(selection, row) {
      let index = this.selectAppRoleList.findIndex(n => {
        return n.id == row.id;
      });
      this.selectAppRoleList.splice(index, 1);
    },
    // 全选
    selectAppRoleAll(selection) {
      let arr = this.getDiffArr(selection, this.selectAppRoleList);
      this.selectAppRoleList.push(...arr);
    },
    // 取消全选
    selectAppRoleAllCancel(selection) {
      this.appRoleList.forEach(n => {
        this.selectAppRoleCancel('', n);
      });
    },
    // =====================移动端角色结束=================
    openRoleModel() {
      this.roleModal = true;
      this.selectRoleList = JSON.parse(JSON.stringify(this.userDetail.roleNames)) || [];
      this.$refs.roleFilter.reset();
    },
    openAppRoleModel() {
      if (this.disabled) return;
      this.appRoleModal = true;
      this.selectAppRoleList = JSON.parse(JSON.stringify(this.userDetail.appRoleNames)) || [];
      this.$refs.appRoleFilter.reset();
    },
    async changeRolePage(page) {
      this.roleListCurrentPage = page;
      await this.getRoles();
    },
    async getRoles() {
      let data = {
        ...this.roleFilterForm,
        currentPage: this.roleListCurrentPage,
        pageSize: this.roleListPageSize
      };
      if (this.code) {
        data.rangeType = '10';
      }
      this.$api.role
        .getCscpRoless(data)
        .then(response => {
          this.roleList = response.data.resultData.data;
          this.roleListTatol = response.data.resultData.recordsTotal;
          for (let i = 0; i < this.selectRoleList.length; i++) {
            for (let j = 0; j < this.roleList.length; j++) {
              if (this.selectRoleList[i].id == this.roleList[j].id) {
                this.$set(this.roleList[j], '_checked', true);
                break;
              }
            }
          }
          // return response.data.resultData.data;
        })
        .catch();
    },
    async changeAppRolePage(page) {
      this.appRoleListCurrentPage = page;
      await this.getAppRoles();
    },
    async getAppRoles() {
      this.$api.role
        .queryTAppRolePage({
          currentPage: this.appRoleListCurrentPage,
          pageSize: this.appRoleListPageSize,
          ...this.appRoleFilterForm
        })
        .then(response => {
          this.appRoleList = response.data.resultData.records;
          this.appRoleListTatol = response.data.resultData.total;
          for (let i = 0; i < this.selectAppRoleList.length; i++) {
            for (let j = 0; j < this.appRoleList.length; j++) {
              if (this.selectAppRoleList[i].id == this.appRoleList[j].id) {
                this.$set(this.appRoleList[j], '_checked', true);
                break;
              }
            }
          }
          return response.data.resultData.records;
        })
        .catch();
    },
    initOrgId() {
      this.$api.org
        .selectOrgList({ size: 1000, parentId: '0' })
        .get(url, httpConfig)
        .then(response => {
          this.orgIdOptions = response.data.resultData;
        })
        .catch();
    },
    closeMe() {
      this.$store.commit('app/closePage', {
        vm: this,
        fromName: this.$route.name,
        toName: 'regionUserManagement'
      });
    },
    getPasswordRule() {
      this.$api.login.cscpUserPasswordRule().then(res => {
        this.passwordRule = res.data;
      });
    },
    onPushApplicationCancel() {
      this.showApplicationModal = false;
    },
    openApplicationModal() {
      if (this.disabled) return;
      this.showApplicationModal = true;
      // this.pushApplicationList = JSON.parse(JSON.stringify(this.userDetail.authorizeAppList)) || [];
      // this.$refs.applicationFilter.reset();
    },
    onPushApplicationOk() {
      // 拿到已经选中的应用
      this.userDetail.authorizeAppList = this.$refs.pushApp.selectedRowData || [];
      this.showApplicationModal = false;
    },
    //获取应用列表
    async getApplicationList() {
      const params = {
        currentPage: 1,
        pageSize: 1000,
        // autoPush: 1,
        status: 1
      }
      const { data } = await this.$api.applicationManager.queryTSyncAppSystemManagePage(params);
      // 自动推送的应用
      this.appList = data.resultData.data;
      this.appAutoPushList = data.resultData.data.filter(item => item.autoPush == 1) || [];
    },
    setTime(e, key) {
      // console.log(e, 'e');
      this.userDetail[key] = e;
    }
  },
  async created() {
    await this.getApplicationList().catch(() => {
      this.$Message.error('获取应用列表失败，请稍后重试！');
    });
    this.userInfo = JSON.parse(sessionStorage.getItem('setUserInfo'))
    this.getPersonLabelList();
    this.getUserTypeList();
    this.init();
  },
  mounted() { }
};
</script>

<style lang="less">
@import './image-editor.less';
@import '../../styles/common.less';

.set-org {
  position: relative;
  max-height: 500px;
  overflow-y: auto;
}

.userEdit {
  .el-cascader-node__postfix {
    position: relative !important;
  }

  .roleTable {
    margin-top: 10px;
    max-height: 576px;
    overflow: scroll;
    color: rgb(255, 255, 255);
  }
}

.select-user-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 9;
}
</style>
