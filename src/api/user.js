/*
 * @Author: srd15999579174 <EMAIL>
 * @Date: 2023-06-14 11:11:19
 * @LastEditors: sumin <EMAIL>
 * @LastEditTime: 2025-02-25 16:05:04
 * @FilePath: \biyi-oa-web\src\api\user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 用户相关接口
 */
import { http } from '@/libs/util.js';
const user = {};

// 新增用户
user.createCscpUser = data => http('post', '/api/system/createCscpUser', data);

// 修改用户
user.updateCscpUser = data => http('put', '/api/system/updateCscpUser', data);

// 获取当前登录用户的基本信息
user.cscpCurrentUser = data => http('get', '/api/system/cscpCurrentUser', data);

// 传入用户的真实姓名，产生一个唯一的用户名
user.getUserNameByRealName = data => http('get', '/api/system/getUserNameByRealName', data);

// 判断手机号码是否存在
user.cscpUserExistByMobile = data => http('get', '/api/system/cscpUserExistByMobile', data);

// 判断备用手机号码是否存在
user.existMobileByMobileList = data => http('get', '/api/system/existMobileByMobileList', data);

// 根据用户的登录信息判断用户是否存在
user.cscpUserExistByUsername = data => http('get', '/api/system/cscpUserExistByUsername', data);

// 根据id获取单个用户信息
user.cscpUsers = (id, data) => http('get', '/api/system/cscpUsers/' + id, data);

// 根据id删除用户
user.deleteCscpUser = data => http('delete', '/api/system/deleteCscpUser', data);

// 批量删除
user.disableCscpUser = data => http('put', '/api/system/disableCscpUser', data);

// 分页查询用户信息
user.getCscpUsers = data => http('get', '/api/system/getCscpUsers', data);

// 分页查询用户信息2
user.getCscpUsersList = data => http('get', '/api/system/getCscpUserList', data);

// 分页根据机构查询用户信息
user.getCscpUserListByOrgId = data => http('get', '/api/system/getCscpUserListByOrgId', data);

// 新分页根据机构查询用户信息
user.getNewCscpUserListByOrgId = data => http('get', '/api/system/getNewCscpUserListByOrgId', data);

// 更改用户激活状态
user.updateUserStatus = (id, status) =>
  http('post', '/api/system/updateUserStatus/' + id + '/' + status);

// 判断该租户下的用户表排序号是否存在
user.checkUserSortExist = data => http('get', '/api/system/checkUserSortExist', data);

// 判断该用户在该部门的排序号是否存在
user.checkUserOrgSortExist = data => http('get', '/api/system/checkUserOrgSortExist', data);

// 获取租户在线人数和本单位在线人数
user.numberOfPeopleOnline = () => http('get', '/api/system/numberOfPeopleOnline');

// 获取本单位在线人数和未在线人数
user.statisticsSignInPeople = () => http('get', '/api/system/statisticsSignInCompanyPeople');

// 获取租户在线人数和未在线人数
user.statisticsSignInTenantPeople = () => http('get', '/api/system/statisticsSignInTenantPeople');

// app登录，只校验手机号码，不校验密码
user.appLogin = data => http('post', '/api/system/appLogin', data);

// 保存用户部门关系
user.saveUserOrg = data => http('post', '/api/system/saveUserOrg', data);

// 查询用户组织机构树
user.querySxOrgTree = data => http('post', '/api/sxSyncUserOrgData/querySxOrgTree', data);

// 优化的查询用户组织机构树
user.querySxOrgTreeNew = data => http('post', '/api/sxSyncUserOrgData/querySxOrgTreeNew', data);

// 查询用户组织机构树-人社厅数据
user.queryBizHrsUnitInfo = data => http('get', '/api/bizHrsUserInfo/queryBizHrsUnitInfo', data);

// 查询用户
user.queryBizHrsUserInfoByStrId = data =>
  http('get', '/api/bizHrsUserInfo/queryBizHrsUserInfoByStrId', data);

// 选中两个机构点同步信息提交机构名称

user.syncBizHrsUserInfo = (hrsUnitName, deptName) =>
  http(
    'post',
    `/api/bizHrsUserInfo/syncBizHrsUserInfo?hrsUnitName=${hrsUnitName}&deptName=${deptName}`
  );

// 查询商信组织结构树节点下的用户
user.querySxUserByTreeId = (data, params) =>
  http('post', '/api/sxSyncUserOrgData/querySxUserByTreeId', data, params);

// 第三方登录
user.swLogin = userStrId => http('post', '/api/system/swLoginPC?userStrId=' + userStrId);

// 第三方登录
user.swLogin2 = (userStrId, loginName) =>
  http('post', `/api/system/swLogin?userStrId=${userStrId}&loginName=${loginName}`);

// 登陆重复名处理
user.fixDuplicateLogin = () => http('get', `/api/system/fixDuplicateLogin`);

// 用户自动推送
user.syncUserBusiness = data => http('post', `/api/tSyncAppSystemManage/syncUserBusiness`, data);

// 机构管理 查看成员 批量推送
user.autoSyncUserBusiness = data => http('post', `/api/system/auto/syncUserBusiness`, data);

//导出用户
user.exportUser = data => http('post', '/api/sxSyncUserOrgData/querySxUserByTreeId/export', data);

// 用户启用
user.enableCscpUser = data => http('put', '/api/system/enableCscpUser', data);

//  统一社会代码是否重复
user.checkSocialCreditCode = data => http('get', `/api/system/checkCreditCode`, data);

// 提交删除用户申请-POST请求
user.submitDeleteAudit = data => http('post', '/api/system/submitDeleteAudit', data);

// 区划管理员-获取待审批列表
user.getDivisionOrgAuditList = data => http('get', '/api/system/selectApprovalList', data);

// 区划管理员-更新审批结果状态(审批通过=1,审批驳回=2)
user.updateAuditStatus = data => http('post', '/api/system/updateAuditStatus', data);
export default user;
