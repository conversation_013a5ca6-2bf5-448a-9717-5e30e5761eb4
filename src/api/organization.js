/*
 * @Author: srd17680162958 <EMAIL>
 * @Date: 2023-04-12 17:53:57
 * @LastEditors: sumin <EMAIL>
 * @LastEditTime: 2025-05-23 19:45:34
 * @FilePath: /oaweb/src/api/organization.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

/**
 * 机构相关接口
 */
import { http } from '@/libs/util.js';
const org = {};

// 查询本单位用户
org.getUnitUser = () => http('get', '/api/system/selectThisUnit');

// 查询机构信息详情
org.getCscpOrgs = id => http('get', '/api/system/cscpOrgs/' + id);

// 多个查询机构信息详情
org.batchCscpOrgs = id => http('get', '/api/system/batch/cscpOrgs?ids=' + id);

// 删除机构(机构ID)
org.deleteCscpOrgs = id => http('delete', '/api/system/cscpOrgs/' + id);

// 软删除(机构ID)
org.deleteCscpOrgsRemove = data => http('post', '/api/system/orgAudit/add', data);

// 查询所有的组织机构和相关的工作组、人员
org.cscpOrgsAll = data => http('get', '/api/system/cscpOrgs/all', data);

// 获取删除列表
org.selectDeletedList = data => http('get', '/api/system/selectDeletedList', data);

//  删除数据
org.deleteRealById = data => http('get', '/api/system/deleteRealById', data);

// 新增组织机构相关信息
org.cscpOrgsSave = data => http('post', '/api/system/cscpOrgs/save', data);

// 只查询已经勾选的节点树
org.listCheckedOrgNodeTree = data => http('post', '/api/system/listCheckedOrgNodeTree', data);

// 查询所有机构信息, 不需要勾选已经选择的节点
org.selectOrgList = data => http('get', '/api/system/selectOrgList', data);

// 查询所有机构信息, 勾选已经选择的节点
org.selectOrgListCheckedNode = (data, params) =>
  http('post', '/api/system/selectOrgListCheckedNode', data, params);

// 查询指定机构下的所有用户
org.selectUserList = id => http('get', '/api/system/selectUserList/' + id);

// 分页查询指定机构下的所有用户，部门信息1
org.pageQueryUserList = (id, data = {}) =>
  http(
    'get',
    '/api/system/pageQueryUserList/' + id,
    Object.assign(
      {},
      {
        currentPage: 1,
        pageSize: 10000
      },
      data
    )
  );

// 分页查询指定机构下的所有用户，部门信息2
org.pageSelectUsers = (id, data) => http('get', '/api/system/pageSelectUsers/' + id, data);

// 更新机构(只更新机构信息)
org.updateCscpOrg = data => http('put', '/api/system/updateCscpOrg', data);

// 获取地区信息
org.Listlan = data => http('get', '/api/SysOrgAddr/Listlan', data);

// 查询单位下的所有用户
org.selectUserCompanyList = data => http('get', '/api/system/selectUserCompanyList', data);

// 查询所有机构信息, 不需要勾选已经选择的节点，带用户名搜索
org.selectOrgAndQueryRealNameList = data =>
  http('get', '/api/system/selectOrgAndQueryRealNameList', data);

// 查询指定部门下的分管领导和其他领导
org.selectLeader = id => http('get', `/api/system/selectBranchLeaderAndDepartmentHead/${id}`);

// 设置单个部门领导
org.saveDepartmentHead = data =>
  http('post', `/api/system/saveDepartmentHead?orgId=${data.orgId}&userId=${data.userId}`);

// 取消单个部门领导
org.deleteDepartmentHead = data =>
  http('post', `/api/system/deleteDepartmentHead?orgId=${data.orgId}&userId=${data.userId}`);

// 移除用户单位关系
org.deleteUserOrg = data =>
  http('post', `/api/system/deleteUserOrg?orgId=${data.orgId}&userId=${data.userId}`);

// 获取当前登录人账号所在单位
org.getCurrentOrg = data => http('get', `/api/system/getCurrentOrg`);

// 查询该机构下所有用户信息
org.queryCscpUserByOrdIdsListIds = data =>
  http('post', `/api/system/queryCscpUserByOrdIdsListIds`, data);

// 查询其他单位组下所有用户信息
org.queryCscpUserByCscpGroupListIds = data =>
  http('get', '/api/cscpProcUser/queryCscpUserByCscpGroupListIds', data);

// 查询机构信息, 勾选已经选择的节点
org.queryOrgListCheckedNode = (data, params) =>
  http('post', '/api/system/selectOrgListCheckedNode', data, params);

// 根据id查询下级机构,id为0时查询所有顶层单位
org.selectOrgById = data => http('get', '/api/system/selectOrgById', data);

// 区划用户管理 查询部门
org.selectDivisionOrgByIdName = data => http('get', '/api/system/selectDivisionOrgByIdName', data);
// 根据查询市级机构
org.selectOrgCityState = data => http('get', '/api/system/selectOrgCityState', data);

// 获取当前登录人账号的二级单位
org.getCurrentParentOrg = data => http('get', '/api/system/getCurrentParentOrg', data);

// 根据id查询区划下级机构,id为0时查询所有顶层单位
org.selectRegionOrgById = data => http('get', '/api/system/selectDivisionOrgById', data);

// 根据关键字查询机构
org.getOrgHierarchyByName = orgName =>
  http('get', `/api/system/getOrgHierarchyByName?orgName=${orgName}`);

// 根据关键字查询机构
org.syncOrgBusiness = data => http('post', `/api/tSyncAppSystemManage/syncOrgBusiness`, data);

// new根据关键字查询机构
org.getOrgHierarchyByNameNewest = (orgName = '', orgId = '') =>
  http('get', `/api/system/getOrgHierarchyByNameNewest?orgName=${orgName}&orgId=${orgId}`);

// 区划用户管理查询列表
org.selectRemoveOrgList = data => http('post', '/api/system/orgAudit/selectRemoveOrgList', data);

// 审核保存
org.selectRemoveOrgAudit = data => http('put', '/api/system/orgAudit/audit', data);

// 批量更新机构排序
org.updateOrgOrderBy = data => http('post', `/api/system/updateOrgOrderBy`, data);

// 根据父id查询到下面所有子节点
org.selectAllChildNodesListById = data =>
  http('post', `/api/system/selectAllChildNodesListById`, data);

// 机构管理 查询成员 拖动排序
org.userOrderBy = data => http('put', `/api/system/cscpOrg/userOrderBy`, data);

// 社会信用号查询
org.selectOrgByCreditCode = creditCode =>
  http('get', `/api/system/selectOrgByCreditCode/${creditCode}`);

// 修改父级机构（只是修改层级）
org.updateParentOrg = data => http('post', `/api/system/updateOrgParentId`, data);
export default org;
