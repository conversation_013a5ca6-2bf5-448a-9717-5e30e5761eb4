user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    ###以下为基线整改
    limit_conn_zone $server_name zone=perserver:10m;   ###限速设置
    server_tokens off;   ###版本号限制
    client_body_timeout 60;  ###HTTP限制
    client_header_timeout 60;
    send_timeout 60;
    gzip  on;  ####开启压缩

    error_log  /var/log/nginx/error.log warn;


    log_format main '{ "time_local": "$time_local", '
                              '"time": "$time_iso8601", '
                               '"remote_addr": "$remote_addr", '
                               '"remote_user": "$remote_user", '
                               '"body_bytes_sent": "$body_bytes_sent", '
                               '"request_time": "$request_time", '
                               '"up_resp_time": "$upstream_response_time",'
                               '"status": "$status", '
                               '"host": "$host", '
                               '"request": "$request", '
                               '"request_method": "$request_method", '
                               '"uri": "$uri", '
                               '"http_referrer": "$http_referer", '
                               '"bytes_sent": "$bytes_sent",'
                               '"content_length": "$content_length",'
                               '"request_length": "$request_length",'
                               '"http_x_forwarded_for": "$http_x_forwarded_for", '
                               '"http_user_agent": "$http_user_agent" '
                                '}';

    #access_log  logs/access.log  main;
#####################


    # 请求报文大小
    client_max_body_size 100M;
    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;
    # 压缩指定类型，类型需要对应mine.types文件中定义的MIME Type
    gzip_types font/ttf font/opentype font/x-woff text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png image/jpg image/svg;
    #如果文件大于1k就启动压缩
    gzip_min_length 1k;
    include /etc/nginx/conf.d/*.conf;

}
