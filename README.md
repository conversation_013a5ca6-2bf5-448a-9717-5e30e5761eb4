## Welcome to WPS OAAssist Demo

### 比翼 OA 工程结构

```sh
├── public                   # 静态资源(直接复制，不会经过webpack的打包处理)
├── src                      # 项目根目录
│   ├── api                  # 项目api
│   ├── assets               # 静态资源(会经过webpack的打包处理)
│   ├── i18n                 # 国际化
│   ├── i18n                 # 国际化
│   ├── libs                 # 工具箱
│   │   └── wps              # wps
│   ├── router               # 路由管理
│   ├── store                # vuex状态管理
│   ├── theme                # 主题
│   ├── utils                # 常用工具类
│   └── views                # 页面目录
│       ├── biyi-admin       # 比翼admin
│       │   └── src          # 比翼admin根目录
│       │       ├── components # 比翼admin组件目录
│       │       ├── mixins   # 比翼admin mixins
│       │       ├── styles   # 比翼admin styles
│       │       └── system-page  # 比翼admin system页
│       ├── biyi-workflow    # 比翼工作流
│       │   ├── app          # 比翼工作流App h5页面
│       │   ├── form         # 比翼工作流表单设计目录
│       │   │   ├── formDesign   # 比翼工作流表单设计目录
│       │   │   │   ├── component # 比翼工作流表单设计组件目录
│       │   │   │   │   ├── design # 比翼工作流表单设计目录
│       │   │   │   │   │   ├── byBaseEventProp.vue # 基本事件属性组件
│       │   │   │   │   │   ├── byBaseNormalProp.vue # 基本常用属性数据组件
│       │   │   │   │   │   ├── byBaseOptionProp.vue # 基本常用属性数据组件
│       │   │   │   │   │   ├── byDataSourceMultiple.vue # checkbox基本数据源属性
│       │   │   │   │   │   ├── byDataSourceSimple.vue # radio基本数据源属性
│       │   │   │   │   │   ├── byDesignDynamicNormal.vue # 动态基础组件 ？
│       │   │   │   │   │   ├── byDesignNormal.vue # 基础组件
│       │   │   │   │   │   ├── byDesignTableNormal.vue # 表格表单组件
│       │   │   │   │   │   ├── byFormProp.vue # 表单属性组件
│       │   │   │   │   │   ├── byItemProp.vue # 表单属性组件
│       │   │   │   │   ├── js
│       │   │   │   │   │   ├── byFormData.js
│       │   │   │   │   │   ├── renderDynamicMixin.js
│       │   │   │   │   │   ├── renderMixin.js
│       │   │   │   │   │   ├── renderOptionMixin.js
│       │   │   │   │   │   ├── tool.js
│       │   │   │   │   ├── printer
│       │   │   │   │   │   ├── byPrinterDynamicNormal.vue
│       │   │   │   │   │   ├── byRenderNormal.vue
│       │   │   │   │   ├── render
│       │   │   │   │   │   ├── byRenderDynamicNormal.vue
│       │   │   │   │   │   ├── byPrinterNormal.vue
│       │   │   │   │   └── style # styles
│       │   │   │   │   ├── image # 图片文件
│       │   │   │   ├── container # 设计容器目录
│       │   │   │   │   ├── design # 设计主体
│       │   │   │   │   │   ├── byMainDesign.vue # 低代码编辑入口
│       │   │   │   │   │   ├── byPanelDesign.vue # 低代码设计模块组件
│       │   │   │   │   │   ├── byPanelDesignApp.vue # 低代码设计APP端模块组件
│       │   │   │   │   │   ├── byPropDesign.vue # 低代码设计右侧基础组件属性、事件等组件
│       │   │   │   │   │   ├── bySilderDesign.vue  # 低代码设计左侧基础组件选择块
│       │   │   │   │   ├── printer
│       │   │   │   │   │   ├── byFormPrinter.vue # 表单打印组件
│       │   │   │   │   └── render
│       │   │   │   │   │   ├── appFormRender.vue # APP 表单渲染组件
│       │   │   │   │   │   ├── byFormRender.vue #  表单渲染组件
│       │   │   │   └── plugin          # 基础组件目录
│       │   │   │       ├── layout      # 布局组件目录
│       │   │   │       │   ├── card    # 卡片布局组件
│       │   │   │       │   ├── dynamicRow # 动态行布局组件
│       │   │   │       │   ├── grid    # 栅栏布局组件
│       │   │   │       │   └── table   # 表格布局组件
│       │   │   │       └── normal      # 基础组件目录
│       │   │   │           ├── cascader # 级联选择组件
│       │   │   │           ├── checkUser #选择用户组件
│       │   │   │           ├── checkbox # 多选框组件
│       │   │   │           ├── date     # 日期选择组件
│       │   │   │           ├── documentNum # 公务文号组件
│       │   │   │           ├── html       # html组件
│       │   │   │           ├── inputNumber # 数字输入框组件
│       │   │   │           ├── multipleSelect # 多选下拉框组件
│       │   │   │           ├── radio  # 单选框组
│       │   │   │           ├── rate # 评分组件
│       │   │   │           ├── select # 下拉框组件
│       │   │   │           ├── signature # 签批意见组件
│       │   │   │           ├── switch # 开关组件
│       │   │   │           ├── text   # 单行文本组件
│       │   │   │           ├── textarea # 多行行文本组件
│       │   │   │           ├── time  # 时间选择器组件
│       │   │   │           └── upload  # 上传组件
│       │   │   └── mechanism
│       │   └── src
│       │       └── Conformance
│       │           ├── cloudForm
│       │           │   ├── form
│       │           │   │     ├── form_list.vue   # 表单管理
│       │           │   │     ├── form_sort.vue   # 表单分类管理
│       │           │   └── model  # 表单模型管理
│       │           ├── components # 组件目录
│       │           ├── noWorkflow #非工作流
│       │           └── workflow #工作流
│       │
│       ├── error-pages # error pages
│       ├── hnjcoa    # 湖南集成OA
│       ├── home      # 首页
│       ├── huaihua   # 怀化
│       └── main      # 入口首页
├── .browserslistrc   # 浏览器相关支持情况
├── .dockerignore     # docker 忽略文件
├── .editorconfig     # 编辑器风格配置文件
├── .env.development  # 项目开发环境配置
├── .env.devtest      # 项目开发测试环境配置
├── .env.production   # 项目生产环境配置
├── .env.test         # 项目测试环境配置
├── .eslintrc.js      # eslint 检查规则文件
├── Dockerfile        # docker 配置文件
├── Jenkinsfile       # jenkins 打包配置文件
├── README.md         # README 文件
├── babel.config.js   # babel 配置文件
├── default.conf      # nginx 配置文件
├── index.html.js
├── package-lock.json # 项目依赖锁文件
├── package.json      # 项目基本信息、依赖信息
├── pom.xml           # maven 的配置文件
├── postcss.config.js # postcss 配置文件
└── vue.config.js     # vue.config 配置文件

```

# 好签服务授权地址

```javascript
  // 老版本表单签批管理后台地址
 http://************:28080/sdk/console/index.html

// 账号 admin/Hq20230705sdkc

 // 机器授权码
 NjMwRjAwMDBGREZCOEIwNyxOT1RTUEVDSUZJRUQ=

  // 新版本表单签批和内容签批管理后台地址
 http://************:28080/sdko/console/index.html#/sdk/login

// 账号 admin/Hq20230705sdkc

// 机器授权码
B8E210E9BBA1D10333AAC93A3FD660087E7E101EBA6096812C9E7A3D66A0F66989B690ADE1B22067378952DD94AD80799A10C76392D0DA2F3C4DF41945AA087F86BD52E4023B8F95BF050694DB5656A49346CBEC51C176853F90841BB39A6CCAE3FA379D46BD40FC139A4E6CC295AB5A57D3B547ADB09EBB831F094069A574267B87B7BFAC6A462C2A80FDE93AC95079C9FE55ECCCBCBF50D63E391766D9F65292D992B5F5E28888F175AC4D02F4398DF10E7452E368AB46F17AFFB58AB9DBFA673473533CAF8457FA32FEB670A6BBCA89D3A7D3078CA7FE572558CA3680246C4A0258B15C74160DC757791A8535BBFE1EE6BCEC24D5E4A5A98A51FD6F9748C0C101AB85E9D2064BB84E256CE5CAA418B804CF2227B41EA6195197655E87EFE2549420F7735B798E180C2EF9E32EDADC2A4BD578AFC818EB8102CBA8EB2ADDFEE0B1F181F69D1A9EAFCC3721982C60E76842C6D7BBC6D72953F8354B849A492A64FA026C0CF741479945189F0F5A457C5B6356A05C0EF0DB5AFD5A798F9B482E86C40E7791A1CE5218B69EDBF14ED63F2D95B99A07D6A245


```
