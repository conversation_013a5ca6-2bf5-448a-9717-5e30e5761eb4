@A4TopHead: 120PX;
@formBorderColor: #000;
@formFontColor: #000;
@formFontFamily: 'FZKai-Z03S';
@fontFamilyInput: 'FZKai-Z03S';
@headFontFamily: 'FZKai-Z03S';
@titleSize: 32PX;
@titleFamily: 'FZKai-Z03S';
@titleWeight: 600;
@headSize: 18PX;
@inputSize: 18PX;
@labelSize: 18PX;
@labelWeight: 600;
@inputWeight: "normal";
@titleColor: #000;
@titleMB: 0;
@titleMT: 0;

// .design-form-container{
//   .ivu-form-item-label {
//     color: @formFontColor  !important;
//   }
// }
.render-title,
.design-title {
  font-family: @titleFamily;
  font-weight: @titleWeight;
}

.render-title,
.design-title.ivu-row {
  color: @titleColor;
  font-size: @titleSize;
  margin-bottom: @titleMB;
  margin-top: @titleMT;
}

.design-form-list {
  font-family: @fontFamilyInput;

  .design-view .ivu-form-item-label {
    font-family: @formFontFamily;
    font-size: @labelSize;
    font-weight: @labelWeight;
  }

  .ivu-form-item-content {
    font-size: @inputSize;
    font-weight: @inputWeight;

    .ivu-select,
    .ivu-select-input,
    .ivu-select-item,
    .ivu-input,
    .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
    .ivu-select-single .ivu-select-selection .ivu-select-selected-value,
    .ivu-select-single .ivu-select-selection,
    .ivu-input-number-input-wrap .ivu-input-number-input,
    .ivu-rate,
    .ivu-radio-wrapper,
    .ivu-checkbox-wrapper,
    .ivu-select-multiple .ivu-select-selection .ivu-select-placeholder {
      font-size: inherit;
      font-weight: @inputWeight;
    }
  }
}

.renderPage {
  .ql-container {
    font-size: @inputSize;
    font-family: @fontFamilyInput !important;
    font-weight: 400;
  }

  .ivu-form-item-label {
    color: @formFontColor !important;
    font-family: @formFontFamily;
  }

  .ivu-input[disabled],
  fieldset[disabled] .ivu-input,
  .commentDiv,
  .ivu-input,
  .ivu-select,
  .ivu-select-input,
  .ivu-select-item,
  .ivu-tooltip-rel,
  .ivu-select-disabled .ivu-select-selection,
  .demo-upload-link,
  .printer-content-formItem,
  .ivu-form-item-content {
    color: #000;
    font-size: @inputSize;
    font-family: inherit;
    font-weight: @inputWeight;
  }

  .ivu-form-label-top {
    .render_footer.a4Top {
      .ivu-form-item-label {
        float: left;
      }

      .ivu-form-item {
        margin: 0;
      }

      .ivu-form-item-content {
        margin-left: @A4TopHead;
      }
    }
  }

  .render-content-formItem,
  .printer-content-formItem {
    .ivu-form-item-label {
      font-size: @labelSize;
      font-weight: @labelWeight;
      font-family: @formFontFamily;
    }

    .ivu-form-item-content {
      font-size: @inputSize;
      font-family: @fontFamilyInput;
      font-weight: @inputWeight;
    }
  }

  .ivu-input-number {
    .ivu-input-number-handler {
      font-size: @inputSize;
      font-weight: @inputWeight;

      .ivu-input-number-handler-up-inner,
      .ivu-input-number-handler-down-inner {
        font-size: @inputSize;
        font-weight: @inputWeight;
      }
    }
  }

  .ivu-select-multiple {

    .ivu-select-selection .ivu-select-placeholder,
    .ivu-tag {
      font-size: @inputSize;
      font-weight: @inputWeight;
    }
  }

  .ivu-form-item-content {

    .ivu-select,
    .ivu-select-input,
    .ivu-select-item,
    .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
    .ivu-select-single .ivu-select-selection .ivu-select-selected-value,
    .ivu-select-single .ivu-select-selection,
    .ivu-input-number,
    .ivu-input-number-input-wrap .ivu-input-number-input,
    .ivu-rate {
      font-family: inherit;
      font-size: @inputSize;
      font-weight: @inputWeight;
    }
  }

  .render_footer {
    .ivu-form-item-label {
      color: @formFontColor;
    }
  }

  .form_bottom {
    .ivu-form-item {
      margin: 0;
      border-right: solid 1px @formBorderColor;
      //height: 100%;
    }

    .design-dynamic-border, .design-dynamic-border-table {
      .render-content-formItem .ivu-form-item-content .dyrenderBox .dyform .ivu-form-item {
        border-right: solid 1px @formBorderColor !important;
      }
    }

    border-bottom: solid 1px @formBorderColor;

    .render-content-formItem,
    .printer-content-formItem {
      .ivu-form-item-label {
        font-size: @labelSize;
      }

      .ivu-form-item-content {
        border-left: solid 1px @formBorderColor;

        .ivu-tooltip-rel {
          font-size: inherit;
          line-height: inherit;
        }
      }
    }

    .design-dynamic-border, .design-dynamic-border-table {
      font-family: @fontFamilyInput;
      border-top: solid 1px @formBorderColor;
      border-left: solid 1px @formBorderColor;

      .dyrenderBox {
        border-top: solid 1px @formBorderColor;
        border-left: solid 1px @formBorderColor;

        .ivu-row {
          //border-bottom: solid 1px @formBorderColor;
          border-left: solid 1px @formBorderColor;
        }
      }

      .ivu-form-item-content {
        border-left: solid 1px @formBorderColor;
      }

      .dynamic-num {
        color: @formBorderColor;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: solid 1px @formBorderColor;
        border-right: solid 1px @formBorderColor;

        .ivu-form-item-label {
          font-size: @labelSize;
          font-weight: @labelWeight;
          font-family: @formFontFamily;
        }
      }

      .printer-content-formItem {
        .ivu-form-item-label {
          padding-bottom: 0;
          font-size: @labelSize;
          font-weight: @labelWeight;
          font-family: @formFontFamily;
        }

        .ivu-form-item-content {
          padding-top: 0;
          font-size: @inputSize;
          font-family: @fontFamilyInput;
          font-weight: @inputWeight;
        }
      }
    }

    .design-dynamic-border-table {
      .dyform .ivu-form-item-label {
        //border-bottom: solid 1px @formBorderColor;
      }

      .dyform .ivu-form-item-content {
        border-top: solid 1px @formBorderColor;
      }

      .dyform {
        //border-right: solid 1px @formBorderColor;
      }
      .byGridrow {
        border: none !important;
      }
    }

    .byGridrow {
      border: 1px solid @formBorderColor;
    }
  }

  .tableRender {
    .ivu-form-item-label {
      color: @formFontColor !important;
    }

    .design-dynamic-border-table {
      border-bottom: 1px solid @formBorderColor;
    }
  }
}

.noA4 {

  .render-content-formItem,
  .printer-content-formItem {
    .ivu-form-item-label {
      font-size: @labelSize;
      font-weight: @labelWeight;
      font-family: @formFontFamily;
    }

    .ivu-form-item-content {
      font-size: @inputSize;
      font-family: @fontFamilyInput;
      font-weight: @inputWeight;

      .ivu-select,
      .ivu-select-input,
      .ivu-select-item,
      .ivu-input,
      .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
      .ivu-select-single .ivu-select-selection .ivu-select-selected-value,
      .ivu-select-single .ivu-select-selection,
      .ivu-input-number-input-wrap,
      .ivu-rate,
      .ivu-radio-wrapper,
      .ivu-checkbox-wrapper,
      .ivu-select-multiple .ivu-select-selection .ivu-select-placeholder {
        font-size: inherit;
        font-weight: @inputWeight;
      }
    }
  }
}

.byRenderNormal {
  .ivu-form-item.ivu-form-item-label-position-top, .ivu-form-item.ivu-form-item-label-position-topCenter, .ivu-form-item.ivu-form-item-label-position-topRight {
    .ivu-form-item-label {
      //border-bottom: 1px solid @formBorderColor;
      //width: 100% !important;
      margin-top: -3px;
    }
  }
}

//----------------------------------------页眉页脚----------------------------------------------
.renderPage .renderForm .render_footer .render-content-formItem,
.renderPage .renderForm .render_footer .printer-content-formItem {
  font-size: @headSize;
  font-family: @headFontFamily;

  .ivu-form-item-content,
  .ivu-tooltip-rel,
  .ivu-form-item-content .ivu-input,
  .ivu-form-item-content .ivu-select,
  .ivu-form-item-content .ivu-select-input,
  .ivu-form-item-content .ivu-select-item,
  .ivu-form-item-content .ivu-select-single .ivu-select-selection .ivu-select-placeholder， .ivu-form-item-content .ivu-select-single .ivu-select-selection .ivu-select-selected-value,
  .ivu-form-item-content .ivu-select-single .ivu-select-selection,
  .ivu-form-item-content .ivu-input-number-input-wrap,
  .ivu-form-item-content .ivu-rate,
  .ivu-form-item-content .ivu-radio-wrapper,
  .ivu-form-item-content .ivu-checkbox-wrapper {
    font-size: inherit;
    font-family: inherit;
    padding: 0;
  }
}

.renderPage .renderForm .render_footer .printer-content-formItem .ivu-form-item-content {

  .ivu-form-item-label,
  .ivu-tooltip-rel,
  .ivu-form-item-content .ivu-input,
  .ivu-form-item-content .ivu-select,
  .ivu-form-item-content .ivu-select-input,
  .ivu-form-item-content .ivu-select-item,
  .ivu-form-item-content .ivu-select-single .ivu-select-selection .ivu-select-placeholder， .ivu-form-item-content .ivu-select-single .ivu-select-selection .ivu-select-selected-value,
  .ivu-form-item-content .ivu-select-single .ivu-select-selection,
  .ivu-form-item-content .ivu-input-number-input-wrap,
  .ivu-form-item-content .ivu-rate,
  .ivu-form-item-content .ivu-radio-wrapper,
  .ivu-form-item-content .ivu-checkbox-wrapper {
    font-weight: @inputWeight;
  }
}

.renderPage .renderForm .render_footer .render-content-formItem .ivu-form-item-label,
.renderPage .renderForm .render_footer .printer-content-formItem .ivu-form-item-label {
  padding: 0 !important;
  font-family: @headFontFamily !important;
  font-size: @headSize;
}

.design-form-container .design-footer-list {
  font-size: @headSize !important;
  font-family: @headFontFamily !important;

  .ivu-form-item-label,
  .ivu-form-item-content,
  .ivu-select,
  .ivu-select-input,
  .ivu-select-item,
  .ivu-input,
  .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
  .ivu-select-single .ivu-select-selection .ivu-select-selected-value,
  .ivu-select-single .ivu-select-selection,
  .ivu-input-number-input-wrap,
  .ivu-rate,
  .ivu-radio-wrapper,
  .ivu-checkbox-wrapper {
    font-size: inherit;
    font-family: inherit;
    padding: 0;
  }

  .design-view .ivu-form-item-label {
    font-size: inherit;
    font-family: inherit;
    padding: 0;
  }
}

/*全局的滚动条样式*/
.ps__thumb-y {
  background-color: #e1e1e1 !important;
}

.ps__thumb-x {
  background-color: #e1e1e1 !important;
}

/*全局的tab滚动条样式*/
::-webkit-scrollbar {
  width: 6px; //竖轴宽度
  height: 4px; //横轴宽度
  background-color: #ededed !important;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #e1e1e1 !important;
}

//----------------------------------------页眉页脚结束----------------------------------------------
