/*
 * @Author: srd15623693749 <EMAIL>
 * @Date: 2024-07-02 12:41:16
 * @LastEditors: srd15623693749 <EMAIL>
 * @LastEditTime: 2024-07-02 12:51:13
 * @FilePath: \dist\exec.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
var liveServer = require("live-server");

var params = {
  file: "index.html", 
  proxy: [ ['/api', 'http://**************:31403/api']]
}

liveServer.start(params)
