<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Fluid ComboTree - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Fluid ComboTree</h2>
	<p>This example shows how to set the width of ComboTree to a percentage of its parent container.</p>
	<div style="margin:20px 0"></div>
	<p>width: 50%</p>
	<input class="easyui-combotree" value="122" data-options="url:'tree_data1.json',method:'get'" style="width:50%;">
	<p>width: 30%, height: 26px</p>
	<input class="easyui-combotree" value="124" data-options="url:'tree_data1.json',method:'get'" style="width:30%;height:26px">

</body>
</html>