<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiple ComboTree - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Multiple ComboTree</h2>
	<p>Click the right arrow button to show the tree panel and select multiple nodes.</p>
	<div style="margin:20px 0">
		<span>Cascade Check: </span>
		<input type="checkbox" checked onclick="$('#cc').combotree({cascadeCheck:$(this).is(':checked')})">
	</div>
	<select id="cc" class="easyui-combotree" data-options="url:'tree_data1.json',method:'get'" multiple style="width:200px;"></select>

</body>
</html>