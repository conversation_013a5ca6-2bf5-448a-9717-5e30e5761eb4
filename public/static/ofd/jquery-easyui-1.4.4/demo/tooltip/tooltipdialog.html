<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Tooltip Dialog - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Tooltip Dialog</h2>
	<p>This sample shows how to create a tooltip dialog.</p>
	<div style="margin:20px 0;"></div>
	<div style="padding:10px 200px">
		<p><a id="dd" href="javascript:void(0)">Click here</a> to see the tooltip dialog.
	</div>
	<script>
		$(function(){
			$('#dd').tooltip({
				content: $('<div></div>'),
				showEvent: 'click',
				onUpdate: function(content){
					content.panel({
						width: 200,
						border: false,
						title: 'Login',
						href: '_dialog.html'
					});
				},
				onShow: function(){
					var t = $(this);
					t.tooltip('tip').unbind().bind('mouseenter', function(){
						t.tooltip('show');
					}).bind('mouseleave', function(){
						t.tooltip('hide');
					});
				}
			});
		});
	</script>
</body>
</html>