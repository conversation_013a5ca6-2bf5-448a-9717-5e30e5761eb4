<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Set Value for ComboGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Set Value for ComboGrid</h2>
	<p>Click the buttons below to perform actions.</p>
	<div style="margin:20px 0">
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="getValue()">GetValue</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="setValue1()">SetValue1</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="setValue2()">SetValue2</a>
	</div>
	<input id="cc" class="easyui-combogrid" style="width:250px" data-options="
			panelWidth: 500,
			idField: 'itemid',
			textField: 'productname',
			url: 'datagrid_data1.json',
			method: 'get',
			columns: [[
				{field:'itemid',title:'Item ID',width:80},
				{field:'productname',title:'Product',width:120},
				{field:'listprice',title:'List Price',width:80,align:'right'},
				{field:'unitcost',title:'Unit Cost',width:80,align:'right'},
				{field:'attr1',title:'Attribute',width:200},
				{field:'status',title:'Status',width:60,align:'center'}
			]],
			fitColumns: true
		">
	<script type="text/javascript">
		function getValue(){
			var val = $('#cc').combogrid('getValue');
			alert(val);
		}
		function setValue1(){
			$('#cc').combogrid('setValue', 'EST-13');
		}
		function setValue2(){
			$('#cc').combogrid('setValue', {
				itemid: 'customid',
				productname: 'CustomName'
			});
		}
	</script>
</body>
</html>