<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>LinkButton Size - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>LinkButton Size</h2>
	<p>This sample shows how to display small buttons and large buttons.</p>
	<div style="margin:10px 0 40px 0;"></div>
	<p>Small Buttons</p>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add'">Add</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-remove'">Remove</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'">Save</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cut',disabled:true">Cut</a>
		<a href="#" class="easyui-linkbutton">Text Button</a>
	</div>
	<p>Large Buttons</p>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-large-picture',size:'large',iconAlign:'top'">Picture</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-large-clipart',size:'large',iconAlign:'top'">Clip Art</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-large-shapes',size:'large',iconAlign:'top'">Shapes</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-large-smartart',size:'large',iconAlign:'top'">SmartArt</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-large-chart',size:'large',iconAlign:'top'">Chart</a>
	</div>
	
</body>
</html>