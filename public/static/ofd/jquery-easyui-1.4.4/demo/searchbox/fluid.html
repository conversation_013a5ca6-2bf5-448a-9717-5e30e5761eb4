<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Fluid SearchBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Fluid SearchBox</h2>
	<p>This example shows how to set the width of SearchBox to a percentage of its parent container.</p>
	<div style="margin:20px 0;"></div>
	<p>width: 50%</p>
	<input class="easyui-searchbox" data-options="prompt:'Please Input Value',menu:'#mm'" style="width:50%">
	<p>width: 30%</p>
	<input class="easyui-searchbox" data-options="prompt:'Please Input Value'" style="width:30%">
	<div id="mm">
		<div data-options="name:'all',iconCls:'icon-ok'">All News</div>
		<div data-options="name:'sports'">Sports News</div>
	</div>
</body>
</html>