<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Button Align on FileBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Button Align on FileBox</h2>
	<p>Change the button align to the left or right of filebox.</p>
	<div style="margin:20px 0 40px 0;"></div>
	<span>Select Button Align:</span>
	<select onchange="changeAlign(this.value)">
		<option value="left">Left</option>
		<option value="right" selected>Right</option>
	</select>
	<div style="margin:20px 0;"></div>
	<input id="fb" class="easyui-filebox" style="width:400px" data-options="prompt:'Choose a file...'">
	<script type="text/javascript">
		function changeAlign(align){
			$('#fb').filebox({buttonAlign:align});
		}
	</script>
</body>
</html>