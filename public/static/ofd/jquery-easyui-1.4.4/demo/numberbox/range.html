<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Number Range - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Number Range</h2>
	<p>The value is constrained to a specified range.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:400px;padding:60px 100px;">
		<div style="margin-bottom:20px">
			<div>Amount:</div>
			<input class="easyui-numberbox" data-options="min:10,max:90,precision:2,required:true">
		</div>
		<div style="margin-bottom:20px">
			<div>Weight:</div>
			<input class="easyui-numberbox" data-options="min:10,max:90,required:true">
		</div>
		<div style="margin-bottom:20px">
			<div>Age:</div>
			<input class="easyui-numberbox" data-options="min:0,max:100,required:true">
		</div>
	</div>
</body>
</html>