<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Formatting Tree Nodes - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Formatting Tree Nodes</h2>
	<p>This example shows how to display extra information on nodes.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="padding:5px">
		<ul class="easyui-tree" data-options="
					url:'tree_data1.json',
					method:'get',
					animate:true,
					formatter:function(node){
						var s = node.text;
						if (node.children){
							s += '&nbsp;<span style=\'color:blue\'>(' + node.children.length + ')</span>';
						}
						return s;
					}
				">
		</ul>
	</div>
</body>
</html>