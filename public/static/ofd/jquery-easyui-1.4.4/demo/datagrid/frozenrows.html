<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Frozen Rows in DataGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Frozen Rows in DataGrid</h2>
	<p>This sample shows how to freeze some rows that will always be displayed at the top when the datagrid is scrolled down.</p>
	<div style="margin:20px 0;"></div>
	<table class="easyui-datagrid" title="Frozen Rows in DataGrid" style="width:700px;height:250px"
			data-options="
				singleSelect: true,
				collapsible: true,
				rownumbers: true,
				url: 'datagrid_data1.json',
				method: 'get',
				onLoadSuccess: function(){
					$(this).datagrid('freezeRow',0).datagrid('freezeRow',1);
				}
			">
		<thead data-options="frozen:true">
			<tr>
				<th data-options="field:'itemid',width:100">Item ID</th>
				<th data-options="field:'productid',width:120">Product</th>
			</tr>
		</thead>
		<thead>
			<tr>
				<th data-options="field:'listprice',width:90,align:'right'">List Price</th>
				<th data-options="field:'unitcost',width:90,align:'right'">Unit Cost</th>
				<th data-options="field:'attr1',width:230">Attribute</th>
				<th data-options="field:'status',width:60,align:'center'">Status</th>
			</tr>
		</thead>
	</table>

</body>
</html>