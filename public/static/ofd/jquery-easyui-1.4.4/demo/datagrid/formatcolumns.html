<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Format DataGrid Columns - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Format DataGrid Columns</h2>
	<p>The list price value will show red color when less than 30.</p>
	<div style="margin:20px 0;"></div>
	<table class="easyui-datagrid" title="Format DataGrid Columns" style="width:700px;height:250px"
			data-options="rownumbers:true,singleSelect:true,iconCls:'icon-ok',url:'datagrid_data1.json',method:'get'">
		<thead>
			<tr>
				<th data-options="field:'itemid',width:80">Item ID</th>
				<th data-options="field:'productid',width:100">Product</th>
				<th data-options="field:'listprice',width:80,align:'right',formatter:formatPrice">List Price</th>
				<th data-options="field:'unitcost',width:80,align:'right'">Unit Cost</th>
				<th data-options="field:'attr1',width:240">Attribute</th>
				<th data-options="field:'status',width:60,align:'center'">Status</th>
			</tr>
		</thead>
	</table>
	<script>
		function formatPrice(val,row){
			if (val < 30){
				return '<span style="color:red;">('+val+')</span>';
			} else {
				return val;
			}
		}
	</script>
</body>
</html>