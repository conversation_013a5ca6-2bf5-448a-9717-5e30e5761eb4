<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Frozen Columns in DataGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Frozen Columns in DataGrid</h2>
	<p>You can freeze some columns that can't scroll out of view.</p>
	<div style="margin:20px 0;"></div>
	<table class="easyui-datagrid" title="Frozen Columns in DataGrid" style="width:700px;height:250px"
			data-options="rownumbers:true,singleSelect:true,url:'datagrid_data1.json',method:'get'">
		<thead data-options="frozen:true">
			<tr>
				<th data-options="field:'itemid',width:100">Item ID</th>
				<th data-options="field:'productid',width:120">Product</th>
			</tr>
		</thead>
		<thead>
			<tr>
				<th data-options="field:'listprice',width:90,align:'right'">List Price</th>
				<th data-options="field:'unitcost',width:90,align:'right'">Unit Cost</th>
				<th data-options="field:'attr1',width:250">Attribute</th>
				<th data-options="field:'status',width:60,align:'center'">Status</th>
			</tr>
		</thead>
	</table>

</body>
</html>