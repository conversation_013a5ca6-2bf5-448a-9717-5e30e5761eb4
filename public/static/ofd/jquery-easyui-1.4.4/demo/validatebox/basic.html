<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Basic ValidateBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Basic ValidateBox</h2>
	<p>It's easy to add validate logic to a input box.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" title="Register" style="width:400px;padding:10px 60px 20px 60px">
		<table cellpadding="5">
			<tr>
				<td>User Name:</td>
				<td><input class="easyui-validatebox textbox" data-options="required:true,validType:'length[3,10]'"></td>
			</tr>
			<tr>
				<td>Email:</td>
				<td><input class="easyui-validatebox textbox" data-options="required:true,validType:'email'"></td>
			</tr>
			<tr>
				<td>Birthday:</td>
				<td><input class="easyui-datebox textbox"></td>
			</tr>
			<tr>
				<td>URL:</td>
				<td><input class="easyui-validatebox textbox" data-options="required:true,validType:'url'"></td>
			</tr>
			<tr>
				<td>Phone:</td>
				<td><input class="easyui-validatebox textbox" data-options="required:true"></td>
			</tr>
		</table>
	</div>
	<style scoped="scoped">
		.textbox{
			height:20px;
			margin:0;
			padding:0 2px;
			box-sizing:content-box;
		}
	</style>

</body>
</html>