<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Tabs Strip Tools - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Tabs Strip Tools</h2>
	<p>Click the mini-buttons on the tab strip to perform actions.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-tabs" style="width:700px;height:250px">
		<div title="About" data-options="tools:'#p-tools'" style="padding:10px">
			<p style="font-size:14px">jQuery EasyUI framework helps you build your web pages easily.</p>
			<ul>
				<li>easyui is a collection of user-interface plugin based on jQuery.</li>
				<li>easyui provides essential functionality for building modem, interactive, javascript applications.</li>
				<li>using easyui you don't need to write many javascript code, you usually defines user-interface by writing some HTML markup.</li>
				<li>complete framework for HTML5 web page.</li>
				<li>easyui save your time and scales while developing your products.</li>
				<li>easyui is very easy but powerful.</li>
			</ul>
		</div>
		<div title="Help" data-options="iconCls:'icon-help',closable:true" style="padding:10px">
			This is the help content.
		</div>
	</div>
	<div id="p-tools">
		<a href="javascript:void(0)" class="icon-mini-add" onclick="alert('add')"></a>
		<a href="javascript:void(0)" class="icon-mini-edit" onclick="alert('edit')"></a>
		<a href="javascript:void(0)" class="icon-mini-refresh" onclick="alert('refresh')"></a>
	</div>

</body>
</html>