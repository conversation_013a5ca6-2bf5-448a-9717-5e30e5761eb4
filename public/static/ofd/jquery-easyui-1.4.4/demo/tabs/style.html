<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Tabs Style - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Tabs Style</h2>
	<p>Click the options below to change the tabs style.</p>
	<div style="margin:20px 0;">
		<input id="plain" type="checkbox" onclick="setTabs()"><span>plain</span><br>
		<input id="narrow" type="checkbox" onclick="setTabs()"><span>narrow</span><br>
		<input id="pill" type="checkbox" onclick="setTabs()"><span>pill</span><br>
		<input id="justified" type="checkbox" onclick="setTabs()"><span>justified</span>
	</div>
	<div id="tt" class="easyui-tabs" style="width:700px;height:250px">
		<div title="About" style="padding:10px">
			<p style="font-size:14px">jQuery EasyUI framework helps you build your web pages easily.</p>
			<ul>
				<li>easyui is a collection of user-interface plugin based on jQuery.</li>
				<li>easyui provides essential functionality for building modem, interactive, javascript applications.</li>
				<li>using easyui you don't need to write many javascript code, you usually defines user-interface by writing some HTML markup.</li>
				<li>complete framework for HTML5 web page.</li>
				<li>easyui save your time and scales while developing your products.</li>
				<li>easyui is very easy but powerful.</li>
			</ul>
		</div>
		<div title="My Documents" style="padding:10px">
			<ul class="easyui-tree" data-options="url:'tree_data1.json',method:'get',animate:true"></ul>
		</div>
		<div title="Help" data-options="iconCls:'icon-help',closable:true" style="padding:10px">
			This is the help content.
		</div>
	</div>
	<script type="text/javascript">
		function setTabs(){
			$('#tt').tabs({
				plain: $('#plain').is(':checked'),
				narrow: $('#narrow').is(':checked'),
				pill: $('#pill').is(':checked'),
				justified: $('#justified').is(':checked')
			})
		}
	</script>
</body>
</html>