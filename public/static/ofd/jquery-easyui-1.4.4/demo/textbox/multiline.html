<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiline TextBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Multiline TextBox</h2>
	<p>This example shows how to define a textbox for the user to enter multi-line text input.</p>
	<div style="margin:20px 0;"></div>
	<input class="easyui-textbox" data-options="multiline:true" value="This TextBox will allow the user to enter multiple lines of text." style="width:300px;height:100px">
</body>
</html>