<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TextBox Size - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TextBox Size</h2>
	<p>The textbox can vary in size.</p>
	<div style="margin:20px 0 40px 0;"></div>
	<div style="margin-bottom:40px">
		<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search small...'" style="width:250px;height:22px;">
	</div>
	<div style="margin-bottom:40px">
		<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search large...'" style="width:250px;height:26px;">
	</div>
	<div style="margin-bottom:40px">
		<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search big...'" style="width:250px;height:32px;">
	</div>
	<div style="margin-bottom:40px">
		<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search huge...'" style="width:250px;height:40px;">
	</div>
</body>
</html>