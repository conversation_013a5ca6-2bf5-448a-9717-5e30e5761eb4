<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Custom Pagination Buttons - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Custom Pagination Buttons</h2>
	<p>The customized buttons can be appended to page bar.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel">
		<div class="easyui-pagination" data-options="total:114,buttons:buttons"></div>
	</div>
	<script>
		var buttons = [{
			iconCls:'icon-add',
			handler:function(){
				alert('add');
			}
		},{
			iconCls:'icon-cut',
			handler:function(){
				alert('cut');
			}
		},{
			iconCls:'icon-save',
			handler:function(){
				alert('save');
			}
		}];
	</script>
</body>
</html>