<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Custom Format in ComboBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Custom Format in ComboBox</h2>
	<p>This sample shows how to custom the format of list item.</p>
	<div style="margin:20px 0"></div>
	<input class="easyui-combobox" name="language" data-options="
				url: 'combobox_data1.json',
				method: 'get',
				valueField: 'id',
				textField: 'text',
				panelWidth: 350,
				panelHeight: 'auto',
				formatter: formatItem
			">
	<script type="text/javascript">
		function formatItem(row){
			var s = '<span style="font-weight:bold">' + row.text + '</span><br/>' +
					'<span style="color:#888">' + row.desc + '</span>';
			return s;
		}
	</script>
</body>
</html>