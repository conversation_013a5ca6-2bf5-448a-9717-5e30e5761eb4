<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<title>Basic Form - jQuery EasyUI Mobile Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/mobile.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.mobile.js"></script>
</head>
<body>
	<div class="easyui-navpanel" style="position:relative;padding:20px">
		<header>
			<div class="m-toolbar">
				<div class="m-title">Basic Form</div>
				<div class="m-right">
					<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#ff').form('reset')" style="width:60px">Reset</a>
				</div>
			</div>
		</header>
		<form id="ff">
			<div>
				<label>Full name</label>
				<input class="easyui-textbox" prompt="Full name" style="width:100%">
			</div>
			<div>
				<label>Birthday</label>
				<input class="easyui-datebox" prompt="Birthday" data-options="editable:false,panelWidth:220,panelHeight:240,iconWidth:30" style="width:100%">
			</div>
			<div>
				<label>Password</label>
				<input class="easyui-textbox" type="password" prompt="Password" style="width:100%">
			</div>
			<div>
				<label>Number</label>
				<input class="easyui-numberbox" prompt="Number" style="width:100%">
			</div>
			<div>
				<label>Volumn</label>
				<input class="easyui-slider" value="10" style="width:100%">
			</div>
		</form>
	</div>
	<style scoped>
		form label{
			display: block;
			margin: 10px 0 5px 0;
		}
	</style>
</body>
</html>