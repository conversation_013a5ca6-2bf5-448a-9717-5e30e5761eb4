<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>NumberSpinner - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<div class="m-title">NumberSpinner</div>
			</div>
		</header>
		<ul class="m-list">
			<li><span>Basic Number</span><br><input class="easyui-numberspinner" style="width:100%"></li>
			<li><span>Increment Number</span><br><input class="easyui-numberspinner" value="1000" data-options="increment:100" style="width:100%"></li>
			<li><span>Format Number</span><br><input class="easyui-numberspinner" value="23893" data-options="groupSeparator:',',decimalSeparator:'.',prefix:'$'" style="width:100%"></li>
		</ul>

	</div>
</body>	
</html>
