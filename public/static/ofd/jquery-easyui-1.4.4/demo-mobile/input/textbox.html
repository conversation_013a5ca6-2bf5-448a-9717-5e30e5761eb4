<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TextBox - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<div class="m-title">TextBox</div>
			</div>
		</header>
		<ul class="m-list">
			<li><span>Standard TextBox</span><br><input class="easyui-textbox" prompt="Search..." style="width:100%"></li>
			<li><span>Icon</span><br><input class="easyui-textbox" iconCls="icon-search" style="width:100%"></li>
			<li><span>Button</span><br><input class="easyui-textbox" buttonText="Search" style="width:100%"></li>
		</ul>

	</div>
</body>	
</html>
