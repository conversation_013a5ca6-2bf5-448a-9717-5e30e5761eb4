<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>DataList Selection - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span class="m-title">DataList Selection</span>
			</div>
		</header>
		<div id="dl" data-options="
				fit: true,
				border: false,
				lines: true,
				checkbox: true,
				singleSelect: false
				">
		</div>
	</div>
	<script>
		var data = [
			{"group":"FL-DSH-01","item":"Tailless"},
			{"group":"FL-DSH-01","item":"With tail"},
			{"group":"FL-DSH-02","item":"Adult Female"},
			{"group":"FL-DSH-02","item":"Adult Male"}
		];
		$(function(){
			$('#dl').datalist({
				data: data,
				textField: 'item',
				groupField: 'group'
			})
		})
	</script>
</body>	
</html>
