.combo {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.combo .combo-text {
  font-size: 12px;
  border: 0px;
  margin: 0;
  padding: 0px 2px;
  vertical-align: baseline;
}
.combo-arrow {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.combo-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.combo-panel {
  overflow: auto;
}
.combo-arrow {
  background: url('images/combo_arrow.png') no-repeat center center;
}
.combo-panel {
  background-color: #ffffff;
}
.combo {
  border-color: #95B8E7;
  background-color: #fff;
}
.combo-arrow {
  background-color: #E0ECFF;
}
.combo-arrow-hover {
  background-color: #eaf2ff;
}
.combo-arrow:hover {
  background-color: #eaf2ff;
}
.combo .textbox-icon-disabled:hover {
  cursor: default;
}
.textbox-invalid {
  border-color: #ffa8a8;
  background-color: #fff3f3;
}
