<!--
 * @Author: srd17680162958 <EMAIL>
 * @Date: 2023-03-24 16:06:23
 * @LastEditors: srd17680162958 <EMAIL>
 * @LastEditTime: 2023-09-28 10:19:22
 * @FilePath: /oaweb/public/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
  <meta http-equiv="expires" content="0">
  <meta http-equiv="Cache" content="no-cache">
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>logo.png" />
  <!-- <link rel="stylesheet" href="<%= BASE_URL %>pdf-sign/ws.sdko.p.css"> -->
  <link rel="stylesheet" href="<%= BASE_URL %>iview.css">
  <link rel="stylesheet" href="<%= BASE_URL %>process-sheet-icon/iconfont.css">
  <link rel="stylesheet/less" href="<%= BASE_URL %>form.less">
  <link rel="stylesheet/less" href="<%= BASE_URL %>font/font.css">
  <!--  <link rel="prefetch" href="<%= BASE_URL %>font/FZFSJW.ttf" />-->
  <!--  <link rel="prefetch" href="<%= BASE_URL %>font/FZXBSJW.ttf" />-->
  <!--  <link rel="prefetch" href="<%= BASE_URL %>font/FZKTJW.ttf" />-->
  <!--  <link rel="prefetch" href="<%= BASE_URL %>font/st.ttf" />-->
  <!--  <link rel="prefetch" href="<%= BASE_URL %>font/FZFSGBK.ttf" />-->
  <!--  <link rel="prefetch" href="<%= BASE_URL %>font/FZKTGBK.ttf" />-->
</head>

<body>
  <noscript>
  </noscript>
  <div id="app"></div>
  <script src="<%= BASE_URL %>less.min.js" type="text/javascript"></script>
  <script src="/js/vue.min.js"></script>
   <script src="/js/vue.js"></script>
  <script src="/js/iview.min.js"></script>

  <script src="/js/axios.min.js"></script>
  <script src="/js/vuex.js"></script>
  <script src="/js/jquery.min.js"></script>
  <script src="/js/moment.min.js"></script>
  <script defer src="/js/Sortable.min.js"></script>
  <script defer src="/js/vuedraggable.umd.min.js"></script>

  <script defer src="/js/jQuery.print.min.js"></script>
  <!-- 鼠标悬浮工具包 -->
  <!-- <script defer src="/js/popper.min.js"></script> -->
  <!-- <script defer src="/js/tippy-bundle.umd.js"></script> -->
  <!-- <script defer src="/js/vconsole.min.js"></script> -->

  <script>
    ; (function () {  //自执行函数
      var src = '/js/vconsole.min.js';// VConsole地址
      if (!/debug=true/.test(window.location)) return;  //不满足条件就不执行
      document.write('<scr' + 'ipt src="' + src + '"></scr' + 'ipt > ');  //将VConsole调试的js放到页面中
      document.write('<scr' + 'ipt>var vConsole = new VConsole();</scr' + 'ipt>'); //执行VConsole中的初始化方法
    })();


  </script>

</body>

</html>