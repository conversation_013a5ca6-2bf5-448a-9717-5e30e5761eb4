<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
        <span class="sub-title">彩色字体</span>
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4061036" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe62d;</span>
                <div class="name">icon_cuiban</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe632;</span>
                <div class="name">icon_edit</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe637;</span>
                <div class="name">icon_chuanyue</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe62b;</span>
                <div class="name">icon_shouqi</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe62c;</span>
                <div class="name">icon_quanpin</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe627;</span>
                <div class="name">icon_back</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe628;</span>
                <div class="name">icon_charu</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe62a;</span>
                <div class="name">icon_back_S</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe62e;</span>
                <div class="name">icon_banjie</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe62f;</span>
                <div class="name">icon_jianqian</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe631;</span>
                <div class="name">icon_daoru</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe633;</span>
                <div class="name">icon_fenpin</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe635;</span>
                <div class="name">icon_dayin</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe636;</span>
                <div class="name">icon_history</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe638;</span>
                <div class="name">icon_fujian</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe63b;</span>
                <div class="name">icon_more</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe63c;</span>
                <div class="name">icon_waibufenfa</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe63d;</span>
                <div class="name">icon_fenfa</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe63f;</span>
                <div class="name">icon_message</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe641;</span>
                <div class="name">icon_neibufenfa</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe642;</span>
                <div class="name">icon_qianzhang</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe644;</span>
                <div class="name">icon_yulanzhengwen</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe645;</span>
                <div class="name">icon_yijian</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe646;</span>
                <div class="name">icon_jiaqian</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe647;</span>
                <div class="name">icon_jiasong</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe649;</span>
                <div class="name">icon_jiedong</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe64b;</span>
                <div class="name">icon_read</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe64d;</span>
                <div class="name">icon_yijiao</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe64e;</span>
                <div class="name">icon_shangchuan</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe651;</span>
                <div class="name">icon_save</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe653;</span>
                <div class="name">icon_zuofei</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe654;</span>
                <div class="name">icon_下载</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon stdoa-iconfont">&#xe655;</span>
                <div class="name">icon_taohong</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'stdoa-iconfont';
  src: 
       url('iconfont.woff2?t=1683768946529') format('woff2'),
       url('iconfont.woff?t=1683768946529') format('woff'),
       url('iconfont.ttf?t=1683768946529') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.stdoa-iconfont {
  font-family: "stdoa-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="stdoa-iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"stdoa-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-cuiban-1"></span>
            <div class="name">
              icon_cuiban
            </div>
            <div class="code-name">.stdoa-cuiban-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-bianji-1"></span>
            <div class="name">
              icon_edit
            </div>
            <div class="code-name">.stdoa-bianji-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-chuanyue-2"></span>
            <div class="name">
              icon_chuanyue
            </div>
            <div class="code-name">.stdoa-chuanyue-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-shouqi-1"></span>
            <div class="name">
              icon_shouqi
            </div>
            <div class="code-name">.stdoa-shouqi-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-zhankai-1"></span>
            <div class="name">
              icon_quanpin
            </div>
            <div class="code-name">.stdoa-zhankai-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-back-1"></span>
            <div class="name">
              icon_back
            </div>
            <div class="code-name">.stdoa-back-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-charu-1"></span>
            <div class="name">
              icon_charu
            </div>
            <div class="code-name">.stdoa-charu-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-back-2"></span>
            <div class="name">
              icon_back_S
            </div>
            <div class="code-name">.stdoa-back-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-banjie-1"></span>
            <div class="name">
              icon_banjie
            </div>
            <div class="code-name">.stdoa-banjie-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-jianqian-1"></span>
            <div class="name">
              icon_jianqian
            </div>
            <div class="code-name">.stdoa-jianqian-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-daoru-1"></span>
            <div class="name">
              icon_daoru
            </div>
            <div class="code-name">.stdoa-daoru-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-fenpin-1"></span>
            <div class="name">
              icon_fenpin
            </div>
            <div class="code-name">.stdoa-fenpin-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-dayin-1"></span>
            <div class="name">
              icon_dayin
            </div>
            <div class="code-name">.stdoa-dayin-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-history-1"></span>
            <div class="name">
              icon_history
            </div>
            <div class="code-name">.stdoa-history-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-fujian-1"></span>
            <div class="name">
              icon_fujian
            </div>
            <div class="code-name">.stdoa-fujian-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-fenfa-1"></span>
            <div class="name">
              icon_more
            </div>
            <div class="code-name">.stdoa-fenfa-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-waibufenfa-1"></span>
            <div class="name">
              icon_waibufenfa
            </div>
            <div class="code-name">.stdoa-waibufenfa-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-fenfa-2"></span>
            <div class="name">
              icon_fenfa
            </div>
            <div class="code-name">.stdoa-fenfa-2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-message-1"></span>
            <div class="name">
              icon_message
            </div>
            <div class="code-name">.stdoa-message-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-neibufenfa-1"></span>
            <div class="name">
              icon_neibufenfa
            </div>
            <div class="code-name">.stdoa-neibufenfa-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-qianzhang-1"></span>
            <div class="name">
              icon_qianzhang
            </div>
            <div class="code-name">.stdoa-qianzhang-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-yulanzhengwen-1"></span>
            <div class="name">
              icon_yulanzhengwen
            </div>
            <div class="code-name">.stdoa-yulanzhengwen-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-yijian-1"></span>
            <div class="name">
              icon_yijian
            </div>
            <div class="code-name">.stdoa-yijian-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-jiaqian-1"></span>
            <div class="name">
              icon_jiaqian
            </div>
            <div class="code-name">.stdoa-jiaqian-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-jiasong-1"></span>
            <div class="name">
              icon_jiasong
            </div>
            <div class="code-name">.stdoa-jiasong-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-jiedong-1"></span>
            <div class="name">
              icon_jiedong
            </div>
            <div class="code-name">.stdoa-jiedong-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-chuanyue-1"></span>
            <div class="name">
              icon_read
            </div>
            <div class="code-name">.stdoa-chuanyue-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-yijiao-1"></span>
            <div class="name">
              icon_yijiao
            </div>
            <div class="code-name">.stdoa-yijiao-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-shangchuan-1"></span>
            <div class="name">
              icon_shangchuan
            </div>
            <div class="code-name">.stdoa-shangchuan-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-save-1"></span>
            <div class="name">
              icon_save
            </div>
            <div class="code-name">.stdoa-save-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-zuofei-1"></span>
            <div class="name">
              icon_zuofei
            </div>
            <div class="code-name">.stdoa-zuofei-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-down-1"></span>
            <div class="name">
              icon_下载
            </div>
            <div class="code-name">.stdoa-down-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon stdoa-iconfont stdoa-taohong-1"></span>
            <div class="name">
              icon_taohong
            </div>
            <div class="code-name">.stdoa-taohong-1
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="stdoa-iconfont stdoa-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            stdoa-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-cuiban-1"></use>
                </svg>
                <div class="name">icon_cuiban</div>
                <div class="code-name">#stdoa-cuiban-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-bianji-1"></use>
                </svg>
                <div class="name">icon_edit</div>
                <div class="code-name">#stdoa-bianji-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-chuanyue-2"></use>
                </svg>
                <div class="name">icon_chuanyue</div>
                <div class="code-name">#stdoa-chuanyue-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-shouqi-1"></use>
                </svg>
                <div class="name">icon_shouqi</div>
                <div class="code-name">#stdoa-shouqi-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-zhankai-1"></use>
                </svg>
                <div class="name">icon_quanpin</div>
                <div class="code-name">#stdoa-zhankai-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-back-1"></use>
                </svg>
                <div class="name">icon_back</div>
                <div class="code-name">#stdoa-back-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-charu-1"></use>
                </svg>
                <div class="name">icon_charu</div>
                <div class="code-name">#stdoa-charu-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-back-2"></use>
                </svg>
                <div class="name">icon_back_S</div>
                <div class="code-name">#stdoa-back-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-banjie-1"></use>
                </svg>
                <div class="name">icon_banjie</div>
                <div class="code-name">#stdoa-banjie-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-jianqian-1"></use>
                </svg>
                <div class="name">icon_jianqian</div>
                <div class="code-name">#stdoa-jianqian-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-daoru-1"></use>
                </svg>
                <div class="name">icon_daoru</div>
                <div class="code-name">#stdoa-daoru-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-fenpin-1"></use>
                </svg>
                <div class="name">icon_fenpin</div>
                <div class="code-name">#stdoa-fenpin-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-dayin-1"></use>
                </svg>
                <div class="name">icon_dayin</div>
                <div class="code-name">#stdoa-dayin-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-history-1"></use>
                </svg>
                <div class="name">icon_history</div>
                <div class="code-name">#stdoa-history-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-fujian-1"></use>
                </svg>
                <div class="name">icon_fujian</div>
                <div class="code-name">#stdoa-fujian-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-fenfa-1"></use>
                </svg>
                <div class="name">icon_more</div>
                <div class="code-name">#stdoa-fenfa-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-waibufenfa-1"></use>
                </svg>
                <div class="name">icon_waibufenfa</div>
                <div class="code-name">#stdoa-waibufenfa-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-fenfa-2"></use>
                </svg>
                <div class="name">icon_fenfa</div>
                <div class="code-name">#stdoa-fenfa-2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-message-1"></use>
                </svg>
                <div class="name">icon_message</div>
                <div class="code-name">#stdoa-message-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-neibufenfa-1"></use>
                </svg>
                <div class="name">icon_neibufenfa</div>
                <div class="code-name">#stdoa-neibufenfa-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-qianzhang-1"></use>
                </svg>
                <div class="name">icon_qianzhang</div>
                <div class="code-name">#stdoa-qianzhang-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-yulanzhengwen-1"></use>
                </svg>
                <div class="name">icon_yulanzhengwen</div>
                <div class="code-name">#stdoa-yulanzhengwen-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-yijian-1"></use>
                </svg>
                <div class="name">icon_yijian</div>
                <div class="code-name">#stdoa-yijian-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-jiaqian-1"></use>
                </svg>
                <div class="name">icon_jiaqian</div>
                <div class="code-name">#stdoa-jiaqian-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-jiasong-1"></use>
                </svg>
                <div class="name">icon_jiasong</div>
                <div class="code-name">#stdoa-jiasong-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-jiedong-1"></use>
                </svg>
                <div class="name">icon_jiedong</div>
                <div class="code-name">#stdoa-jiedong-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-chuanyue-1"></use>
                </svg>
                <div class="name">icon_read</div>
                <div class="code-name">#stdoa-chuanyue-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-yijiao-1"></use>
                </svg>
                <div class="name">icon_yijiao</div>
                <div class="code-name">#stdoa-yijiao-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-shangchuan-1"></use>
                </svg>
                <div class="name">icon_shangchuan</div>
                <div class="code-name">#stdoa-shangchuan-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-save-1"></use>
                </svg>
                <div class="name">icon_save</div>
                <div class="code-name">#stdoa-save-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-zuofei-1"></use>
                </svg>
                <div class="name">icon_zuofei</div>
                <div class="code-name">#stdoa-zuofei-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-down-1"></use>
                </svg>
                <div class="name">icon_下载</div>
                <div class="code-name">#stdoa-down-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#stdoa-taohong-1"></use>
                </svg>
                <div class="name">icon_taohong</div>
                <div class="code-name">#stdoa-taohong-1</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
