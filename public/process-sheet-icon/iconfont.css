@font-face {
  font-family: "stdoa-iconfont"; /* Project id 4061036 */
  /* Color fonts */
  src: 
       url('./iconfont.woff2?t=1683768946529') format('woff2'),
       url('./iconfont.woff?t=1683768946529') format('woff'),
       url('./iconfont.ttf?t=1683768946529') format('truetype');
}

.stdoa-iconfont {
  font-family: "stdoa-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.stdoa-cuiban-1:before {
  content: "\e62d";
}

.stdoa-bianji-1:before {
  content: "\e632";
}

.stdoa-chuanyue-2:before {
  content: "\e637";
}

.stdoa-shouqi-1:before {
  content: "\e62b";
}

.stdoa-zhankai-1:before {
  content: "\e62c";
}

.stdoa-back-1:before {
  content: "\e627";
}

.stdoa-charu-1:before {
  content: "\e628";
}

.stdoa-back-2:before {
  content: "\e62a";
}

.stdoa-banjie-1:before {
  content: "\e62e";
}

.stdoa-jianqian-1:before {
  content: "\e62f";
}

.stdoa-daoru-1:before {
  content: "\e631";
}

.stdoa-fenpin-1:before {
  content: "\e633";
}

.stdoa-dayin-1:before {
  content: "\e635";
}

.stdoa-history-1:before {
  content: "\e636";
}

.stdoa-fujian-1:before {
  content: "\e638";
}

.stdoa-fenfa-1:before {
  content: "\e63b";
}

.stdoa-waibufenfa-1:before {
  content: "\e63c";
}

.stdoa-fenfa-2:before {
  content: "\e63d";
}

.stdoa-message-1:before {
  content: "\e63f";
}

.stdoa-neibufenfa-1:before {
  content: "\e641";
}

.stdoa-qianzhang-1:before {
  content: "\e642";
}

.stdoa-yulanzhengwen-1:before {
  content: "\e644";
}

.stdoa-yijian-1:before {
  content: "\e645";
}

.stdoa-jiaqian-1:before {
  content: "\e646";
}

.stdoa-jiasong-1:before {
  content: "\e647";
}

.stdoa-jiedong-1:before {
  content: "\e649";
}

.stdoa-chuanyue-1:before {
  content: "\e64b";
}

.stdoa-yijiao-1:before {
  content: "\e64d";
}

.stdoa-shangchuan-1:before {
  content: "\e64e";
}

.stdoa-save-1:before {
  content: "\e651";
}

.stdoa-zuofei-1:before {
  content: "\e653";
}

.stdoa-down-1:before {
  content: "\e654";
}

.stdoa-taohong-1:before {
  content: "\e655";
}

