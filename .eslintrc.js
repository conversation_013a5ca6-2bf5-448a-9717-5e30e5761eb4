/*
 * @Author: srd17680162958 <EMAIL>
 * @Date: 2023-01-30 09:52:50
 * @LastEditors: srd17680162958 <EMAIL>
 * @LastEditTime: 2023-01-30 09:53:17
 * @FilePath: /new-bi-oa-web/.eslintrc.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    "plugin:vue/essential",
    //'@vue/standard'
  ],
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "vue/no-parsing-error": [2, { "x-invalid-end-tag": false }],
    "vue/multi-word-component-names":"off",
  },
  parserOptions: {
    parser: "@babel/eslint-parser",
  },
};
