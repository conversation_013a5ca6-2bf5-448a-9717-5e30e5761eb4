server {
    listen       80;
    listen  [::]:80;
    server_name  _;
    gzip on;####开启压缩
    gzip_static on;####开启压缩
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 5;#压缩级别大小，最大为9，值越小，压缩后比例越小，CPu处理更快，值越大，消耗CPu比较高
    # gzip_types text/plain application/x - javescript text/css application/xm1;
    gzip_vary on;
    gzip_types text/plain application/x-javascript application/javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    #location ~ .*\.(?:htm|html)$ {
      # 由于服务器部署多套项目环境，所以配置具体的项目目录。
      #root    /usr/share/nginx/html
      # 缓存设置 -1为永不缓存
      #expires      -1;
      # 添加返回头字段，设置HTTP请求头
      #add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
    #}
     location /introduce/ {
        root   /usr/share/nginx/html;
        index  introduce/index.html introduce/index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /introducepdf/ {
        root   /usr/share/nginx/html;
        index  introduce/1.pdf;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        # 10环境
        proxy_pass http://oa-web-api-jigou:9003;
        # 134/59环境
        # proxy_pass http://oa-web-api:9003;
        proxy_http_version 1.1;
    }
    ###以下为极限整改
    location /jixian {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
	    limit_rate 10000M;   ####限速设置
	    limit_conn perserver 10000;
#        allow all;   ###allow错误
        deny all;
    }

    error_page  400 404 413           /404.html;

############

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}