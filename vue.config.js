/*
 * @Author: srd17680162958 <EMAIL>
 * @Date: 2022-11-18 10:06:57
 * @LastEditors: srd17680162958 <EMAIL>
 * @LastEditTime: 2024-08-22 16:31:13
 * @FilePath: /shaoshan/vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const {defineConfig} = require("@vue/cli-service");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const path = require("path");
// const UnusedFilesW5WebpackPlugin = require('useless-files-w5-webpack-plugin');
const Webpack = require('webpack');

const CopySourceMapPlugin = require('./copySourceMapPlugin');
const fs = require('fs');



function resolve(dir) {
  return path.join(__dirname, dir);
}
const TimeStamp = new Date().getTime();

module.exports = defineConfig({
  parallel: false,
  publicPath: "/",
  devServer: {
    port: 8099, // 端口
    compress: true, // 是否启动压缩 gzip
    hot: true,
    open: true,
      proxy: {
        //  "/api/whitelist": {
        //          // 代理地址
        // target: `http://**********:31583`, // 需要代理的地址
        // changeOrigin: true, // 是否跨域
        // secure: false, // 如果是https接口，需要配置这个参数
        // // pathRewrite: {
        // //   "^/api": "", // 本身的接口地址没有 '/apis' 这种通用前缀，所以要rewrite，如果本身有则去掉
        // // },
        // logLevel: 'debug',
        // disableHostCheck: true,
        // router: (req) => {
        //   let baseApi = /^VUE_APP_BASE_API=(\S*)/m.exec(fs.readFileSync(".env.development", "utf-8"))[1]
        //   return baseApi;
        //     },
        //     },
      "/api": {
        // 代理地址
        target: process.env.VUE_APP_BASE_API, // 需要代理的地址
        changeOrigin: true, // 是否跨域
        secure: false, // 如果是https接口，需要配置这个参数
        // pathRewrite: {
        //   "^/api": "", // 本身的接口地址没有 '/apis' 这种通用前缀，所以要rewrite，如果本身有则去掉
        // },
        logLevel: 'debug',
        disableHostCheck: true,
        router: (req) => {
          let baseApi = /^VUE_APP_BASE_API=(\S*)/m.exec(fs.readFileSync(".env.development", "utf-8"))[1]
          return baseApi;
            },
        // onProxyRes: (proxyRes,req) => {
        //   proxyRes.headers['x-real-ur12'] = fs.readFileSync(process.cwd() + '/target.env', 'utf8');
        // },
        }
        
    },
  },
  lintOnSave: false,
  // transpileDependencies: process.env.NODE_ENV === "production" ? ["*"] : false,
  transpileDependencies: ["*"],
  productionSourceMap: true,
  chainWebpack: (config) => {
    config
      .plugin("ignore")
      .use(
        new Webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn$/)
      );
    config.plugins.delete("prefetch")  //删除预加载 提升首次加载
    config.resolve.alias
      .set("@", resolve("src"))
      .set("@api", resolve("src/api"))
      .set("biyi-ui", resolve("node_modules/biyi-ui"))
      .set("lodash", resolve("node_modules/lodash"));
    // config.plugin("copy").use(require("copy-webpack-plugin"), [
    //   {
    //     patterns: [{ from: "public", to: "./" }],
    //   },
    // ]);
    // config;
    // .plugin("webpackbar")
    // .use(require("webpackbar"))
    // .tap((args) => {
    //   return [...args, { color: "red", name: "比翼OA" }];
    // });
    // 打包分析
    if (process.env.IS_ANALYZE) {
      config
        .plugin("webpack-bundle-analyzer")
        .use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin, [{
          analyzerMode: 'server',       // 默认值：server，共有server，static，json，disabled四种模式
          analyzerHost: '127.0.0.1',    // 默认值：127.0.0.1，在server模式下使用的主机启动HTTP服务器。
          analyzerPort: 'auto',           // 默认值：8888，在server模式下使用的端口号
          reportFilename: "report.html", // 默认值：report.html，在static模式下生成的捆绑报告文件的路径名
          openAnalyzer: true,            // 默认值：true，是否在默认浏览器中自动打开报告
        }]);
    }
    // 开启js、css压缩，生成gz压缩文件
    if (process.env.NODE_ENV === "production") {
      const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg|ttf|otf)$/;
      config.plugin("CompressionPlugin").use("compression-webpack-plugin", [
        {
          filename: `[path][base]${TimeStamp}.gz`,
          algorithm: "gzip",
          test: productionGzipExtensions,
          threshold: 10240, // 只处理比这个值大的资源。按字节计算
          minRatio: 0.8, // 只有压缩率比这个值小的资源才会被处理
        },
      ]);
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          styles: {
            name: "styles",
            test: /\.(s?css|less|sass)$/,
            chunks: "initial",
            priority: 50,
          },
          vendors: {
            name: "chunk-vendors",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            reuseExistingChunk: true,
            chunks: "initial",
          },
          common: {
            name: "chunk-common",
            minChunks: 2, // 拆分前必须共享模块的最小 chunks 数。
            maxInitialRequests: 5, // 打包后的入口文件加载时，还能同时加载js文件的数量（包括入口文件）
            minSize: 0, // 生成 chunk 的最小体积（≈ 20kb)
            priority: 30, // 优化将优先考虑具有更高 priority（优先级）的缓存组
            reuseExistingChunk: true, // 如果当前 chunk 包含已从主 bundle 中拆分出的模块，则它将被重用，而不是生成新的模块
          },
          // design: {
          //   name: "chunk-design",
          //   test: /[\\/]node_modules[\\/]_?view-design(.*)/,
          //   chunks: "all",
          //   priority: 3,
          //   reuseExistingChunk: true,
          // },
        },
      });
      config.plugin('copy-source-map').use(CopySourceMapPlugin);
    }
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
          math: "always",
        },
      },
    },
  },
  configureWebpack: {
    cache: {
      type: 'filesystem',
      allowCollectingMemory: true,
      buildDependencies: {
        config: [__filename]
      },
    },
    module: {
      noParse: /^(lodash|moment)$/,
    },
    externals: {
      vue: "Vue",
      vuex: "Vuex",
      "view-design": "iview",
      iview: "ViewUI",
      jquery: "jQuery",
      axios: "axios",
      vuedraggable: "vuedraggable",
      moment: "moment",
    },
    plugins: [
      new NodePolyfillPlugin(),
      // new UnusedFilesW5WebpackPlugin({
      //   root: ['./src'], // 项目目录
      //   output: './fileList.json', // 输出文件列表
      //   clean: false, // 是否删除文件, 不建议开启，手动删除比较好，防止误删
      //   exclude: ['node_modules']  // 排除文件列表
      // })
    ],
  },
});
