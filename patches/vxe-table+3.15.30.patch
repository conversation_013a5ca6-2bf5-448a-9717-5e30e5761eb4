diff --git a/node_modules/vxe-table/lib/table/src/methods.js b/node_modules/vxe-table/lib/table/src/methods.js
index bcaef51..b2f781f 100644
--- a/node_modules/vxe-table/lib/table/src/methods.js
+++ b/node_modules/vxe-table/lib/table/src/methods.js
@@ -10409,7 +10409,7 @@ const Methods = {
         rowRest.treeLoaded = false;
       });
     }
-    internalData.treeExpandedMaps = {};
+    // internalData.treeExpandedMaps = {};
     if (transform) {
       handleVirtualTreeToList($xeTable);
       $xeTable.handleTableData();
