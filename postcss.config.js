/*
 * @Author: Mico-2023KNEHMM\Administrator hn_xtj<PERSON><EMAIL>
 * @Date: 2023-02-13 09:08:52
 * @LastEditors: srd17680162958 <EMAIL>
 * @LastEditTime: 2023-06-20 09:18:15
 * @FilePath: \shaoshan-web\postcss.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const autoprefixer = require("autoprefixer"); // 导入自动添加前缀的插件

module.exports = {
  plugins: [autoprefixer], // 挂载插件
};
