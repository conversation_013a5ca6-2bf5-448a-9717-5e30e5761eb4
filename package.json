{"name": "biyi-web", "addonType": "wps", "version": "4.5.2", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve --mode development", "self": "vue-cli-service serve --mode self", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8096 vue-cli-service build   --no-module", "build:test": "vue-cli-service build --mode test", "lint": "vue-cli-service lint", "report": "vue-cli-service build --report", "open": "cd dist/&&npx http-server -p3000", "preview": "cd dist && node live-server.js", "zip": "zip -r -X dist-jigou-xxxxv1.zip dist", "postinstall": "patch-package"}, "dependencies": {"@fullcalendar/bootstrap": "^5.11.4", "@fullcalendar/core": "^5.11.4", "@fullcalendar/daygrid": "^5.11.4", "@fullcalendar/interaction": "^5.11.4", "@fullcalendar/list": "^5.11.4", "@fullcalendar/resource-timeline": "^5.11.4", "@fullcalendar/timegrid": "^5.11.4", "@fullcalendar/vue": "^5.11.4", "@oa/sw-workflow": "^0.0.1-beta.175", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.27.2", "babel-plugin-component": "^1.1.1", "biyi-admin": "^3.4.6", "biyi-captcha": "^3.1.0", "biyi-store": "^1.0.0", "biyi-ui": "^1.0.1", "core-js": "^3.8.3", "cropperjs": "^1.5.12", "echarts": "^4.9.0", "element-ui": "^2.15.14", "i": "^0.3.7", "jquery": "^3.6.0", "jsbarcode": "^3.11.6", "jsencrypt": "^3.2.1", "less": "^4.1.3", "moment": "^2.29.4", "node-polyfill-webpack-plugin": "^2.0.0", "npm": "^10.0.0", "perfect-scrollbar": "^1.5.5", "postcss-pxtorem": "^5.0.0", "promise.prototype.finally": "^3.1.4", "qrcodejs2": "^0.0.2", "tippy.js": "^6.3.7", "view-design": "^4.7.0", "vue": "^2.6.14", "vue-codemirror": "^4.0.6", "vue-grid-layout": "^2.4.0", "vue-qr": "^4.0.9", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vue-signature-pad": "^2.0.5", "vue-tree-chart": "^1.2.9", "vuex": "^3.6.2", "vxe-table": "^3.15.29", "xe-utils": "^3.7.4"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-prettier": "^7.1.0", "archiver": "^5.3.1", "babel-plugin-import": "^1.13.6", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less-loader": "^11.1.0", "live-server": "^1.2.2", "os": "^0.1.2", "patch-package": "^8.0.0", "postcss": "^8.2.8", "postcss-loader": "^4.0.4", "sass": "^1.32.0", "sass-loader": "^14.2.1", "thread-loader": "^3.0.4", "useless-files-w5-webpack-plugin": "^2.0.1", "vue-template-compiler": "^2.6.14", "webpack-bundle-analyzer": "^4.8.0", "webpackbar": "^5.0.2"}, "browserslist": {"production": ["> 1%", "Firefox > 38", "chrome > 38", "not dead", "not ie 11"], "development": ["> 1%", "Firefox > 38", "chrome > 38", "not dead", "not ie 11"]}}