/*
 * @Author: sumin <EMAIL>
 * @Date: 2024-07-08 17:48:34
 * @LastEditors: sumin <EMAIL>
 * @LastEditTime: 2024-07-23 15:14:42
 * @FilePath: /biyi-oa-web/babel.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = {
  presets: ["@vue/cli-plugin-babel/preset"],
  plugins: [
    [
      "import",
      {
        libraryName: "vxe-table",
        style: true, // 样式是否也按需加载
      },
    ],
    [
      "component",
      {
        libraryName: "element-ui",
        styleLibraryName: "theme-chalk",
      },
    ]
  ],
};
